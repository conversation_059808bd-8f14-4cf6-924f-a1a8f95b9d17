#!/usr/bin/env python3
"""
Test script to demonstrate token counting functionality in validation API requests.
This script shows how tik<PERSON><PERSON> is used to measure token usage after validation requests.
"""

import asyncio
import json
from datetime import datetime
from app.services.validation_service import ValidationService
from app.utils.token_counter import TokenCounter
from app.models.schemas import Question


async def test_token_counting_demo():
    """Demonstrate token counting functionality."""
    
    print("=" * 60)
    print("TOKEN COUNTING DEMONSTRATION")
    print("=" * 60)
    print()
    
    # Initialize services
    print("1. Initializing services...")
    validation_service = ValidationService()
    token_counter = TokenCounter()
    
    print(f"   Model: {token_counter.model_name}")
    print(f"   Token limit: {token_counter.get_model_token_limit():,}")
    print()
    
    # Create sample data
    print("2. Creating sample validation data...")
    
    # Sample questions
    sample_questions = [
        {"question": "Is the company name spelled correctly in the report?"},
        {"question": "Does the financial data show consistent formatting?"},
        {"question": "Are all required sections present in the XML structure?"},
        {"question": "Is the revenue figure within expected ranges?"},
        {"question": "Does the report comply with regulatory standards?"}
    ]
    
    # Sample XML content (simulating a real report)
    sample_xml = """
    <report>
        <header>
            <company_name>Acme Corporation Ltd</company_name>
            <report_date>2024-12-31</report_date>
            <report_type>Annual Financial Report</report_type>
        </header>
        <financial_data>
            <revenue currency="GBP">5000000</revenue>
            <gross_profit currency="GBP">2000000</gross_profit>
            <net_profit currency="GBP">800000</net_profit>
            <total_assets currency="GBP">10000000</total_assets>
            <total_liabilities currency="GBP">3000000</total_liabilities>
        </financial_data>
        <compliance>
            <regulatory_framework>UK GAAP</regulatory_framework>
            <audit_status>Completed</audit_status>
            <certification>PWC Certified</certification>
        </compliance>
        <notes>
            <note id="1">Revenue includes international sales</note>
            <note id="2">Gross profit margin improved by 5% year-over-year</note>
            <note id="3">All regulatory requirements have been met</note>
        </notes>
    </report>
    """
    
    # Sample RAG data (simulating retrieved examples)
    sample_rag_data = {
        "examples": [
            {"document": "Example validation showing correct company name format"},
            {"document": "Example of proper financial data validation"},
            {"document": "Example of regulatory compliance checking"}
        ],
        "patterns": ["company_name_validation", "financial_consistency", "regulatory_compliance"]
    }
    
    # Sample focus prompt
    focus_prompt = "Focus on financial data accuracy and regulatory compliance"
    
    print(f"   Questions: {len(sample_questions)}")
    print(f"   XML content length: {len(sample_xml):,} characters")
    print(f"   RAG examples: {len(sample_rag_data['examples'])}")
    print()
    
    # Analyze input tokens
    print("3. Analyzing input token usage...")
    input_analysis = token_counter.analyze_validation_request_tokens(
        sample_questions,
        sample_xml,
        sample_rag_data,
        focus_prompt
    )
    
    print("   INPUT TOKEN BREAKDOWN:")
    print(f"   ├─ XML content: {input_analysis['token_breakdown']['xml_content']:,} tokens")
    print(f"   ├─ Questions: {input_analysis['token_breakdown']['questions']:,} tokens")
    print(f"   ├─ RAG data: {input_analysis['token_breakdown']['rag_data']:,} tokens")
    print(f"   ├─ Focus prompt: {input_analysis['token_breakdown']['focus_prompt']:,} tokens")
    print(f"   ├─ System prompt: {input_analysis['token_breakdown']['system_prompt']:,} tokens")
    print(f"   └─ Instructions: {input_analysis['token_breakdown']['instruction_prompt']:,} tokens")
    print()
    print(f"   TOTAL INPUT TOKENS: {input_analysis['total_input_tokens']:,}")
    print(f"   ESTIMATED OUTPUT TOKENS: {input_analysis['estimated_output_tokens']:,}")
    print(f"   ESTIMATED COST: ${input_analysis['estimated_cost']:.6f}")
    print()
    
    # Check token limits
    print("4. Checking token limits...")
    limit_check = token_counter.check_token_limit(input_analysis['total_input_tokens'])
    
    print(f"   Model limit: {limit_check['token_limit']:,} tokens")
    print(f"   Current usage: {limit_check['usage_percentage']:.2f}%")
    print(f"   Within limit: {'✓ YES' if limit_check['within_limit'] else '✗ NO'}")
    print(f"   Tokens remaining: {limit_check['tokens_remaining']:,}")
    print()
    
    # Simulate response analysis
    print("5. Simulating response token analysis...")
    
    # Sample LLM response (what would be returned)
    sample_response = """[
        {
            "rule_number": 1,
            "question": "Is the company name spelled correctly in the report?",
            "summary": "Company name 'Acme Corporation Ltd' is correctly spelled and formatted",
            "confidence_score": 0.95,
            "relevant_sections": ["header/company_name"],
            "status": "approved",
            "reasoning": "Company name matches expected format and contains no spelling errors"
        },
        {
            "rule_number": 2,
            "question": "Does the financial data show consistent formatting?",
            "summary": "All financial figures use consistent GBP currency formatting",
            "confidence_score": 0.92,
            "relevant_sections": ["financial_data"],
            "status": "approved",
            "reasoning": "Currency formatting is consistent across all financial metrics"
        }
    ]"""
    
    # Sample validation results
    sample_results = [
        {
            "question": "Is the company name spelled correctly in the report?",
            "summary": "Company name 'Acme Corporation Ltd' is correctly spelled and formatted",
            "reasoning": "Company name matches expected format and contains no spelling errors",
            "status": "approved"
        },
        {
            "question": "Does the financial data show consistent formatting?",
            "summary": "All financial figures use consistent GBP currency formatting", 
            "reasoning": "Currency formatting is consistent across all financial metrics",
            "status": "approved"
        }
    ]
    
    # Analyze response tokens
    response_analysis = token_counter.analyze_validation_response_tokens(
        sample_response,
        sample_results
    )
    
    print("   RESPONSE TOKEN BREAKDOWN:")
    print(f"   ├─ Raw LLM response: {response_analysis['response_tokens']:,} tokens")
    print(f"   ├─ Processed results: {response_analysis['total_results_tokens']:,} tokens")
    print(f"   └─ Average per result: {response_analysis['average_tokens_per_result']:.1f} tokens")
    print()
    
    # Calculate total cost
    total_input_tokens = input_analysis['total_input_tokens']
    actual_output_tokens = response_analysis['response_tokens']
    
    # Recalculate cost with actual output tokens
    actual_cost = token_counter._calculate_estimated_cost(total_input_tokens, actual_output_tokens)
    
    print("6. Final token usage summary...")
    print(f"   Total input tokens: {total_input_tokens:,}")
    print(f"   Actual output tokens: {actual_output_tokens:,}")
    print(f"   Total tokens used: {total_input_tokens + actual_output_tokens:,}")
    print(f"   Actual cost: ${actual_cost:.6f}")
    print()
    
    # Show cost breakdown by model
    print("7. Cost comparison by model...")
    models_to_test = ["gpt-4o", "gpt-4o-mini", "gpt-4", "gpt-3.5-turbo"]
    
    for model in models_to_test:
        test_counter = TokenCounter()
        test_counter.model_name = model
        cost = test_counter._calculate_estimated_cost(total_input_tokens, actual_output_tokens)
        print(f"   {model:15}: ${cost:.6f}")
    
    print()
    print("=" * 60)
    print("TOKEN COUNTING DEMONSTRATION COMPLETED")
    print("=" * 60)
    print()
    print("Key Benefits:")
    print("• Real-time token usage monitoring")
    print("• Cost estimation before API calls")
    print("• Token limit validation")
    print("• Detailed breakdown by component")
    print("• Model-specific pricing")
    print("• Performance optimization insights")


if __name__ == "__main__":
    asyncio.run(test_token_counting_demo())
