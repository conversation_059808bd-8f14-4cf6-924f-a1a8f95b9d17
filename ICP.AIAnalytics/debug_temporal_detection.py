#!/usr/bin/env python3
"""
Debug which questions are being detected as temporal.
"""

import sys
import os
import asyncio
import re

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService

def is_temporal_validation_question(question: str) -> bool:
    """Check if this is specifically a temporal validation question about registration numbers or expiry dates."""
    question_lower = question.lower()
    # Use word boundaries to avoid false matches (e.g., "amount" containing "date")
    temporal_patterns = [
        r'\bexpiry\b', r'\bexpired\b', r'\bexpire\b', r'\bexpiration\b', 
        r'\bdate\b', r'\btime\b', r'\bperiod\b'
    ]
    registration_patterns = [
        r'\bregistration\b', r'\bnumber\b', r'\breg\b', 
        r'\bcertificate\b', r'\blicense\b', r'\bpermit\b'
    ]

    has_temporal = any(re.search(pattern, question_lower) for pattern in temporal_patterns)
    has_registration = any(re.search(pattern, question_lower) for pattern in registration_patterns)

    return has_temporal or has_registration

async def debug_temporal_detection():
    """Debug which questions are being detected as temporal."""
    print("Debugging Temporal Question Detection")
    print("=" * 50)
    
    try:
        # Load questions from the permanent question bank
        validation_service = ValidationService()
        questions = await validation_service.question_bank_service.get_questions_from_permanent_bank()
        
        print(f"Total questions loaded: {len(questions)}")
        print()
        
        temporal_questions = []
        credit_questions = []
        other_questions = []
        
        for i, question in enumerate(questions, 1):
            question_text = question.question
            is_temporal = is_temporal_validation_question(question_text)
            is_credit = "credit amount" in question_text.lower()
            
            if is_temporal:
                temporal_questions.append((i, question_text))
            elif is_credit:
                credit_questions.append((i, question_text))
            else:
                other_questions.append((i, question_text))
        
        print(f"Temporal questions detected: {len(temporal_questions)}")
        print(f"Credit questions: {len(credit_questions)}")
        print(f"Other questions: {len(other_questions)}")
        print()
        
        if temporal_questions:
            print("TEMPORAL QUESTIONS (causing raw XML usage):")
            print("-" * 50)
            for i, question_text in temporal_questions:
                print(f"{i:2d}. {question_text}")
                
                # Show which pattern matched
                question_lower = question_text.lower()
                temporal_patterns = [
                    r'\bexpiry\b', r'\bexpired\b', r'\bexpire\b', r'\bexpiration\b', 
                    r'\bdate\b', r'\btime\b', r'\bperiod\b'
                ]
                registration_patterns = [
                    r'\bregistration\b', r'\bnumber\b', r'\breg\b', 
                    r'\bcertificate\b', r'\blicense\b', r'\bpermit\b'
                ]
                
                matched_temporal = [p for p in temporal_patterns if re.search(p, question_lower)]
                matched_registration = [p for p in registration_patterns if re.search(p, question_lower)]
                
                if matched_temporal:
                    print(f"    Temporal patterns matched: {matched_temporal}")
                if matched_registration:
                    print(f"    Registration patterns matched: {matched_registration}")
                print()
        
        if credit_questions:
            print("CREDIT QUESTIONS (should NOT be temporal):")
            print("-" * 50)
            for i, question_text in credit_questions:
                is_temporal = is_temporal_validation_question(question_text)
                status = "❌ TEMPORAL" if is_temporal else "✅ NOT TEMPORAL"
                print(f"{i:2d}. {status} - {question_text}")
            print()
        
        # Test specific problematic patterns
        print("TESTING SPECIFIC PATTERNS:")
        print("-" * 30)
        test_questions = [
            "Mark as an issue if a small company is associated with a large credit amount.",
            "Mark as an issue if a large company is associated with a small credit amount.",
            "Check if registration number is expired",
            "Verify the expiry date",
            "Check the date field",
            "Validate the amount field"
        ]
        
        for test_q in test_questions:
            is_temporal = is_temporal_validation_question(test_q)
            status = "❌ TEMPORAL" if is_temporal else "✅ NOT TEMPORAL"
            print(f"{status} - {test_q}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_temporal_detection())
