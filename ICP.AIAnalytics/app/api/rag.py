"""
RAG (Retrieval-Augmented Generation) API endpoints for managing the knowledge base.
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

from app.services.validation_service import ValidationService
from app.models.schemas import ValidationResult

router = APIRouter()

# Initialize validation service for RAG operations
validation_service = ValidationService()


@router.post("/rag/initialize")
async def initialize_rag_knowledge_base(examples: List[Dict[str, Any]]):
    """
    Initialize the RAG knowledge base with correct response examples.
    
    Expected format for examples:
    [
        {
            "id": "example_1",
            "question": "Company name should match requested name",
            "summary": "COMPLIANT: CompanyName 'GenAI25' matches Requested 'GenAI25'",
            "status": "approved",
            "confidence_score": 0.95,
            "relevant_sections": ["Report/HeaderSection/CompanyName"],
            "reasoning": "Direct comparison shows exact match between company and requested names",
            "xml_context": "<CompanyName>GenAI25</CompanyName><Requested>GenAI25</Requested>"
        }
    ]
    """
    try:
        if not examples:
            raise HTTPException(status_code=400, detail="No examples provided")
        
        # Validate example format
        for i, example in enumerate(examples):
            if not isinstance(example, dict):
                raise HTTPException(
                    status_code=400, 
                    detail=f"Example {i} must be a dictionary"
                )
            
            required_fields = ["question", "summary", "status"]
            missing_fields = [field for field in required_fields if field not in example]
            if missing_fields:
                raise HTTPException(
                    status_code=400,
                    detail=f"Example {i} missing required fields: {missing_fields}"
                )
        
        # Initialize RAG knowledge base
        result = await validation_service.initialize_rag_knowledge_base(examples)
        
        if result.get("status") == "error":
            raise HTTPException(status_code=500, detail=result.get("error"))
        
        return {
            "message": "RAG knowledge base initialized successfully",
            "result": result,
            "examples_processed": len(examples),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initializing RAG: {str(e)}")


@router.post("/rag/initialize-from-file")
async def initialize_rag_from_file(file: UploadFile = File(...)):
    """
    Initialize the RAG knowledge base from a JSON file containing correct examples.
    
    File should contain a JSON array of examples in the same format as the /initialize endpoint.
    """
    try:
        if not file.filename.endswith('.json'):
            raise HTTPException(status_code=400, detail="File must be a JSON file")
        
        # Read and parse the file
        content = await file.read()
        try:
            examples = json.loads(content.decode('utf-8'))
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON format: {str(e)}")
        
        if not isinstance(examples, list):
            raise HTTPException(status_code=400, detail="JSON file must contain an array of examples")
        
        # Use the same initialization logic
        result = await validation_service.initialize_rag_knowledge_base(examples)
        
        if result.get("status") == "error":
            raise HTTPException(status_code=500, detail=result.get("error"))
        
        return {
            "message": "RAG knowledge base initialized from file successfully",
            "filename": file.filename,
            "result": result,
            "examples_processed": len(examples),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initializing RAG from file: {str(e)}")


@router.post("/rag/add-examples")
async def add_examples_to_rag(examples: List[Dict[str, Any]]):
    """
    Add additional examples to the existing RAG knowledge base.
    """
    try:
        if not examples:
            raise HTTPException(status_code=400, detail="No examples provided")
        
        # Validate example format (same as initialize)
        for i, example in enumerate(examples):
            if not isinstance(example, dict):
                raise HTTPException(
                    status_code=400, 
                    detail=f"Example {i} must be a dictionary"
                )
            
            required_fields = ["question", "summary", "status"]
            missing_fields = [field for field in required_fields if field not in example]
            if missing_fields:
                raise HTTPException(
                    status_code=400,
                    detail=f"Example {i} missing required fields: {missing_fields}"
                )
        
        # Add to existing knowledge base
        result = await validation_service.rag_service.initialize_knowledge_base(examples)
        
        if result.get("status") == "error":
            raise HTTPException(status_code=500, detail=result.get("error"))
        
        return {
            "message": "Examples added to RAG knowledge base successfully",
            "result": result,
            "examples_added": len(examples),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error adding examples to RAG: {str(e)}")


@router.get("/rag/status")
async def get_rag_status():
    """
    Get the current status of the RAG knowledge base.
    """
    try:
        # Check if RAG is enabled
        rag_enabled = validation_service.rag_service is not None
        
        status_info = {
            "rag_enabled": rag_enabled,
            "timestamp": datetime.now().isoformat()
        }
        
        if rag_enabled:
            # Get collection information
            try:
                examples_info = await validation_service.rag_service.vector_store.get_collection_info(
                    validation_service.rag_service.examples_collection
                )
                patterns_info = await validation_service.rag_service.vector_store.get_collection_info(
                    validation_service.rag_service.patterns_collection
                )
                
                status_info.update({
                    "examples_collection": examples_info,
                    "patterns_collection": patterns_info,
                    "vector_store_mode": validation_service.rag_service.vector_store.mode,
                    "embeddings_enabled": validation_service.rag_service.embeddings is not None
                })
                
            except Exception as e:
                status_info["collection_error"] = str(e)
        
        return status_info
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting RAG status: {str(e)}")


@router.post("/rag/test-retrieval")
async def test_rag_retrieval(query: Dict[str, str]):
    """
    Test RAG retrieval with a sample question and XML content.
    
    Expected format:
    {
        "question": "Company name should match requested name",
        "xml_content": "<CompanyName>Test Corp</CompanyName><Requested>Test Corp</Requested>"
    }
    """
    try:
        question = query.get("question")
        xml_content = query.get("xml_content", "")
        
        if not question:
            raise HTTPException(status_code=400, detail="Question is required")
        
        # Test retrieval
        retrieved_data = await validation_service.rag_service.retrieve_relevant_examples(
            question, xml_content, n_results=5
        )
        
        return {
            "message": "RAG retrieval test completed",
            "query": {
                "question": question,
                "xml_content": xml_content[:200] + "..." if len(xml_content) > 200 else xml_content
            },
            "retrieved_data": retrieved_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error testing RAG retrieval: {str(e)}")


@router.delete("/rag/reset")
async def reset_rag_knowledge_base():
    """
    Reset the RAG knowledge base by clearing all collections.
    Use with caution - this will remove all stored examples and patterns.
    """
    try:
        # Reset collections
        try:
            validation_service.rag_service.vector_store.client.delete_collection(
                validation_service.rag_service.examples_collection
            )
        except:
            pass  # Collection might not exist
        
        try:
            validation_service.rag_service.vector_store.client.delete_collection(
                validation_service.rag_service.patterns_collection
            )
        except:
            pass  # Collection might not exist
        
        return {
            "message": "RAG knowledge base reset successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resetting RAG: {str(e)}")
