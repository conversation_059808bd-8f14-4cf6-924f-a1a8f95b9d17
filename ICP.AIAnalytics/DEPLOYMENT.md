# Docker Deployment Guide

This guide explains how to deploy the XML Report Validation System using Docker.

## Prerequisites

- <PERSON>er and Docker Compose installed
- OpenAI API key
- At least 2GB RAM available

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd ICP.AIAnalytics
```

### 2. Environment Configuration

Create a `.env` file in the project root:

```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your configuration
nano .env
```

Required environment variables:

```env
# OpenAI Configuration (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here

# Application Configuration
DEBUG=False
HOST=0.0.0.0
PORT=8000

# Vector Database Configuration
CHROMADB_MODE=server

# Performance Configuration
DEFAULT_BATCH_SIZE=10
MAX_TOKENS=4000
```

### 3. Build and Run

#### Option A: Using Docker Compose (Recommended)

```bash
# Build and start the application
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the application
docker-compose down
```

#### Option B: Using Docker directly

```bash
# Build the image
docker build -t xml-validation-system .

# Run the container
docker run -d \
  --name xml-validation-api \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your_openai_api_key_here \
  -e DEBUG=False \
  -v xml_validation_data:/app/data \
  xml-validation-system
```

### 4. Verify Deployment

```bash
# Check if the service is running
curl http://localhost:8000/health

# Check container status
docker ps

# View logs
docker logs xml-validation-api
```

## Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `OPENAI_API_KEY` | Required | Your OpenAI API key |
| `OPENAI_MODEL` | gpt-4o-mini | OpenAI model to use |
| `DEBUG` | False | Enable debug mode |
| `CHROMADB_MODE` | embedded | Vector DB mode (embedded/server) |
| `DEFAULT_BATCH_SIZE` | 10 | Questions processed in parallel |
| `MAX_TOKENS` | 4000 | Maximum tokens for AI responses |
| `MAX_FILE_SIZE` | 10485760 | Maximum upload file size (10MB) |

### ChromaDB Server Mode

The system runs ChromaDB in server mode by default:

```bash
# ChromaDB server runs automatically with the application
docker-compose up -d

# ChromaDB will be available at:
# - Internal: chromadb:8000 (within Docker network)
# - External: localhost:8001 (if needed for external access)
```

## Production Deployment

### 1. Security Considerations

- Use a reverse proxy (nginx/traefik) for SSL termination
- Set `DEBUG=False` in production
- Use secrets management for API keys
- Consider using Docker secrets or Kubernetes secrets

### 2. Resource Requirements

- **Minimum**: 1GB RAM, 1 CPU core
- **Recommended**: 2GB RAM, 2 CPU cores
- **Storage**: At least 10GB for data persistence

### 3. Scaling

For high-traffic deployments:

```bash
# Scale the service
docker-compose up -d --scale xml-validation-api=3

# Use a load balancer
# Configure nginx or traefik for load balancing
```

### 4. Monitoring

The container includes health checks:

```bash
# Check health status
docker inspect xml-validation-api | grep Health -A 10

# Monitor resource usage
docker stats xml-validation-api
```

## Troubleshooting

### Common Issues

1. **Container won't start**
   ```bash
   # Check logs
   docker logs xml-validation-api
   
   # Verify environment variables
   docker exec xml-validation-api env | grep OPENAI
   ```

2. **API key issues**
   ```bash
   # Verify API key is set
   docker exec xml-validation-api printenv OPENAI_API_KEY
   ```

3. **Port conflicts**
   ```bash
   # Check if port 8000 is in use
   netstat -tulpn | grep :8000
   
   # Use different port
   docker run -p 8001:8000 xml-validation-system
   ```

4. **Permission issues**
   ```bash
   # Fix data directory permissions
   docker exec xml-validation-api chown -R app:app /app/data
   ```

### Logs and Debugging

```bash
# View real-time logs
docker-compose logs -f xml-validation-api

# Access container shell
docker exec -it xml-validation-api bash

# Check application status
curl http://localhost:8000/health
```

## Data Persistence

The application uses Docker volumes for data persistence:

- `xml_validation_data`: Uploaded files and processed data
- `chromadb_data`: Vector database data (if using server mode)

To backup data:

```bash
# Backup data volume
docker run --rm -v xml_validation_data:/data -v $(pwd):/backup alpine tar czf /backup/xml_validation_backup.tar.gz -C /data .

# Restore data volume
docker run --rm -v xml_validation_data:/data -v $(pwd):/backup alpine tar xzf /backup/xml_validation_backup.tar.gz -C /data
```

## API Documentation

Once deployed, access the API documentation at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- Health Check: http://localhost:8000/health

## Support

For issues and questions:
1. Check the logs: `docker logs xml-validation-api`
2. Verify configuration in `.env` file
3. Test the health endpoint: `curl http://localhost:8000/health`
4. Check resource usage: `docker stats xml-validation-api` 