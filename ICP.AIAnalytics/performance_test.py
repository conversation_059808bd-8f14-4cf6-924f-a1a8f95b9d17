#!/usr/bin/env python3
"""
Performance Test Script for Validation API Optimizations
"""

import asyncio
import time
import json
import aiohttp
from typing import Dict, Any

# Test configuration
API_BASE_URL = "http://localhost:8000"
BEARER_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTczNzgwNzE5Nn0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"  # Replace with valid token

# Test payload
TEST_PAYLOAD = {
    "report_id": "test_report_001",
    "enable_client_filtering": True,
    "order_details_params": {
        "bearer_token": BEARER_TOKEN,
        "order_id": "12345"
    },
    "validation_options": {
        "include_low_confidence": True,
        "min_confidence_threshold": 0.3
    }
}

async def test_validation_performance():
    """Test validation API performance with different optimization settings."""
    
    print("🚀 Starting Validation API Performance Test")
    print("=" * 60)
    
    # Test scenarios
    scenarios = [
        {
            "name": "Baseline (All optimizations enabled)",
            "config": {
                "ENABLE_HOLISTIC_VALIDATION": True,
                "ENABLE_PERFORMANCE_OPTIMIZATIONS": True,
                "SKIP_RAG_FOR_SPEED": False,
                "REDUCE_LLM_CONTEXT": False
            }
        },
        {
            "name": "Speed Mode (Skip RAG)",
            "config": {
                "ENABLE_HOLISTIC_VALIDATION": True,
                "ENABLE_PERFORMANCE_OPTIMIZATIONS": True,
                "SKIP_RAG_FOR_SPEED": True,
                "REDUCE_LLM_CONTEXT": False
            }
        },
        {
            "name": "Ultra Speed Mode (Skip RAG + Reduce Context)",
            "config": {
                "ENABLE_HOLISTIC_VALIDATION": True,
                "ENABLE_PERFORMANCE_OPTIMIZATIONS": True,
                "SKIP_RAG_FOR_SPEED": True,
                "REDUCE_LLM_CONTEXT": True
            }
        }
    ]
    
    results = []
    
    async with aiohttp.ClientSession() as session:
        for scenario in scenarios:
            print(f"\n📊 Testing: {scenario['name']}")
            print("-" * 40)
            
            # Update configuration (if we had an endpoint for this)
            # For now, we'll just test with current settings
            
            # Run validation test
            start_time = time.time()
            
            try:
                async with session.post(
                    f"{API_BASE_URL}/validate",
                    json=TEST_PAYLOAD,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        end_time = time.time()
                        duration = end_time - start_time
                        
                        # Extract metrics
                        total_questions = result.get("total_questions", 0)
                        processed_questions = result.get("processed_questions", 0)
                        skipped_questions = result.get("skipped_questions", 0)
                        
                        scenario_result = {
                            "scenario": scenario["name"],
                            "duration": duration,
                            "total_questions": total_questions,
                            "processed_questions": processed_questions,
                            "skipped_questions": skipped_questions,
                            "questions_per_second": processed_questions / duration if duration > 0 else 0,
                            "status": "success"
                        }
                        
                        print(f"✅ Duration: {duration:.2f} seconds")
                        print(f"📋 Questions: {total_questions} total, {processed_questions} processed, {skipped_questions} skipped")
                        print(f"⚡ Speed: {scenario_result['questions_per_second']:.2f} questions/second")
                        
                    else:
                        print(f"❌ API Error: {response.status}")
                        scenario_result = {
                            "scenario": scenario["name"],
                            "status": "error",
                            "error": f"HTTP {response.status}"
                        }
                        
            except Exception as e:
                print(f"❌ Exception: {e}")
                scenario_result = {
                    "scenario": scenario["name"],
                    "status": "exception",
                    "error": str(e)
                }
            
            results.append(scenario_result)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📈 PERFORMANCE SUMMARY")
    print("=" * 60)
    
    successful_results = [r for r in results if r.get("status") == "success"]
    
    if successful_results:
        print(f"{'Scenario':<35} {'Duration':<12} {'Speed':<15}")
        print("-" * 62)
        
        for result in successful_results:
            duration = result.get("duration", 0)
            speed = result.get("questions_per_second", 0)
            print(f"{result['scenario']:<35} {duration:<12.2f} {speed:<15.2f}")
        
        # Calculate improvements
        if len(successful_results) > 1:
            baseline = successful_results[0]
            print(f"\n🎯 OPTIMIZATION IMPACT:")
            print("-" * 30)
            
            for result in successful_results[1:]:
                improvement = ((baseline["duration"] - result["duration"]) / baseline["duration"]) * 100
                speed_improvement = ((result["questions_per_second"] - baseline["questions_per_second"]) / baseline["questions_per_second"]) * 100
                
                print(f"{result['scenario']}:")
                print(f"  ⏱️  Time reduction: {improvement:.1f}%")
                print(f"  ⚡ Speed increase: {speed_improvement:.1f}%")
    
    else:
        print("❌ No successful test runs to analyze")
    
    return results

async def test_cache_effectiveness():
    """Test cache effectiveness by running the same request multiple times."""
    
    print("\n🔄 Testing Cache Effectiveness")
    print("=" * 40)
    
    async with aiohttp.ClientSession() as session:
        times = []
        
        for i in range(3):
            print(f"Run {i+1}/3...")
            start_time = time.time()
            
            try:
                async with session.post(
                    f"{API_BASE_URL}/validate",
                    json=TEST_PAYLOAD,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status == 200:
                        await response.json()  # Read response
                        end_time = time.time()
                        duration = end_time - start_time
                        times.append(duration)
                        print(f"  ✅ Duration: {duration:.2f} seconds")
                    else:
                        print(f"  ❌ Error: HTTP {response.status}")
                        
            except Exception as e:
                print(f"  ❌ Exception: {e}")
        
        if len(times) >= 2:
            first_run = times[0]
            subsequent_avg = sum(times[1:]) / len(times[1:])
            cache_improvement = ((first_run - subsequent_avg) / first_run) * 100
            
            print(f"\n📊 Cache Analysis:")
            print(f"  First run: {first_run:.2f}s")
            print(f"  Subsequent avg: {subsequent_avg:.2f}s")
            print(f"  Cache benefit: {cache_improvement:.1f}% faster")

if __name__ == "__main__":
    print("🧪 Validation API Performance Test Suite")
    print("Make sure the API server is running on http://localhost:8000")
    print()
    
    try:
        # Run performance tests
        asyncio.run(test_validation_performance())
        
        # Run cache tests
        asyncio.run(test_cache_effectiveness())
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
