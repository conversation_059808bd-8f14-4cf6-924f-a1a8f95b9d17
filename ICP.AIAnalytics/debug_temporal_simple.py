#!/usr/bin/env python3
"""
Simple debug to check temporal detection on known questions.
"""

import re

def is_temporal_validation_question(question: str) -> bool:
    """Check if this is specifically a temporal validation question about registration numbers or expiry dates."""
    question_lower = question.lower()
    # Use word boundaries to avoid false matches (e.g., "amount" containing "date")
    temporal_patterns = [
        r'\bexpiry\b', r'\bexpired\b', r'\bexpire\b', r'\bexpiration\b', 
        r'\bdate\b', r'\btime\b', r'\bperiod\b'
    ]
    registration_patterns = [
        r'\bregistration\b', r'\bnumber\b', r'\breg\b', 
        r'\bcertificate\b', r'\blicense\b', r'\bpermit\b'
    ]

    has_temporal = any(re.search(pattern, question_lower) for pattern in temporal_patterns)
    has_registration = any(re.search(pattern, question_lower) for pattern in registration_patterns)

    return has_temporal or has_registration

# Test questions from the Excel file that might be problematic
test_questions = [
    "Mark as an issue if a small company is associated with a large credit amount.",
    "Mark as an issue if a large company is associated with a small credit amount.",
    "The Max Credit Currency must be either EUR or USD.",
    "The Max Credit Currency should match the company's location currency, i.e., EUR or USD.",
    "If the client name is present in the 'Client Name' field on the Order Details page, the corresponding details must be included in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section.",
    "Check if registration number is expired",
    "Verify the expiry date",
    "Check the date field",
    "Validate the amount field",
    "Update the report with current date",
    "Registration number validation",
    "Certificate expiry check",
    "License period verification"
]

print("Temporal Question Detection Test")
print("=" * 40)

temporal_count = 0
for i, question in enumerate(test_questions, 1):
    is_temporal = is_temporal_validation_question(question)
    status = "❌ TEMPORAL" if is_temporal else "✅ NOT TEMPORAL"
    print(f"{i:2d}. {status} - {question}")
    
    if is_temporal:
        temporal_count += 1
        # Show which pattern matched
        question_lower = question.lower()
        temporal_patterns = [
            r'\bexpiry\b', r'\bexpired\b', r'\bexpire\b', r'\bexpiration\b', 
            r'\bdate\b', r'\btime\b', r'\bperiod\b'
        ]
        registration_patterns = [
            r'\bregistration\b', r'\bnumber\b', r'\breg\b', 
            r'\bcertificate\b', r'\blicense\b', r'\bpermit\b'
        ]
        
        matched_temporal = [p for p in temporal_patterns if re.search(p, question_lower)]
        matched_registration = [p for p in registration_patterns if re.search(p, question_lower)]
        
        if matched_temporal:
            print(f"    Temporal patterns matched: {matched_temporal}")
        if matched_registration:
            print(f"    Registration patterns matched: {matched_registration}")

print()
print(f"Total temporal questions: {temporal_count}")

# Test the specific credit questions
print("\nCredit Questions Analysis:")
print("-" * 30)
credit_questions = [
    "Mark as an issue if a small company is associated with a large credit amount.",
    "Mark as an issue if a large company is associated with a small credit amount."
]

for question in credit_questions:
    is_temporal = is_temporal_validation_question(question)
    status = "❌ TEMPORAL (WRONG!)" if is_temporal else "✅ NOT TEMPORAL (CORRECT)"
    print(f"{status} - {question}")
    
    if is_temporal:
        print("  This is causing the raw XML issue!")
