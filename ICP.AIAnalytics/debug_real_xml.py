#!/usr/bin/env python3
"""
Debug the exact XML content being sent to LLM for real report 1981352.
"""

import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor

async def debug_real_xml():
    """Debug the exact XML content being sent to LLM for real report."""
    print("Debugging Real XML Content for Report 1981352")
    print("=" * 60)
    
    file_processor = FileProcessor()
    validation_service = ValidationService()
    
    try:
        # Get the report data
        report_data = await file_processor.get_processed_report_by_report_id("1981352")
        
        if not report_data:
            print("❌ Report 1981352 not found")
            return
        
        print("✅ Report 1981352 found")
        print(f"Available keys: {list(report_data.keys())}")
        print()
        
        # Get the XML content that would be sent to LLM
        print("Step 1: Getting XML content for LLM...")
        
        # Method 1: Check xml_data
        xml_data = report_data.get('xml_data', {})
        if xml_data:
            print("✅ xml_data exists")
            xml_content_1 = validation_service._dict_to_xml_string(xml_data)
            print(f"xml_data XML length: {len(xml_content_1)} characters")
        else:
            print("❌ xml_data not found")
            xml_content_1 = None
        
        # Method 2: Check xml_structure (fallback)
        xml_structure = report_data.get('xml_structure', {})
        if xml_structure:
            print("✅ xml_structure exists")
            xml_content_2 = validation_service._dict_to_xml_string(xml_structure)
            print(f"xml_structure XML length: {len(xml_content_2)} characters")
        else:
            print("❌ xml_structure not found")
            xml_content_2 = None
        
        # Use the same logic as validation service
        if xml_data:
            full_xml_content = validation_service._dict_to_xml_string(xml_data)
        else:
            full_xml_content = validation_service._dict_to_xml_string(xml_structure)
        
        # Apply optimization
        optimized_xml_content = validation_service._optimize_xml_content_for_speed(full_xml_content)
        
        print(f"\nStep 2: Final XML content analysis:")
        print(f"Full XML length: {len(full_xml_content)} characters")
        print(f"Optimized XML length: {len(optimized_xml_content)} characters")
        print()
        
        print("Step 3: XML Content Preview (first 2000 characters):")
        print("-" * 60)
        print(optimized_xml_content[:2000])
        print("-" * 60)
        print()
        
        # Check for credit-related content
        print("Step 4: Credit Information Search in XML:")
        xml_lower = optimized_xml_content.lower()
        
        has_payments_section = 'paymentssection' in xml_lower or 'payments' in xml_lower
        has_max_credit = 'maxcredit' in xml_lower
        has_credit_currency = 'maxcreditcurrency' in xml_lower or 'creditcurrency' in xml_lower
        has_credit_figure = 'creditfigure' in xml_lower
        has_xpf = 'xpf' in xml_lower
        has_1000000 = '1000000' in optimized_xml_content
        
        print(f"  PaymentsSection present: {'✅' if has_payments_section else '❌'}")
        print(f"  MaxCredit present: {'✅' if has_max_credit else '❌'}")
        print(f"  Credit currency present: {'✅' if has_credit_currency else '❌'}")
        print(f"  CreditFigure present: {'✅' if has_credit_figure else '❌'}")
        print(f"  XPF currency present: {'✅' if has_xpf else '❌'}")
        print(f"  Amount 1000000 present: {'✅' if has_1000000 else '❌'}")
        print()
        
        if not has_max_credit:
            print("❌ ISSUE: MaxCredit not found in XML content!")
            print("This explains why LLM says 'No credit amount present'")
            
            # Search for any credit-related terms
            credit_terms = ['credit', 'amount', 'limit', 'figure', 'maximum']
            found_terms = []
            for term in credit_terms:
                if term in xml_lower:
                    found_terms.append(term)
            
            if found_terms:
                print(f"Found credit-related terms: {found_terms}")
                
                # Show context around credit terms
                for term in found_terms[:3]:  # Show first 3
                    index = xml_lower.find(term)
                    if index != -1:
                        start = max(0, index - 100)
                        end = min(len(optimized_xml_content), index + 100)
                        context = optimized_xml_content[start:end]
                        print(f"\nContext around '{term}':")
                        print(f"...{context}...")
            else:
                print("No credit-related terms found at all!")
        
        # Check for company information
        print("Step 5: Company Information Search in XML:")
        has_company_name = 'companyname' in xml_lower
        has_employees = 'employee' in xml_lower or 'minemployees' in xml_lower
        has_header_section = 'headersection' in xml_lower
        
        print(f"  CompanyName present: {'✅' if has_company_name else '❌'}")
        print(f"  Employee info present: {'✅' if has_employees else '❌'}")
        print(f"  HeaderSection present: {'✅' if has_header_section else '❌'}")
        print()
        
        if not has_employees:
            print("❌ ISSUE: Employee information not found in XML content!")
            print("This explains why LLM can't determine company size")
        
        # Show the complete XML if it's short enough
        if len(optimized_xml_content) < 3000:
            print("Step 6: Complete XML Content:")
            print("=" * 60)
            print(optimized_xml_content)
            print("=" * 60)
        else:
            print(f"Step 6: XML too long ({len(optimized_xml_content)} chars) - showing key sections only")
            
            # Find and show PaymentsSection if it exists
            payments_start = optimized_xml_content.lower().find('<paymentssection')
            if payments_start != -1:
                payments_end = optimized_xml_content.lower().find('</paymentssection>', payments_start)
                if payments_end != -1:
                    payments_section = optimized_xml_content[payments_start:payments_end + 18]
                    print("\nPaymentsSection found:")
                    print("-" * 40)
                    print(payments_section)
                    print("-" * 40)
                else:
                    print("PaymentsSection start found but no end tag")
            else:
                print("❌ PaymentsSection not found in XML")
        
    except Exception as e:
        print(f"❌ Error debugging XML: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_real_xml())
