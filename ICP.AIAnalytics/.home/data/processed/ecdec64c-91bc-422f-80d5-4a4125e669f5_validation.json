{"validation_id": "ecdec64c-91bc-422f-80d5-4a4125e669f5", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181305", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "df5f9ec6-090b-491a-9e6f-9087964b536b", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f190bfa3-481b-4b54-affc-471e5fd1e120", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f70e179d-68b6-4e14-8fb7-4d9578d53332", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'Example Client' present, comments and payment details populated", "confidence_score": 0.95, "relevant_sections": ["OrderDetails/ClientName", "ClientSpecificComments/Comments", "PaymentSection/PaymentDetails"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4a7d1704-1eb8-44c6-b497-98b706a43f4a", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing; Comments and Notes are empty, compliant", "confidence_score": 0.95, "relevant_sections": ["OrderDetails/ClientName", "ClientSpecificCommentsToBeIncludedInTheReport/Comments", "Payment/Notes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "7b1e52f7-f4f4-496e-a3d7-858a252c5a3a", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit 800.00 is less than Total Income 1000.00, compliant", "confidence_score": 0.95, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a98fbba2-ebfb-4b42-96fd-7ba794adcb59", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials in 'Operating Costs', violates Direct Costs rule", "confidence_score": 0.95, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "26d5e814-be0c-41c7-b0ea-96d8aa1c7555", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'RE test 2' does not match requested 'test', comments missing", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "618eb93a-0135-4c33-b5f7-566552dc5db6", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address 'test' matches active address, comments not needed", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address", "Report/SpecialNotes/TextForSpecificClients"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b5d39d1c-2e2a-4464-9162-f0bb9bff79fb", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not in special note, violates rule for EUR, USD, GBP", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/Requested", "Report/RelatedEntitiesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2ef8074e-fa4d-43ed-8059-7bc3b52aed76", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration numbers missing in Special Notes, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/RegistrationNumbers", "Report/SpecialNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9461e225-165b-4b47-b910-4058a6a7bc3d", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in Payments activities special note", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsActivitiesSpecialNote"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "05ba576e-10ba-42b1-9ff2-ba05b17700ba", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/Shareholders", "Report/RelatedEntitiesSection/RelatedEntity/CompanyName"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2b7c302b-748d-4007-a19c-f0e5ddb28527", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements in significant changes; payments lack reference.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges/ChangeClassifications", "Report/Payments/CreditOpinionNotes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c17f70af-b3d9-48c0-bd20-ccf4d10387a1", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29707, Requested 1108. Please try again in 1.63s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "062eb9e2-b535-42af-9fab-ae019ef0c10d", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29623, Requested 958. Please try again in 1.162s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c9d0c48a-b938-4437-a5e0-585cdc1947f0", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section missing; cannot verify sector relevance", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e862572d-e3fe-4c53-b504-b9ccab03d564", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No 'Activities' section found, cannot verify export text compliance", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d6e3a08b-559d-4a39-99ab-21dbc0c0be19", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28407, Requested 2945. Please try again in 2.704s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "7a83dc48-e142-461f-9b09-30abdb4f1446", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28365, Requested 2915. Please try again in 2.56s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "84640ada-62e4-49af-b028-690d3ec4a231", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28499, Requested 2912. Please try again in 2.822s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "007c9544-5c37-4ed9-811c-f2de6148c4f0", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29677, Requested 1695. Please try again in 2.744s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "42cf4071-8c7d-4141-9fb0-e9033deb0d28", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1494. Please try again in 2.988s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f57e8441-9fa2-4643-84a6-76dcb44641f6", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number '345678' expired with no comment, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "99f8d297-b76f-46f4-aa83-0e08f4ab6c04", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2841. Please try again in 5.682s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "8a8ed93e-d5d7-4ab2-91a7-a2a2491ba483", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1064. Please try again in 2.128s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f5fb228b-05aa-4d2e-bb3c-376a40364761", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1071. Please try again in 2.142s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e4849000-cbb9-4f44-81dd-b7647adba88f", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28394, Requested 1620. Please try again in 28ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "98a028f4-25ac-4c5d-b655-e3bc55639206", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2941. Please try again in 5.882s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "2b9ddafe-5b79-4927-bc40-5934b9c494cb", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2990. Please try again in 5.98s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "04ebec95-e093-4778-9409-5798ce219178", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1083. Please try again in 2.166s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "15fd303d-26ea-4bf5-8ea1-0aa8d12a6d38", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanction Search Performed tag missing, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/Sanction"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5732170b-3ef0-4549-b530-b36e4756cdb3", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27943, Requested 2956. Please try again in 1.798s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "********-db58-475b-8f8f-d102f05f824c", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "14668d07-5c52-4d5e-adf7-e9961e7efa1d", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in RelatedEntitiesSection", "confidence_score": 0.95, "relevant_sections": ["Report/RelatedEntitiesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9ca5c454-2179-43ed-b920-a0a92439ebec", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28921, Requested 1232. Please try again in 306ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "0ec78ce5-c9a2-44da-9482-33ec20bee870", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27200, Requested 2909. Please try again in 218ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e5d86e98-32ea-49da-a25f-93bf172e1a42", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28158, Requested 2853. Please try again in 2.022s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "3af318bf-6d6a-4b9e-acc5-a8aadaed5663", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27255, Requested 2901. Please try again in 312ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "6b2c5680-3739-4605-b0a7-197a4391e59d", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27208, Requested 2926. Please try again in 268ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "cc67b005-4fbd-4587-a4b3-0f8d059f8c3a", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27255, Requested 2922. Please try again in 354ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "282b3bfc-5c38-4760-9edc-ed936152d5fb", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status 'Active' complies with known status requirement", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 16:31:35.708112", "processing_time": 117.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}