#!/usr/bin/env python3
"""
Check the actual XML structure of report 1981352.
"""

import sys
import os
import asyncio
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.file_processor import FileProcessor

async def check_real_report():
    """Check the actual XML structure of report 1981352."""
    print("Checking Real Report 1981352")
    print("=" * 50)
    
    file_processor = FileProcessor()
    
    try:
        # Get the report data
        report_data = await file_processor.get_processed_report_by_report_id("1981352")
        
        if not report_data:
            print("❌ Report 1981352 not found")
            return
        
        print("✅ Report 1981352 found")
        print(f"Available keys: {list(report_data.keys())}")
        print()
        
        # Check XML structure
        xml_structure = report_data.get('xml_structure', {})
        if xml_structure:
            print("XML Structure Analysis:")
            print("=" * 30)
            
            def analyze_structure(data, path="", level=0):
                """Recursively analyze XML structure."""
                indent = "  " * level
                
                if isinstance(data, dict):
                    for key, value in data.items():
                        current_path = f"{path}/{key}" if path else key
                        
                        # Look for credit-related fields
                        if any(term in key.lower() for term in ['credit', 'payment', 'financial', 'company', 'employee', 'turnover']):
                            print(f"{indent}🔍 {current_path}: {type(value).__name__}")
                            if isinstance(value, (str, int, float)):
                                print(f"{indent}    Value: {value}")
                            elif isinstance(value, dict) and len(value) < 10:
                                print(f"{indent}    Content: {value}")
                        else:
                            print(f"{indent}{current_path}: {type(value).__name__}")
                        
                        if isinstance(value, dict) and level < 3:  # Limit depth
                            analyze_structure(value, current_path, level + 1)
                        elif isinstance(value, list) and len(value) > 0 and level < 3:
                            print(f"{indent}  [List with {len(value)} items]")
                            if isinstance(value[0], dict):
                                analyze_structure(value[0], f"{current_path}[0]", level + 1)
                
                elif isinstance(data, list):
                    print(f"{indent}[List with {len(data)} items]")
                    if len(data) > 0 and isinstance(data[0], dict):
                        analyze_structure(data[0], f"{path}[0]", level + 1)
            
            analyze_structure(xml_structure)
            print()
            
            # Look specifically for credit information
            print("Credit Information Search:")
            print("=" * 30)
            
            def find_credit_info(data, path=""):
                """Find all credit-related information."""
                credit_info = []
                
                if isinstance(data, dict):
                    for key, value in data.items():
                        current_path = f"{path}/{key}" if path else key
                        
                        # Check if key contains credit-related terms
                        if any(term in key.lower() for term in ['credit', 'max', 'limit', 'amount', 'currency', 'payment']):
                            credit_info.append({
                                'path': current_path,
                                'key': key,
                                'value': value,
                                'type': type(value).__name__
                            })
                        
                        # Recurse into nested structures
                        if isinstance(value, dict):
                            credit_info.extend(find_credit_info(value, current_path))
                        elif isinstance(value, list):
                            for i, item in enumerate(value):
                                if isinstance(item, dict):
                                    credit_info.extend(find_credit_info(item, f"{current_path}[{i}]"))
                
                return credit_info
            
            credit_info = find_credit_info(xml_structure)
            
            if credit_info:
                print("Found credit-related fields:")
                for info in credit_info:
                    print(f"  📍 {info['path']}")
                    print(f"     Key: {info['key']}")
                    print(f"     Value: {info['value']}")
                    print(f"     Type: {info['type']}")
                    print()
            else:
                print("❌ No credit-related fields found in XML structure")
            
            # Look for company information
            print("Company Information Search:")
            print("=" * 30)
            
            def find_company_info(data, path=""):
                """Find all company-related information."""
                company_info = []
                
                if isinstance(data, dict):
                    for key, value in data.items():
                        current_path = f"{path}/{key}" if path else key
                        
                        # Check if key contains company-related terms
                        if any(term in key.lower() for term in ['company', 'employee', 'turnover', 'size', 'name', 'header']):
                            company_info.append({
                                'path': current_path,
                                'key': key,
                                'value': value,
                                'type': type(value).__name__
                            })
                        
                        # Recurse into nested structures
                        if isinstance(value, dict):
                            company_info.extend(find_company_info(value, current_path))
                        elif isinstance(value, list):
                            for i, item in enumerate(value):
                                if isinstance(item, dict):
                                    company_info.extend(find_company_info(item, f"{current_path}[{i}]"))
                
                return company_info
            
            company_info = find_company_info(xml_structure)
            
            if company_info:
                print("Found company-related fields:")
                for info in company_info[:10]:  # Show first 10
                    print(f"  📍 {info['path']}")
                    print(f"     Key: {info['key']}")
                    print(f"     Value: {info['value']}")
                    print(f"     Type: {info['type']}")
                    print()
                if len(company_info) > 10:
                    print(f"  ... and {len(company_info) - 10} more")
            else:
                print("❌ No company-related fields found in XML structure")
        
        else:
            print("❌ No xml_structure found in report data")
        
        # Check if xml_data exists
        xml_data = report_data.get('xml_data')
        if xml_data:
            print("\n✅ xml_data also exists")
        else:
            print("\n❌ xml_data not found (only xml_structure)")
        
    except Exception as e:
        print(f"❌ Error checking report: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(check_real_report())
