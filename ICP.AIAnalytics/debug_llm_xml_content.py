#!/usr/bin/env python3
"""
Debug script to see exactly what XML content the LLM is receiving.
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor
from app.models.schemas import Question

async def debug_llm_xml_content():
    """Debug what XML content the LLM is actually receiving."""
    print("Debugging LLM XML Content")
    print("=" * 40)
    
    report_id = "1984940"
    print(f"Report ID: {report_id}")
    print()
    
    # Initialize services
    validation_service = ValidationService()
    file_processor = FileProcessor()
    
    try:
        print("Step 1: Loading report data...")
        report_data = await file_processor.get_processed_report_by_report_id(report_id)
        
        if not report_data:
            print(f"❌ No report found")
            return
        
        print(f"✅ Report data loaded")
        print(f"Available keys: {list(report_data.keys())}")
        print()
        
        print("Step 2: Creating temporal validation question...")
        temporal_question = Question(
            id="test-temporal",
            question="If any registration number has expired, a comment should be added explaining the expiry",
            category="temporal_validation",
            client_code=None,
            darwin_reference_sections="LegalStatusSection/RegistrationNumbers",
            expected_outcome="approved",
            client_specific_type="All"
        )
        
        print("Step 3: Extracting XML content for LLM...")
        xml_content = await validation_service._extract_xml_content_for_question_with_darwin(report_data, temporal_question)
        
        print(f"XML content length: {len(xml_content)} characters")
        print()
        
        print("Step 4: Searching for registration-related content in LLM XML...")
        xml_lines = xml_content.split('\n')
        registration_lines = []
        
        for i, line in enumerate(xml_lines):
            line_lower = line.lower()
            if any(word in line_lower for word in ['registration', 'expired', 'dateexpired', '322327812993', '12-jan-2025']):
                registration_lines.append((i+1, line.strip()))
        
        if registration_lines:
            print(f"Found {len(registration_lines)} registration-related lines:")
            for line_num, line in registration_lines:
                print(f"  Line {line_num}: {line}")
        else:
            print("❌ No registration-related content found in LLM XML")
        
        print()
        
        print("Step 5: Checking for LegalStatusSection in LLM XML...")
        legal_section_start = xml_content.find('<LegalStatusSection>')
        legal_section_end = xml_content.find('</LegalStatusSection>')
        
        if legal_section_start != -1 and legal_section_end != -1:
            legal_section = xml_content[legal_section_start:legal_section_end + len('</LegalStatusSection>')]
            print(f"✅ Found LegalStatusSection ({len(legal_section)} characters)")
            
            # Check for RegistrationNumbers
            if '<RegistrationNumbers>' in legal_section:
                print("✅ Found RegistrationNumbers in LegalStatusSection")
                
                # Extract RegistrationNumbers section
                reg_start = legal_section.find('<RegistrationNumbers>')
                reg_end = legal_section.find('</RegistrationNumbers>')
                if reg_start != -1 and reg_end != -1:
                    reg_section = legal_section[reg_start:reg_end + len('</RegistrationNumbers>')]
                    print(f"RegistrationNumbers section:")
                    print(reg_section)
                    
                    # Check for specific registration number
                    if '322327812993' in reg_section:
                        print("✅ Found registration number 322327812993")
                    else:
                        print("❌ Registration number 322327812993 NOT found")
                    
                    # Check for expiry date
                    if '12-Jan-2025' in reg_section:
                        print("✅ Found expiry date 12-Jan-2025")
                    else:
                        print("❌ Expiry date 12-Jan-2025 NOT found")
                    
                    # Check for DateExpired tag
                    if '<DateExpired>' in reg_section:
                        print("✅ Found DateExpired tag")
                    else:
                        print("❌ DateExpired tag NOT found")
                        
                else:
                    print("❌ Could not extract RegistrationNumbers section")
            else:
                print("❌ RegistrationNumbers NOT found in LegalStatusSection")
                print("LegalStatusSection content preview:")
                print(legal_section[:500] + "..." if len(legal_section) > 500 else legal_section)
        else:
            print("❌ LegalStatusSection NOT found in LLM XML")
            print("XML content preview (first 1000 chars):")
            print(xml_content[:1000])
        
        print()
        
        print("Step 6: Testing with a simple LLM call...")
        if validation_service.llm:
            try:
                # Create a simple test prompt
                test_prompt = f"""
You are analyzing XML data for registration number expiry validation.
Current date: {validation_service.get_current_date()}

XML Data:
{xml_content}

Question: Does this XML contain any registration numbers? If yes, list them with their expiry dates.
Look specifically in LegalStatusSection/RegistrationNumbers for nested RegistrationNumber elements.

Respond with just the facts you find.
"""
                
                print("Sending test prompt to LLM...")
                response = await validation_service.llm.ainvoke(test_prompt)
                print(f"LLM Response:")
                print(response.content if hasattr(response, 'content') else str(response))
                
            except Exception as e:
                print(f"❌ Error testing LLM: {e}")
        else:
            print("❌ LLM not available")
        
        print()
        print("=" * 40)
        print("Debug completed!")
        
    except Exception as e:
        print(f"❌ Error in debug: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_llm_xml_content())
