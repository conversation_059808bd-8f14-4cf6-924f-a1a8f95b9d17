from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class ValidationStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class FileUploadResponse(BaseModel):
    file_id: str
    filename: str
    file_type: str
    file_size: int
    upload_timestamp: datetime
    status: str = "uploaded"
    source: Optional[str] = "file_upload"  # "file_upload" or "external_api"
    report_id: Optional[str] = None  # For external API reports

class ExternalAPIRequest(BaseModel):
    report_id: str
    bearer_token: str

class OrderDetailsRequest(BaseModel):
    csr_id: str
    copy: str
    version: str
    bearer_token: Optional[str] = None  # Made optional since it will be inherited from parent request

class Question(BaseModel):
    id: str
    question: str
    category: Optional[str] = None
    priority: Optional[str] = "medium"
    expected_format: Optional[str] = None
    client_code: Optional[str] = None  # Added for client-specific filtering
    # New fields for updated Excel format
    client_specific_type: Optional[str] = None  # "Client", "All", or specific client info
    darwin_reference_sections: Optional[str] = None  # Darwin Reference Section(s) 
    expected_outcome: Optional[str] = None  # Expected Outcome
    editor: Optional[str] = None  # Editor field

class QuestionsUploadResponse(BaseModel):
    file_id: str
    filename: str
    total_questions: int
    questions: List[Question]
    upload_timestamp: datetime

class ValidationRequest(BaseModel):
    report_id: str  # Changed from report_file_id to report_id
    validation_options: Optional[Dict[str, Any]] = {}
    enable_client_filtering: Optional[bool] = True  # Enable client-specific validation by default
    order_details_params: Optional[OrderDetailsRequest] = None  # Parameters for order details API
    direct_client_code: Optional[str] = None  # Direct client code for filtering (alternative to order_details_params)
    # New field for combined upload and validation
    bearer_token: Optional[str] = None  # If provided, will upload report from external API first

class ValidationResult(BaseModel):
    question_id: str
    question_number: int
    question: str
    summary: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    relevant_sections: List[str] = []
    status: str
    client_match_status: Optional[str] = None  # "matched", "skipped", "no_client_code"
    reasoning: Optional[str] = None  # Detailed reasoning for the validation result
    cross_references: Optional[List[str]] = []  # Related sections or rules for holistic validation

class TokenAnalysis(BaseModel):
    """Token usage analysis for validation requests"""
    model: str
    total_input_tokens: int
    estimated_output_tokens: int
    actual_output_tokens: Optional[int] = None
    estimated_cost: float
    token_breakdown: Dict[str, int]
    usage_percentage: Optional[float] = None
    within_limit: Optional[bool] = None

class ValidationResponse(BaseModel):
    validation_id: str
    status: ValidationStatus
    questions_file_id: str
    report_id: str  # Changed from report_file_id to report_id
    total_questions: int
    processed_questions: int
    skipped_questions: Optional[int] = 0  # Questions skipped due to client mismatch
    results: List[ValidationResult]
    validation_timestamp: datetime
    processing_time: Optional[float] = None
    client_filtering_enabled: Optional[bool] = False
    order_client_code: Optional[str] = None  # Client code from order details API
    token_analysis: Optional[TokenAnalysis] = None  # Token usage analysis

class XMLValidationOutput(BaseModel):
    """Model for XML formatted validation output"""
    validation_id: str
    timestamp: datetime
    summary: Dict[str, Any]
    questions_and_answers: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class HistoryResponse(BaseModel):
    validations: List[ValidationResponse]
    total_count: int
    page: int
    page_size: int 

class PermanentQuestionBankResponse(BaseModel):
    """Response model for permanent question bank operations"""
    success: bool
    message: str
    file_path: Optional[str] = None
    total_questions: Optional[int] = None
    timestamp: datetime
    permanent_question_bank_enabled: bool = False

class PermanentQuestionBankStatus(BaseModel):
    """Status model for permanent question bank"""
    enabled: bool
    file_path: str
    file_exists: bool
    total_questions: Optional[int] = None
    last_loaded: Optional[datetime] = None
    last_modified: Optional[datetime] = None 