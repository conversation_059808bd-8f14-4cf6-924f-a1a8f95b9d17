[{"validation_id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "128079.47.1", "total_questions": 41, "processed_questions": 41, "skipped_questions": 2, "results": [{"question_id": "b39db6af-83c9-4542-a642-2569145fe094", "question_number": 1, "question": "The Max Credit Currency must be either EUR or USD.", "summary": "Question skipped - Client code mismatch. This question is for client 'TURKEXIM', but the report is not for this client.", "confidence_score": 1, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "063f448f-d7ae-4085-936b-36201e317faf", "question_number": 2, "question": "The Max Credit Currency should match the company’s location currency, i.e., EUR or USD.", "summary": "Validation failed. The Max Credit currency is BHD. The rule specifies the location currency should be considered EUR or USD for this check, which does not match BHD.", "confidence_score": 1, "relevant_sections": ["Report/Payments/OpinionOnMaxCredit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b4dbfe35-280f-490e-ba5c-ea2f59f2d06e", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, the corresponding details must be included in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section.", "summary": "Rule compliant. A 'Client Reference No' is present, and both 'Text for Specific Client' and the 'Payments' section are populated.", "confidence_score": 0.98, "relevant_sections": ["Report/Header/ClientReferenceNo", "Report/SpecialNotes/TextForSpecificClient", "Report/Payments/CreditOpinionNotes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ddda77c1-45e5-4f79-ac22-776c3fcc91d5", "question_number": 4, "question": "If the client name is not present in the 'Client Name' field on the Order Details page, there should be no corresponding details in either the 'Client Specific Comments to be Included in the Report' section or the 'Payment' section.", "summary": "Rule compliant. A 'Client Reference No' is present, so this rule is not applicable.", "confidence_score": 1, "relevant_sections": ["Report/Header/ClientReferenceNo"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "205927b6-f244-4e1d-84f7-008c04ea0d6a", "question_number": 5, "question": "In the financial section, Gross Profit should be less than Total Income.", "summary": "Cannot validate. The financial data does not contain a 'Gross Profit' line item. Only 'Total Income' and 'Total Profit After Tax' are listed.", "confidence_score": 0.9, "relevant_sections": ["Report/Financials/ProfitAndLoss"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f188f0e5-3ebc-45c9-8488-6dc4c50066cb", "question_number": 6, "question": "In the financial info section, expenses for raw materials and supplies should be listed under Direct Costs, not Operating Costs.", "summary": "Cannot validate. The provided financial extract does not break down costs into 'Direct Costs' or 'Operating Costs'.", "confidence_score": 0.9, "relevant_sections": ["Report/Financials/ProfitAndLoss"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "75c60cc2-a25c-457f-a9cc-4c93b1dfb016", "question_number": 7, "question": "The company name provided in the order as \"Requested\" must match the official company name. If it does not match, both the Requested Name and a Client Specific Comment should be included.", "summary": "Rule compliant. The 'Requested Name' matches the official company name used throughout the report.", "confidence_score": 1, "relevant_sections": ["Report/Header/RequestedName"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "26c2d894-3ed6-41f0-95dc-0f3ab532dffc", "question_number": 8, "question": "The address provided by the client should match one in the Address section. If it does not match an active address, a Client Specific Comment should be included.", "summary": "Rule compliant. The address in the header matches the first address listed in the 'Address Details' section.", "confidence_score": 1, "relevant_sections": ["Report/Header/Address", "Report/Address/AddressDetails"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "d569bcbc-e5f6-4522-aead-f447c81a2bb5", "question_number": 9, "question": "If the client requests maximum credit in EUR, USD, or GBP and not TURKEXIM, then the max credit should be noted in the Special Note.", "summary": "Rule compliant. The maximum credit is in BHD, not EUR, USD, or GBP. This rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/Payments/OpinionOnMaxCredit"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b7eee65b-2c3a-4825-a660-8d3f7a2ac305", "question_number": 10, "question": "Registration or license numbers provided by the client should be recorded either in the Legal section or in the Special Note.", "summary": "Rule compliant. Multiple registration numbers (BCCI Membership, VAT, CR Number) are listed in the 'Legal Status/Registration Numbers' section.", "confidence_score": 1, "relevant_sections": ["Report/LegalStatus/RegistrationNumbers"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "84e397ea-cb6d-4def-b115-9193b9284e12", "question_number": 11, "question": "The report should not contain biased or colloquial language.", "summary": "Rule compliant. Report text uses professional language. The phrase 'local suppliers' is considered descriptive, not biased.", "confidence_score": 0.95, "relevant_sections": ["Report/Payments/QualificationNotes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "70029548-2860-4e1b-be64-08e9d46addac", "question_number": 12, "question": "The report should be checked for spelling errors.", "summary": "Cannot validate. Automated spell-checking is outside the scope of this validation. Manual review is required.", "confidence_score": 0.9, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "18798f43-68b8-4197-a69a-3401ca5e3817", "question_number": 13, "question": "If adverse announcements are included in the Significant Changes section, the Payments section should reference these announcements.", "summary": "Rule compliant. The 'Significant Changes' section is blank. This rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/Summary/SignificantChanges"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "cb571969-7e26-4240-93d7-c8c6e6df9cb7", "question_number": 14, "question": "If adverse announcements are included in both the Significant Changes and Payments sections, the Trade Risk Assessment should be marked as High, and the Credit Opinion and Max Credit fields should be left blank, with a comment added in Opinion on Max Credit.", "summary": "Rule compliant. No adverse announcements are listed in the report, so this rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/Summary/SignificantChanges", "Report/Payments"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b01923c6-8ef0-4a44-b10b-28e14a1e66f2", "question_number": 15, "question": "Adverse announcements should be described in negative terms.", "summary": "Rule compliant. No adverse announcements are present, so this rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/Summary/SignificantChanges"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "f629a0bd-8a92-4ceb-9139-b9c2756905e3", "question_number": 16, "question": "The Payments section must contain relevant information related to the sector in which the company operates.", "summary": "Rule compliant. The 'Payments' section describes the company as importers/distributors of building materials, which aligns with the 'Activities' and NACE codes.", "confidence_score": 0.98, "relevant_sections": ["Report/Payments/CreditOpinionNotes", "Report/Activities"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "523014cb-7bfc-4e34-976c-876acb0bc5e5", "question_number": 17, "question": "In service activities, incorrect text related to exports should not be included.", "summary": "Rule compliant. The 'Activities' section correctly notes 'Export Comment: Subject does not operate overseas.'", "confidence_score": 1, "relevant_sections": ["Report/Activities/ExportComment"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "02c6363c-44c0-4a62-ae91-138276b6d27a", "question_number": 18, "question": "If 'Income' is populated, 'Total Income' should also be populated, and vice versa.", "summary": "Rule compliant. In the financial table, both 'Sales turnover' and 'Total Income' are populated with identical values for all years shown.", "confidence_score": 1, "relevant_sections": ["Report/Financials/ProfitAndLoss/Income", "Report/Financials/ProfitAndLoss/TotalIncome"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "350e5505-d66f-42ed-86b4-7d4439f95e90", "question_number": 19, "question": "When the credit opinion is requested in a different currency and no credit is granted, the TRA should be marked High.", "summary": "Rule compliant. Credit is granted and was not requested in an alternate currency. This rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/Payments/OpinionOnMaxCredit"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ce3a2f61-989f-463f-aea5-718d324bc03d", "question_number": 20, "question": "Country risk classification should not be included if it is not applicable.", "summary": "Rule compliant. No specific 'Country risk classification' is mentioned in the 'Payments' section.", "confidence_score": 0.98, "relevant_sections": ["Report/Payments/CreditOpinionNotes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6848f085-294d-4afc-b6c7-7b1fa51388a0", "question_number": 21, "question": "The TRA should not include a classification for companies that are dormant or have ceased trading.", "summary": "Rule compliant. The company status is 'Active' and the TRA is 'Normal'. This rule is followed.", "confidence_score": 1, "relevant_sections": ["Report/LegalStatus/CompanyStatus", "Report/Payments/TradeRiskAssessment"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "215349a3-3f9f-46af-a763-2d347dc3dfcc", "question_number": 22, "question": "If the license number provided in the order belongs to a registered branch but is not listed under Related Entities as a Branch, a Special Note must be added and 'Transfer to Principal' should be selected.", "summary": "Cannot validate. The report does not specify which license number was provided in the order. Manual validation against the original order is required.", "confidence_score": 0.9, "relevant_sections": ["Report/Header/ClientReferenceNo", "Report/RelatedEntities"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "69a5e3a3-2f21-45c6-90cd-e24f746c1050", "question_number": 23, "question": "If any registration number has expired, a comment should be added explaining the expiry.", "summary": "Rule compliant. The only registration number with an expiry date is the 'CR Number', which expires on 16 July 2026. No numbers have expired.", "confidence_score": 1, "relevant_sections": ["Report/LegalStatus/RegistrationNumbers"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c468c855-6045-45a2-8f95-e86f0ef384f2", "question_number": 24, "question": "Mark as an issue if a small company is associated with a large credit amount.", "summary": "Requires editorial review. The company has 180 employees and BHD 16M turnover. A credit of BHD 200,000 seems reasonable. Not a clear compliance failure.", "confidence_score": 0.9, "relevant_sections": ["Report/Summary", "Report/Payments/OpinionOnMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "891e1ad3-f8fb-42d6-9d6c-cd02a3a1490e", "question_number": 25, "question": "Mark as an issue if a large company is associated with a small credit amount.", "summary": "Rule compliant. This rule does not appear to apply as the credit amount seems proportionate to the company's scale.", "confidence_score": 0.95, "relevant_sections": ["Report/Summary", "Report/Payments/OpinionOnMaxCredit"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6031aacf-708a-472a-8ec6-e5b2395e7b6c", "question_number": 26, "question": "Position classifications should align with similar positions across the report.", "summary": "Rule compliant. Positions listed (e.g., 'Chairman', 'Managing Director', 'Financial Controller') are standard and consistent.", "confidence_score": 1, "relevant_sections": ["Report/Personnel/PersonalSectionDetails"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1e050431-91c2-4c13-bba5-1533b1334a31", "question_number": 27, "question": "If the research type is \"Negative,\" the report must include <PERSON><PERSON>, <PERSON><PERSON>, General In<PERSON>, and Special Notes reflecting that the company could not be located.", "summary": "Rule compliant. The report is not a 'Negative' research type. This rule does not apply.", "confidence_score": 1, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "706162a9-e22d-4736-a13d-9db12297cf76", "question_number": 28, "question": "If the research type is \"No Contact,\" it means the subject company was not contacted, and no Person Interviewed should be mentioned.", "summary": "Rule compliant. A person was interviewed, so the research type is not 'No Contact'. This rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/Summary/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "fb0e35a8-96a1-4b2e-b09f-d37e08268c2e", "question_number": 29, "question": "If the research type is \"Decline,\" it means the subject company refused to provide information. The Payments section should include a comment stating this.", "summary": "Rule compliant. The report is not a 'Decline' type, as financial figures and an interview were provided. This rule does not apply.", "confidence_score": 1, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5ef22d7d-e570-4d54-998c-52a3afaa656f", "question_number": 30, "question": "If the research type is blank, it means the subject company was contacted. The Payments section should include a comment stating this, and the Person Interviewed should be listed.", "summary": "Rule compliant. The research type is effectively blank/default, and a 'Person Interviewed' is correctly listed.", "confidence_score": 1, "relevant_sections": ["Report/Summary/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "7120172a-d24c-4cf7-8978-7ca85bf07744", "question_number": 31, "question": "A sanctions search must always be conducted.", "summary": "Rule compliant. The report explicitly states 'Sanction Search Performed: Yes'.", "confidence_score": 1, "relevant_sections": ["Report/PublicRecordInformation/Sanction/SanctionSearchPerformed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c9d1ec65-37e9-47ef-9d90-247c21588aec", "question_number": 32, "question": "If sanctions are present, the Payments section should state this, the TRA should be High, and both the Credit Opinion and Max Credit fields should be left blank, with a note in Opinion on Max Credit.", "summary": "Rule compliant. The report states 'Sanction found: No', so this rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/PublicRecordInformation/Sanction/SanctionFound"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "65bf5de4-e31f-4346-b160-d1be9397614b", "question_number": 33, "question": "Town names should not appear in the Registration Number field.", "summary": "Question skipped - Client code mismatch. This question is for client 'TURKEXIM', but the report is not for this client.", "confidence_score": 1, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "e19c9a77-8b76-46d4-9cd1-3103c2986ba0", "question_number": 34, "question": "The Provenance icon should be present when data is added to a section.", "summary": "Cannot validate. The report text includes a key for provenance icons but does not render them, making it impossible to verify their presence in each section.", "confidence_score": 0.9, "relevant_sections": ["Report/Header"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c87257a6-2d50-437d-885d-6666cf1a0404", "question_number": 35, "question": "If a research type is selected, a telephone number or email address should be provided in the report.", "summary": "Rule compliant. The report has a default research type (contact was made) and the 'Contact Details' section is populated with multiple phone numbers and email addresses.", "confidence_score": 1, "relevant_sections": ["Report/Address/ContactDetails"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "30fb75a8-a287-47a4-95e0-1fd983956e45", "question_number": 36, "question": "Capital values must include the currency.", "summary": "Rule compliant. Both 'Capital Paid Up' and 'Capital Authorised' values are explicitly listed with the currency 'BHD'.", "confidence_score": 1, "relevant_sections": ["Report/LegalStatus/Capital"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "70b94be7-3a77-4ca1-b8a4-fd860ec113a9", "question_number": 37, "question": "If the subject is a member of a group and related entities are listed, the Shareholder field should also be populated.", "summary": "Rule compliant. A parent company is listed, and the 'Shareholders' section is correctly populated, identifying the parent as the main shareholder.", "confidence_score": 1, "relevant_sections": ["Report/RelatedEntities", "Report/LegalStatus/Shareholders"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a4caf677-5f3e-4999-b52d-048bf9fc6025", "question_number": 38, "question": "The Credit Opinion should be properly calculated.", "summary": "Cannot validate. The Credit Opinion is 'Large'. Verifying the calculation requires a proprietary scoring model not available for this validation.", "confidence_score": 0.9, "relevant_sections": ["Report/Payments/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8d5f2077-7be7-4a56-8c91-ad6f09e465e3", "question_number": 39, "question": "The company name in the financials must match the name in the header when the subject is selected.", "summary": "Rule compliant. The company name in the 'Financials' section matches the name in the header.", "confidence_score": 1, "relevant_sections": ["Report/Header/RequestedName", "Report/Financials/Subject"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a6955e95-49c1-4d7f-a82c-99ced91733b9", "question_number": 40, "question": "Town name must be entered in the Town field.", "summary": "Validation failed. While 'Man<PERSON>' is present in the main address, some related entities list the town ('Salhiya') within the address line instead of in a discrete field.", "confidence_score": 0.9, "relevant_sections": ["Report/Address/AddressDetails", "Report/RelatedEntities/Affiliate/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "372f1e55-29ba-4d56-9140-a80092067fe6", "question_number": 41, "question": "If the company status in the Legal section is \"Not Known,\" credit should not be granted.", "summary": "Rule compliant. The 'Company Status' is 'Active', not 'Not Known'. This rule does not apply.", "confidence_score": 1, "relevant_sections": ["Report/LegalStatus/CompanyStatus"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-24T10:30:00.000000", "processing_time": 25.456, "validation_options": {"focus_prompt": null}, "client_filtering_enabled": true, "order_client_code": "**********/REV", "rag_enabled": true, "permanent_question_bank_used": true, "upload_info": {"file_id": "b81ebcca-e224-5e11-bc6e-b00bf93cce42", "filename": "COMPANY_STATUS_REPORT_128079.47.1.txt", "file_type": ".txt", "file_size": 18432, "upload_timestamp": "2025-07-24T10:29:44.000000", "status": "uploaded", "source": "manual_upload", "report_id": "128079.47.1"}}]