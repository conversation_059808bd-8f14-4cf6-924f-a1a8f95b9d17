{"validation_id": "7102294e-a1dd-4fec-a525-5cdb2c9686a9", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "3c440f28-6d6c-42aa-bb9e-51c9a89434dc", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "521ff60c-bba3-4d83-9bde-7ea79bea3c33", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "395c2b07-2095-4d35-ad56-18bb81e373dc", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is missing; cannot verify compliance with required sections.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection", "Report/Payments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d3edb7ad-2aad-4b58-9094-403ce47df439", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; comments and payment notes should not be present.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c3f177f9-9731-4ffd-afbb-58c7787f23f2", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit not provided; cannot verify against Total Income.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5b97bb02-1bb7-4760-994b-785c50986e6d", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses for raw materials found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6fb3bdcd-a26b-43e6-8c55-be4113c12282", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name matches Company Name; Client Specific Comments not filled.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4354cbe4-0121-49fb-a6c9-ba9a6455e42f", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific comment is missing.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "03c16c6b-12bc-4f6f-a257-4fc164423cfa", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special notes; compliance violated.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "cfecfc23-8958-41c4-a111-3920cd400338", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License and registration numbers are missing from the report sections.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/RegistrationNumbers", "Report/SpecialNotesSection/TextForSpecificClients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ccf1864c-d2c9-4b9d-8037-4df439855001", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "cbbbc515-c56b-4025-a366-f31339204d0b", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check passed; no violations found in specified sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/ICPLegalGroup", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2653082d-eb1b-4359-b770-3ad622203568", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Payments section lacks reference to significant changes, violating compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/Payments", "Report/SignificantChanges"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c83b2615-ec3d-414a-b503-68e856235a0c", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Rule violated: significant changes and payments sections missing.", "confidence_score": 0.9, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "af40d4fb-57c4-4463-be82-bd0e04b511a9", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance with negative description rule is not applicable.", "confidence_score": 0.9, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b4664410-a9c6-4aae-b9e9-7bac4b2db2c2", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant sector information from Subject activities.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8e663c6e-ea87-49ec-995e-4ae00b8d35b5", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Compliance violation: Incorrect texts added regarding export activities.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b8b149d2-3a84-433d-bbec-368152e04c2a", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing, violating the validation rule.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitAndLoss/Income", "Report/ProfitAndLoss/TotalIncome"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "06d6fc63-ade4-491c-ae3f-fddbdbb05165", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Credit opinion not requested; compliance rule not met.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/Capital/NotGiven"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3d299277-85e8-4ee2-bc2d-a1f80c835c78", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not present when applicable, violation found.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "749a40da-0074-4f7e-9620-29867eff6b9d", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is active; no violation of dormant or ceased trading classification.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e9a9eba8-8594-43da-b575-7d931b4c332e", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not provided; compliance cannot be verified.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "8687d83f-1b8c-40b8-a1a5-f853cc413383", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no Comment present.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2e56ca79-a716-4d1b-8624-6adf28fbce87", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp capital is small (123.00) but has a large amount of liabilities, violating the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f10b5a7b-c6b5-4b15-ac1d-313125602625", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital of 123.00 violates the large with small amount rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2a61495e-b8be-4688-b180-e8f733f90528", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications are not provided for compliance checking.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9897501a-eb73-4e99-8f03-261f0862a52e", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation rejected: Missing 'Research Type' as 'Negative' in <PERSON><PERSON>.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "f6b00069-4d4f-4505-a431-517194ef9fe5", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline' but Person Interviewed is absent, compliance violated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection/PersonInterviewed"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4e7e02ff-3fc5-45ac-b15a-41b456f4187d", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Validation failed: 'Payments' section lacks comment on decline.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1c92bc43-b2ae-4b63-a3c3-3e83767a0ddc", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation failed: Research Type is missing, no comment on payments, and no Person Interviewed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/PaymentsSection/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6c6e9cf4-42db-4ac0-afee-dafd8debf6c9", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; compliance violation found.", "confidence_score": 0.98, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b53a019b-f43f-49cc-adb7-e0d893c81349", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No sanctions found; compliance rule violated due to missing required sections.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed", "Report/SanctionSection/SanctionFound"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f0b4d008-859a-4386-9de3-3c5bedacd88a", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "ef5451cd-304b-4de1-880c-d0d4df29f695", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7f10e29b-bfa1-462e-98ba-603056aea6ca", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details present; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "307de11c-4559-40ab-8519-fc153f0ec901", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; expected a currency specification.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6085a1cc-cfbf-46f2-bffb-2645bbd31a44", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder field is not populated while related entities exist.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6d9e3789-8517-4a22-a19e-9e2786559db2", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot verify credit opinion calculation; missing payment data.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9345f16b-2f38-4595-be92-49d2aa0b3174", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Names in financials match header; compliance rule satisfied.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "17c16cb2-ea6e-4051-a454-20cb2aa87d53", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty; report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bea63e0d-ea79-48a5-a9d9-c4217154b61c", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is known as 'Active'; credit can be granted.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 14:08:24.295349", "processing_time": 15.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}