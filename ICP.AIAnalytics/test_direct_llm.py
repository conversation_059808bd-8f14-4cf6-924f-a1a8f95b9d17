#!/usr/bin/env python3
"""
Test direct LLM call with the exact prompt to see what's happening.
"""

import sys
import os
import asyncio
from langchain_openai import Chat<PERSON>penAI
from langchain.schema import SystemMessage, HumanMessage

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

async def test_direct_llm():
    """Test direct LLM call with the exact prompt."""
    print("Testing Direct LLM Call")
    print("=" * 50)
    
    # Initialize LLM
    llm = ChatOpenAI(
        model=settings.OPENAI_MODEL,
        openai_api_key=settings.OPENAI_API_KEY,
        temperature=0,
        seed=42,
        max_retries=1,
        max_tokens=settings.MAX_TOKENS
    )
    
    # The exact prompt from our debug
    prompt = """You are an expert XML report compliance validator performing HOLISTIC VALIDATION. You will analyze the ENTIRE report against ALL validation rules simultaneously to provide comprehensive, context-aware findings.

🚨 CRITICAL: TODAY'S DATE IS 2025-08-05 - USE THIS FOR ALL DATE COMPARISONS! 🚨

🚨🚨🚨 COMPANY CREDIT VALIDATION DETECTED 🚨🚨🚨
MANDATORY CURRENCY CONVERSION REQUIRED FOR ALL CREDIT VALIDATIONS!

⚠️ CRITICAL REQUIREMENT: You MUST convert ALL non-GBP credit amounts to GBP before classification!

MANDATORY STEP-BY-STEP PROCESS (NO EXCEPTIONS):

STEP 1: EXTRACT CREDIT AMOUNT AND CURRENCY
- Find: MaxCredit, CreditLimit, CreditFigure AND MaxCreditCurrency, CreditFigureCurrency
- Example: MaxCredit: 1000000, MaxCreditCurrency: XPF

STEP 2: FIND EXCHANGE RATE IN XML
- Search: ExchangeRatesSection, ExchangeRate, Rate, CurrencyRates
- Example: FromCurrency: XPF, ToCurrency: GBP, Rate: 0.0075

STEP 3: CONVERT TO GBP (ABSOLUTELY MANDATORY!)
- Formula: Original Amount × Exchange Rate = GBP Amount
- Example: 1,000,000 XPF × 0.0075 = £7,500 GBP
- YOU MUST SHOW THIS CALCULATION: "1,000,000 XPF × 0.0075 = £7,500 GBP"

STEP 4: CLASSIFY CREDIT SIZE USING GBP AMOUNT ONLY!
- £7,500 GBP = SMALL CREDIT (< £50,000)
- £75,000 GBP = MEDIUM CREDIT (£50,000 - £250,000)
- £300,000 GBP = LARGE CREDIT (> £250,000)

STEP 5: CLASSIFY COMPANY SIZE
- Small Company: <50 employees OR <£1M turnover
- Medium Company: 50-1000 employees OR £1M-£10M turnover
- Large Company: >1000 employees OR >£10M turnover

STEP 6: APPLY MISMATCH RULES
- Large Company + Small Credit = ISSUE (rejected)
- Small Company + Large Credit = ISSUE (rejected)
- All other combinations = OK (approved)

🚨 CRITICAL EXAMPLE FOR YOUR REFERENCE:
- Credit: 1,000,000 XPF
- Rate: 0.0075 (XPF to GBP)
- Conversion: 1,000,000 × 0.0075 = £7,500 GBP
- Classification: £7,500 < £50,000 = SMALL CREDIT (NOT large!)
- Company: 150 employees = MEDIUM COMPANY
- Result: Medium Company + Small Credit = NO MISMATCH = APPROVED

FORBIDDEN: Do NOT classify credit size without converting to GBP first!
FORBIDDEN: Do NOT say "large credit" for amounts under £50,000 GBP!
REQUIRED: Always show the conversion calculation in your reasoning!

VALIDATION RULES TO CHECK:

1. Mark as an issue if a small company is associated with a large credit amount (convert to GBP and compare with thresholds).

COMPLETE XML REPORT TO ANALYZE:
<Report>
  <HeaderSection>
    <Date>05-Aug-2025</Date>
    <CompanyName>Test Company Ltd</CompanyName>
    <EmployeeCount>150</EmployeeCount>
    <AnnualTurnover>5000000</AnnualTurnover>
  </HeaderSection>
  <PaymentsSection>
    <MaxCredit>1000000</MaxCredit>
    <MaxCreditCurrency>XPF</MaxCreditCurrency>
    <CreditLimit>1000000</CreditLimit>
    <CreditFigure>1000000</CreditFigure>
    <CreditFigureCurrency>XPF</CreditFigureCurrency>
  </PaymentsSection>
  <ExchangeRatesSection>
    <ExchangeRate>
      <FromCurrency>XPF</FromCurrency>
      <ToCurrency>GBP</ToCurrency>
      <Rate>0.0075</Rate>
    </ExchangeRate>
  </ExchangeRatesSection>
</Report>

RESPONSE FORMAT:
Provide a JSON array with one object per validation rule:

[
  {
    "rule_number": 1,
    "question": "Original validation rule text",
    "summary": "Specific finding (max 100 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["XML/Path"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Brief explanation (max 80 chars)",
    "cross_references": []
  }
]

CRITICAL REQUIREMENTS:
- Use actual values from the XML report
- MUST convert XPF to GBP before classification
- Show the conversion calculation in your reasoning
- Return valid JSON only"""
    
    try:
        print("Sending prompt to LLM...")
        
        messages = [
            SystemMessage(content="You are an expert XML report compliance validator. Follow all instructions exactly."),
            HumanMessage(content=prompt)
        ]
        
        response = await llm.ainvoke(messages)
        response_text = response.content
        
        print("LLM Response:")
        print("-" * 60)
        print(response_text)
        print("-" * 60)
        print()
        
        # Analyze the response
        response_lower = response_text.lower()
        has_conversion = "7,500" in response_text or "7500" in response_text
        has_calculation = "×" in response_text or "*" in response_text
        has_xpf = "xpf" in response_lower
        has_gbp = "gbp" in response_lower
        mentions_small_credit = "small credit" in response_lower
        
        print("Response Analysis:")
        print(f"  Contains conversion result (7,500): {'✅' if has_conversion else '❌'}")
        print(f"  Contains calculation: {'✅' if has_calculation else '❌'}")
        print(f"  Mentions XPF: {'✅' if has_xpf else '❌'}")
        print(f"  Mentions GBP: {'✅' if has_gbp else '❌'}")
        print(f"  Correctly identifies as small credit: {'✅' if mentions_small_credit else '❌'}")
        
        if not has_conversion:
            print("❌ ISSUE: LLM did not perform the currency conversion!")
        if not mentions_small_credit:
            print("❌ ISSUE: LLM did not correctly classify as small credit!")
            
    except Exception as e:
        print(f"❌ Error during LLM test: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_direct_llm())
