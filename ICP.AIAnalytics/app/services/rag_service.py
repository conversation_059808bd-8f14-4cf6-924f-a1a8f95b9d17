"""
RAG (Retrieval-Augmented Generation) Service for improving validation accuracy.
This service uses correct response examples to guide LLM responses.
"""

from typing import List, Dict, Any, Optional
import json
import uuid
from datetime import datetime
import asyncio
from langchain_openai import OpenAIEmbeddings
from langchain_core.documents import Document

from app.core.config import settings
from app.services.vector_store import VectorStore


class RAGService:
    def __init__(self):
        self.vector_store = VectorStore()
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=settings.OPENAI_API_KEY
        ) if settings.OPENAI_API_KEY else None
        
        # Collection names for different types of examples
        self.examples_collection = "validation_examples"
        self.patterns_collection = "response_patterns"
        
    async def initialize_knowledge_base(self, correct_examples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Initialize the RAG knowledge base with correct response examples."""
        try:
            print("Initializing RAG knowledge base...")
            
            # Process and store examples
            examples_added = await self._store_examples(correct_examples)
            
            # Extract and store response patterns
            patterns_added = await self._extract_and_store_patterns(correct_examples)
            
            print(f"RAG knowledge base initialized:")
            print(f"  - Examples added: {examples_added}")
            print(f"  - Patterns extracted: {patterns_added}")
            
            return {
                "status": "success",
                "examples_added": examples_added,
                "patterns_added": patterns_added,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Error initializing RAG knowledge base: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def retrieve_relevant_examples(self, question: str, xml_content: str, n_results: int = 3) -> List[Dict[str, Any]]:
        """Retrieve relevant examples for a given question and XML content."""
        try:
            # Create search query combining question and key XML elements
            search_query = self._create_search_query(question, xml_content)
            
            # Search for similar examples
            examples = await self._search_examples(search_query, n_results)
            
            # Search for relevant patterns
            patterns = await self._search_patterns(question, n_results=2)
            
            return {
                "examples": examples,
                "patterns": patterns,
                "search_query": search_query
            }
            
        except Exception as e:
            print(f"Error retrieving relevant examples: {e}")
            return {"examples": [], "patterns": [], "search_query": ""}
    
    async def _store_examples(self, examples: List[Dict[str, Any]]) -> int:
        """Store correct response examples in vector store."""
        try:
            documents = []
            metadatas = []
            ids = []
            
            for i, example in enumerate(examples):
                # Create searchable document from example
                doc_text = self._create_example_document(example)
                
                documents.append(doc_text)
                metadatas.append({
                    "type": "validation_example",
                    "question": example.get("question", ""),
                    "status": example.get("status", ""),
                    "confidence_score": example.get("confidence_score", 0),
                    "created_at": datetime.now().isoformat(),
                    "example_id": example.get("id", f"example_{i}")
                })
                ids.append(f"example_{uuid.uuid4()}")
            
            if documents:
                # Generate embeddings
                embeddings = None
                if self.embeddings:
                    embeddings = await self._generate_embeddings(documents)
                
                # Store in vector store
                collection = self.vector_store._get_or_create_collection(self.examples_collection)
                collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids,
                    embeddings=embeddings
                )
                
                return len(documents)
            
            return 0
            
        except Exception as e:
            raise Exception(f"Error storing examples: {str(e)}")
    
    async def _extract_and_store_patterns(self, examples: List[Dict[str, Any]]) -> int:
        """Extract and store response patterns from examples."""
        try:
            patterns = self._extract_response_patterns(examples)
            
            documents = []
            metadatas = []
            ids = []
            
            for pattern_type, pattern_data in patterns.items():
                doc_text = f"Pattern Type: {pattern_type}\n"
                doc_text += f"Description: {pattern_data['description']}\n"
                doc_text += f"Keywords: {', '.join(pattern_data['keywords'])}\n"
                doc_text += f"Example Responses: {'; '.join(pattern_data['examples'][:3])}"
                
                documents.append(doc_text)
                metadatas.append({
                    "type": "response_pattern",
                    "pattern_type": pattern_type,
                    "keywords": ", ".join(pattern_data['keywords']),  # Convert list to string
                    "example_count": len(pattern_data['examples']),
                    "created_at": datetime.now().isoformat()
                })
                ids.append(f"pattern_{uuid.uuid4()}")
            
            if documents:
                # Generate embeddings
                embeddings = None
                if self.embeddings:
                    embeddings = await self._generate_embeddings(documents)
                
                # Store in vector store
                collection = self.vector_store._get_or_create_collection(self.patterns_collection)
                collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids,
                    embeddings=embeddings
                )
                
                return len(documents)
            
            return 0
            
        except Exception as e:
            raise Exception(f"Error storing patterns: {str(e)}")
    
    def _create_example_document(self, example: Dict[str, Any]) -> str:
        """Create a searchable document from a validation example."""
        doc_parts = []
        
        # Add question
        if "question" in example:
            doc_parts.append(f"Question: {example['question']}")
        
        # Add summary/response
        if "summary" in example:
            doc_parts.append(f"Response: {example['summary']}")
        
        # Add status and reasoning
        if "status" in example:
            doc_parts.append(f"Status: {example['status']}")
        
        if "reasoning" in example:
            doc_parts.append(f"Reasoning: {example['reasoning']}")
        
        # Add relevant sections
        if "relevant_sections" in example:
            sections = example["relevant_sections"]
            if isinstance(sections, list):
                doc_parts.append(f"Relevant Sections: {', '.join(sections)}")
        
        # Add XML context if available
        if "xml_context" in example:
            doc_parts.append(f"XML Context: {example['xml_context'][:200]}...")
        
        return "\n".join(doc_parts)
    
    def _create_search_query(self, question: str, xml_content: str) -> str:
        """Create an effective search query from question and XML content."""
        # Extract key terms from question
        question_keywords = self._extract_keywords(question)
        
        # Extract key XML elements (simplified)
        xml_keywords = self._extract_xml_keywords(xml_content)
        
        # Combine into search query
        search_parts = [question]
        if question_keywords:
            search_parts.append(" ".join(question_keywords[:5]))
        if xml_keywords:
            search_parts.append(" ".join(xml_keywords[:3]))
        
        return " ".join(search_parts)
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords from text."""
        # Simple keyword extraction (can be enhanced with NLP)
        important_words = [
            "company", "name", "status", "credit", "payment", "currency", "compliance",
            "legal", "registration", "address", "director", "shareholder", "beneficial",
            "ownership", "sanctions", "pep", "aml", "kyc", "risk", "approved", "rejected"
        ]
        
        text_lower = text.lower()
        found_keywords = [word for word in important_words if word in text_lower]
        
        return found_keywords
    
    def _extract_xml_keywords(self, xml_content: str) -> List[str]:
        """Extract key elements from XML content."""
        # Simple XML keyword extraction
        xml_lower = xml_content.lower()
        xml_keywords = []
        
        # Look for common XML elements
        if "companyname" in xml_lower:
            xml_keywords.append("company_name")
        if "status" in xml_lower:
            xml_keywords.append("status")
        if "payment" in xml_lower:
            xml_keywords.append("payment")
        if "currency" in xml_lower:
            xml_keywords.append("currency")
        if "address" in xml_lower:
            xml_keywords.append("address")
        
        return xml_keywords
    
    async def _search_examples(self, query: str, n_results: int) -> List[Dict[str, Any]]:
        """Search for relevant examples in the knowledge base."""
        try:
            collection = self.vector_store._get_or_create_collection(self.examples_collection)
            
            # Generate query embedding
            query_embedding = None
            if self.embeddings:
                embeddings = await self._generate_embeddings([query])
                query_embedding = embeddings[0] if embeddings else None
            
            # Search
            if query_embedding:
                results = collection.query(
                    query_embeddings=[query_embedding],
                    n_results=n_results
                )
            else:
                results = collection.query(
                    query_texts=[query],
                    n_results=n_results
                )
            
            # Format results
            examples = []
            if results.get("documents"):
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i] if results.get("metadatas") else {}
                    distance = results["distances"][0][i] if results.get("distances") else 1.0
                    
                    examples.append({
                        "document": doc,
                        "metadata": metadata,
                        "similarity": 1.0 - distance,  # Convert distance to similarity
                        "relevance_score": max(0, 1.0 - distance)
                    })
            
            return examples
            
        except Exception as e:
            print(f"Error searching examples: {e}")
            return []
    
    async def _search_patterns(self, query: str, n_results: int) -> List[Dict[str, Any]]:
        """Search for relevant response patterns."""
        try:
            collection = self.vector_store._get_or_create_collection(self.patterns_collection)
            
            # Generate query embedding
            query_embedding = None
            if self.embeddings:
                embeddings = await self._generate_embeddings([query])
                query_embedding = embeddings[0] if embeddings else None
            
            # Search
            if query_embedding:
                results = collection.query(
                    query_embeddings=[query_embedding],
                    n_results=n_results
                )
            else:
                results = collection.query(
                    query_texts=[query],
                    n_results=n_results
                )
            
            # Format results
            patterns = []
            if results.get("documents"):
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i] if results.get("metadatas") else {}
                    distance = results["distances"][0][i] if results.get("distances") else 1.0
                    
                    patterns.append({
                        "document": doc,
                        "metadata": metadata,
                        "similarity": 1.0 - distance,
                        "relevance_score": max(0, 1.0 - distance)
                    })
            
            return patterns
            
        except Exception as e:
            print(f"Error searching patterns: {e}")
            return []
    
    async def _generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for texts."""
        try:
            if self.embeddings:
                return await self.embeddings.aembed_documents(texts)
            return None
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            return None
    
    def _extract_response_patterns(self, examples: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Extract common response patterns from examples."""
        patterns = {
            "approved_responses": {
                "description": "Patterns for approved validation responses",
                "keywords": [],
                "examples": []
            },
            "rejected_responses": {
                "description": "Patterns for rejected validation responses", 
                "keywords": [],
                "examples": []
            },
            "manual_intervention": {
                "description": "Patterns for manual intervention responses",
                "keywords": [],
                "examples": []
            }
        }
        
        for example in examples:
            status = example.get("status", "").lower()
            summary = example.get("summary", "")
            
            if status == "approved" and summary:
                patterns["approved_responses"]["examples"].append(summary)
                patterns["approved_responses"]["keywords"].extend(
                    self._extract_keywords(summary)
                )
            elif status == "rejected" and summary:
                patterns["rejected_responses"]["examples"].append(summary)
                patterns["rejected_responses"]["keywords"].extend(
                    self._extract_keywords(summary)
                )
            elif status == "manual_intervention_needed" and summary:
                patterns["manual_intervention"]["examples"].append(summary)
                patterns["manual_intervention"]["keywords"].extend(
                    self._extract_keywords(summary)
                )
        
        # Remove duplicates from keywords
        for pattern_type in patterns:
            patterns[pattern_type]["keywords"] = list(set(patterns[pattern_type]["keywords"]))
        
        return patterns
