{"validation_id": "b1a95f88-0295-4d14-8ac3-16ee9489720f", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181305", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "ec8c65cb-bc9c-4d0a-8da5-44c4c0b3db10", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client 'TURKEXIM'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "2d7992a0-7912-4bda-8f0f-4bd6a1f9b5f8", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client '??'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "6803d38a-fd9d-4003-b8ca-2df119f245e0", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name not present; comments and payment details not required.", "confidence_score": 0.9, "relevant_sections": ["Report/ClientSpecificCommentsSection/ClientSpecificComments", "Report/PaymentSection/PaymentRequired"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "daff4d81-f1b9-41ce-8143-e08b3fe8d730", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name field is missing; Client Specific Comments and Payment notes are not present.", "confidence_score": 0.98, "relevant_sections": ["Order Details/Client Name", "Payments/Credit Opinion Notes", "Special Notes/Text for Specific Clients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "788849e4-54e0-4a68-84dc-05e8933f9f79", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit 'Gross Profit Amount' not less than Total Income 'Total Income Amount'", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bb6dcd42-e59c-4039-8e13-f0d53860bac1", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Raw Materials and Supplies listed under Direct Costs, compliant with validation rule.", "confidence_score": 0.98, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "e6d073fd-b0b0-4116-a9a7-1952671c80c9", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'RE test 2' does not match requested name 'test', comments missing.", "confidence_score": 0.9, "relevant_sections": ["HeaderSection/CompanyName", "HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c1c9443f-107c-4849-9a77-dbcb676c7444", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address matches active address; no comments needed.", "confidence_score": 0.98, "relevant_sections": ["AddressesSection/Addresses/Address/ActiveAddress", "AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "25348c2c-b08d-43a3-a02a-743c71d86098", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit currencies include EUR, USD, GBP; compliant with TURKEXIM exclusion.", "confidence_score": 0.98, "relevant_sections": ["SpecialNote/MaxCredit/Currency", "SpecialNote/MaxCredit/Note"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1e0cbcf0-8c09-40a3-9dc3-d716400c72ab", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License numbers not recorded in legal section or special notes, missing info needed.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/RegistrationNumbers", "RelatedEntitiesLegalStatusSection/RegistrationNumbers"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "44ed540e-55ec-44ca-838e-aaa27a76ece1", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in 'LegalStatus' and 'ICPLegalGroup'.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/LegalStatus", "RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f7bbf323-d945-40be-8db3-3b3e61c06d61", "question_number": 12, "question": "Spelling Check", "summary": "Spelling error found in 'atest' in Shareholder names.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Shareholders/Shareholder/Name"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "dbcffc32-4966-4ad0-bdbc-93b58f5ecd68", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No significant changes or adverse announcements found in XML data.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/LegalStatus", "Report/RelatedEntitiesSection/RelatedEntity"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c2aa55cd-3a3c-4eca-97a1-0cbf3cc27f0d", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Risk level is 'High', but Credit Opinion and Max Credit are blank, compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/SignificantChangesSection/AdverseAnnouncements", "Report/PaymentsSection", "Report/TradeRiskAssessment"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "621a5b92-bd27-4da0-89c7-85be9f5a4d60", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/LegalStatus", "Report/RelatedEntitiesSection/RelatedEntity"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "52ec4745-463c-4460-91dd-f740435d6376", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-related information; compliance violated.", "confidence_score": 0.9, "relevant_sections": ["General Info", "Subject Activities", "Payments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4542a663-c8be-4409-b484-7ab71b83e1d2", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Registration Number 'Other Registration Number1' contains incorrect text, violates export rule.", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e7092617-8e4b-43bd-bebc-157ce81dba43", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both absent, cannot verify compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/ProfitLoss/Income", "Report/ProfitLoss/TotalIncome"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c3ac448f-a1ff-48d0-8da7-71b7721b8dad", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Currency 'ANG' found, credit not given, TRA high condition unmet.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/NotGiven", "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/Currency"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "03a31aeb-8e00-45fb-be58-121ad83b35d1", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not present, violates requirement for payment section.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/LegalStatus", "Report/RelatedEntitiesSection/RelatedEntity"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4fbf2777-7ff6-4549-87b7-ba9fea9f8585", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status 'Active' violates dormant/ceased trading rule.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9968f1a3-b4de-45a7-96c3-4e91983ff2b6", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number '3456789' not listed as a branch, violates compliance rules.", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b35f37ba-340d-4e45-87ea-f286ff00dd9d", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number '32433333' expired but has comment 'test', violates expiry comment rule.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/RegistrationNumbers/RegistrationNumber[1]", "RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber[1]"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "96eeb833-f83f-4d63-8ab0-234e924aebbc", "question_number": 24, "question": "Small with large amount", "summary": "Shareholder 'test 1' holds 10 shares but has 30% shareholding, violates small with large amount rule.", "confidence_score": 0.92, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder[1]/NoOfShares", "LegalStatusSection/Shareholders/Shareholder[1]/Shareholding"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "fade9c7e-0031-4af3-8175-8d3e498bfad5", "question_number": 25, "question": "Large with small amount", "summary": "Shareholder 'atest' has 0 shares, violating large with small amount rule", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder/NoOfShares", "LegalStatusSection/Shareholders/Shareholder/Shareholding"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d5fe2994-e3c9-4f3e-8092-851de509257f", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not found, cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["RelatedEntitiesSection/RelatedEntity/CompanyName"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "199199cb-1a52-4b4a-ab5b-2ebce1f7cefb", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Research Type is missing; Address and General Info sections are populated.", "confidence_score": 0.85, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0dcbe8be-c44c-4dfe-8d71-89dd1abce568", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline', Person Interviewed not present, compliant.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotes/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "378e2501-1051-4b53-809f-244c01c74a19", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research Type is 'Decline', but no comment in Payments section.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c22a6c95-0cce-4701-9532-795f3559888f", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is blank; Payments comment present, Person Interviewed found.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection/Comment", "Report/PersonInterviewedSection/PersonInterviewed"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c0670737-e092-4e6a-b7e3-8f32ef5f270d", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; missing relevant section.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c6e5a3e2-de6c-4be7-b0d8-47c671a85638", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanctions present, Trade Risk Assessment 'High', Credit Opinion and Max Credit are blank, compliant.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/Sanctions", "TradeRiskAssessment/RiskLevel", "CreditOpinion/Opinion", "MaxCredit/Amount", "OpinionOnMaxCredit/Opinion"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c03e20d4-a6fd-4a80-a402-4a89ead3b03c", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client 'TURKEXIM'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "2881abe7-1b5a-4e16-b476-9697d1564ff9", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in RelatedEntitiesLegalStatusSection.", "confidence_score": 0.9, "relevant_sections": ["RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "35c433f1-c649-410c-ab9b-ef5e013d4cc1", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type is 'Select' but no contact details provided.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "39c8532b-6e0f-4a53-baa2-e9ed6832846e", "question_number": 36, "question": "Capital missing currency", "summary": "Capital field is missing currency information, violates compliance.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "893b199b-21a4-40f3-bd90-3aa0d2362b40", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder 'atest' has no shares but is part of related entities, violates requirement.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder/Name", "RelatedEntitiesSection/RelatedEntity/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4db6b98b-17bb-4e10-8fab-4664b6fb225a", "question_number": 38, "question": "Calculate credit opinion", "summary": "Credit opinion calculation cannot be verified due to missing payment data.", "confidence_score": 0.85, "relevant_sections": ["LegalStatusSection/Shareholders", "RelatedEntitiesSection/RelatedEntitiesFinancialSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ebc096b8-d6e1-43bd-bc93-c5aa6c56ae2e", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'RE test 2' does not match shareholder name 'test 2', violation found.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/Shareholders/Shareholder/Name"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d49adf17-610f-4c79-8e9b-b4fc60ee3403", "question_number": 40, "question": "Town missing in town box", "summary": "Town field is 'test', complies with requirement for town box.", "confidence_score": 0.98, "relevant_sections": ["AddressesSection/Addresses/Address/Town"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d12d041e-a9e4-4ab8-a31a-8059a70eea7b", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is 'Active', compliant with credit granting rules.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/CompanyStatus"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 12:07:34.400238", "processing_time": 77.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": null, "rag_enabled": true, "permanent_question_bank_used": true}