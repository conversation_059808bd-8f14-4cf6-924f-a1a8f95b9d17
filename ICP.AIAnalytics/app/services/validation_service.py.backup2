from langchain_openai import Chat<PERSON>penAI
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain.prompts import PromptTemplate
from langchain.schema import SystemMessage, HumanMessage
from typing import List, Dict, Any, Optional
import asyncio
import json
import uuid
from datetime import datetime
import time
import xml.etree.ElementTree as ET

from app.core.config import settings
from app.models.schemas import ValidationResult, ValidationStatus, Question
from app.services.file_processor import FileProcessor


class ValidationService:
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            openai_api_key=settings.OPENAI_API_KEY,
            temperature=0.3,
            max_tokens=settings.MAX_TOKENS
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an XML report compliance validator. Check if the XML data follows the validation rule and provide accurate findings.

VALIDATION RULE #{question_number}: {question}

XML DATA TO CHECK:
{xml_content}

VALIDATION APPROACH:
1. UNDERSTAND THE RULE: What specific requirement is being checked?
2. FIND RELEVANT DATA: Look for XML elements needed to verify the rule
3. CHECK COMPLIANCE: Does the data meet the requirement or violate it?
4. REPORT FINDINGS: State the validation result clearly and specifically

CRITICAL REQUIREMENTS:
1. ACCURACY FOCUS: Provide precise validation findings, not data descriptions
2. SPECIFIC RESULTS: State exactly what was checked and the outcome
3. NATURAL LANGUAGE: Use clear, professional language without tags
4. ONE-LINER: Provide finding in one sentence (max 150 characters)
5. ACTIONABLE: Make it clear if rule is met, violated, or cannot be verified

RESPONSE FORMAT - JSON only:
{{
    "summary": "Clear validation finding with specific details (max 150 characters)",
    "confidence_score": 0.95,
    "relevant_sections": ["xml_path1", "xml_path2"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Brief justification for the validation result"
}}

STATUS GUIDELINES:
- "approved": Rule is followed correctly / No violations found
- "rejected": Rule is violated / Non-compliant condition exists
- "manual_intervention_needed": Cannot verify due to missing data or unclear rule

VALIDATION EXAMPLES:
Rule: "Company name should match requested name"
Good: "Company name 'ABC Ltd' matches requested name 'ABC Ltd'"
Bad: "Company name and requested name are both present in header"

Rule: "Max Credit Currency must be EUR or USD"
Good: "Max Credit Currency is GBP, violates EUR/USD requirement"
Bad: "Currency information is available in financial section"

Rule: "Gross Profit should be less than Total Income"
Good: "Cannot verify: Neither Gross Profit nor Total Income found in XML"
Bad: "Financial information is present in various sections"

Rule: "Spelling Check"
Good: "No spelling errors detected in report content"
Bad: "Company information is active with legal status details"

REMEMBER: Focus on accurate validation findings using natural language.
"""
        )


    def _detect_question_type(self, question: str) -> str:
        """Detect the type of validation question to improve accuracy."""
        question_lower = question.lower()
        
        if any(word in question_lower for word in ["should be", "must be", "should not", "must not"]):
            return "compliance_rule"
        elif any(word in question_lower for word in ["if", "when", "provided", "given"]):
            return "conditional_logic"
        elif any(word in question_lower for word in ["check", "verify", "ensure", "spelling"]):
            return "verification"
        elif any(word in question_lower for word in ["match", "same", "equal"]):
            return "comparison"
        elif any(word in question_lower for word in ["missing", "blank", "empty", "not present"]):
            return "presence_check"
        else:
            return "general_validation"

    def _determine_compliance_status(self, summary: str, question: str) -> str:
        """Determine status based on compliance validation result without explicit tags."""
        summary_lower = summary.lower()
        
        # Check for explicit positive indicators (rule followed)
        if any(pattern in summary_lower for pattern in [
            "matches", "match correctly", "correctly set", "properly configured",
            "no violations", "no errors", "no issues", "no problems",
            "requirement met", "rule followed", "complies with",
            "confirms", "verified", "valid", "correct"
        ]):
            return "approved"
        
        # Check for explicit negative indicators (rule violated)
        if any(pattern in summary_lower for pattern in [
            "violates", "violation", "does not match", "mismatch", "incorrectly",
            "error detected", "issue found", "problem identified",
            "requirement not met", "rule violated", "non-compliant",
            "missing required", "failed", "invalid"
        ]):
            return "rejected"
        
        # Check for uncertainty indicators (cannot verify)
        if any(pattern in summary_lower for pattern in [
            "cannot verify", "cannot determine", "insufficient data",
            "missing data", "not found", "not present", "unavailable",
            "unclear", "ambiguous", "incomplete information"
        ]):
            return "manual_intervention_needed"
        
        # Fallback to existing logic for unclear cases
        return self._determine_status_from_summary(summary, question)

    def _initialize_vector_store(self):
        """Initialize vector store connection."""
        try:
            from app.services.vector_store import VectorStore
            print("Initializing vector store connection...")
            vector_store = VectorStore()
            # (Suppress all connection status/warning prints)
            return vector_store
        except Exception as e:
            # Only print the initialization error if vector store cannot be created at all
            print(f"Vector store initialization warning: {e}")
            return None

    async def validate_report(self, report_id: str, validation_options: Dict[str, Any] = None, enable_client_filtering: bool = False, order_details_params: Dict[str, Any] = None, direct_client_code: str = None) -> Dict[str, Any]:
        """Validate XML report against questions and return results."""
        try:
            validation_id = str(uuid.uuid4())
            start_time = time.time()
            
            # Always use permanent question bank
            print("Using permanent question bank")
            questions = await self.file_processor.load_permanent_questions(force_reload=True)
            questions_source = "permanent_question_bank"
            actual_questions_file_id = "permanent"
            
            # Load report using report_id (resolves internally to file_id)
            report_data = await self.file_processor.get_processed_report_by_report_id(report_id)
            
            if not questions:
                raise Exception("Permanent question bank is enabled but no questions found. Please upload a permanent question bank first.")
            
            if not report_data:
                raise Exception("Report not found or not processed")
            
            print(f"Loaded {len(questions)} questions from {questions_source}")
            
            # Connect to vector store 
            if self.vector_store:
                print("Connecting to vector store ...")
                # Test vector store connection
                await self._vector_store_operations(report_data, questions)
            
            # Handle client filtering if enabled
            order_client_code = None
            # Don't filter questions - process all and mark as skipped if needed
            filtered_questions = questions
            skipped_questions = 0
            
            if enable_client_filtering:
                # Use direct client code if provided, otherwise fetch from API
                if direct_client_code:
                    order_client_code = direct_client_code
                    print(f"Using direct client code: {order_client_code}")
                elif order_details_params:
                    order_client_code = await self._fetch_order_client_code(order_details_params)
                    print(f"Fetched client code from API: {order_client_code}")
                
                if order_client_code:
                    print(f"Client filtering enabled. Order client code: {order_client_code}. All questions will be processed, non-matching ones marked as 'skipped'.")
                else:
                    print("Client filtering enabled but no client code available")
            
            # Reset tracked summaries for new validation
            self.processed_summaries.clear()
            
            # Process validation options
            validation_options = validation_options or {}
            batch_size = validation_options.get("batch_size", settings.DEFAULT_BATCH_SIZE)
            include_low_confidence = validation_options.get("include_low_confidence", True)
            min_confidence_threshold = validation_options.get("min_confidence_threshold", 0.3)
            
            # Validate questions
            validation_results = []
            total_questions = len(questions)
            filtered_total = len(filtered_questions)
            
            # Process questions in batches
            for i in range(0, filtered_total, batch_size):
                batch_questions = filtered_questions[i:i + batch_size]
                batch_start_number = i + 1
                batch_results = await self._validate_question_batch(
                    batch_questions, 
                    report_data,
                    batch_start_number,
                    enable_client_filtering,
                    order_client_code
                )
                
                # Filter results based on confidence threshold
                if not include_low_confidence:
                    batch_results = [
                        result for result in batch_results 
                        if result.confidence_score >= min_confidence_threshold
                    ]
                
                validation_results.extend(batch_results)
            
            # Count skipped questions for reporting
            skipped_questions = sum(1 for result in validation_results if result.status == "skipped")
            
            processing_time = time.time() - start_time
            
            # Create validation response
            validation_response = {
                "validation_id": validation_id,
                "status": ValidationStatus.COMPLETED,
                "questions_file_id": actual_questions_file_id,
                "questions_source": questions_source,
                "report_id": report_id,  # Changed from report_file_id to report_id
                "total_questions": total_questions,
                "processed_questions": len(validation_results),
                "skipped_questions": skipped_questions,
                "results": [result.model_dump() for result in validation_results],
                "validation_timestamp": datetime.now(),
                "processing_time": processing_time,
                "validation_options": validation_options,
                "client_filtering_enabled": enable_client_filtering,
                "order_client_code": order_client_code,
                "rag_enabled": self.vector_store is not None,  # Indicate RAG capability
                "permanent_question_bank_used": questions_source == "permanent_question_bank"
            }
            
            # Save validation results
            await self._save_validation_results(validation_id, validation_response)
            
            return validation_response
            
        except Exception as e:
            raise Exception(f"Error during validation: {str(e)}")

    async def _vector_store_operations(self, report_data: Dict[str, Any], questions: List[Question]):
        """Test vector store connection."""
        try:
            if not self.vector_store:
                return
            
            # Test vector store connection
            print("Testing vector store connection...")
            connection_info = self.vector_store.get_connection_info()
            
            if connection_info.get('status') == 'connected':
                print(f"✓ Vector store connected successfully")
                print(f"  - Mode: {connection_info.get('mode', 'unknown')}")
                print(f"  - Collections: {connection_info.get('collections_count', 0)}")
                print(f"  - Version: {connection_info.get('version', 'unknown')}")
            else:
                print(f"Vector store connection warning: {connection_info.get('error', 'unknown error')}")
            
            print("Vector store connection test completed")
            
        except Exception as e:
            print(f"Vector store connection warning: {e}")

    async def _validate_question_batch(self, questions: List[Question], report_data: Dict[str, Any], start_number: int = 1, enable_client_filtering: bool = False, order_client_code: str = None) -> List[ValidationResult]:
        """Validate a batch of questions."""
        tasks = []
        for i, question in enumerate(questions):
            question_number = start_number + i
            task = self._validate_single_question(question, report_data, question_number, enable_client_filtering, order_client_code)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions and convert to ValidationResult objects
        validation_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Create error result
                validation_results.append(ValidationResult(
                    question_id=questions[i].id,
                    question_number=start_number + i,
                    question=questions[i].question,
                    summary=f"Error processing question: {str(result)}",
                    confidence_score=0.0,
                    relevant_sections=[],
                    status="error"
                ))
            else:
                validation_results.append(result)
        
        return validation_results

    async def _validate_single_question(self, question: Question, report_data: Dict[str, Any], question_number: int, enable_client_filtering: bool = False, order_client_code: str = None) -> ValidationResult:
        """Validate a single question against the XML report."""
        try:
            # Client filtering check - always process question but mark as skipped if needed
            client_match_status = "no_client_code"
            
            if enable_client_filtering:
                if question.client_code:
                    # Question is client-specific
                    if not order_client_code:
                        # Client filtering enabled but no client code available - skip client-specific questions
                        client_match_status = "skipped"
                        return ValidationResult(
                            question_id=question.id,
                            question_number=question_number,
                            question=question.question,  # Use original question text only
                            summary=f"Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client '{question.client_code}'.",
                            confidence_score=0.0,
                            relevant_sections=[],
                            status="skipped",
                            client_match_status="skipped"
                        )
                    elif question.client_code.upper() != order_client_code.upper():
                        # Question is for a different client - mark as skipped
                        client_match_status = "skipped"
                        return ValidationResult(
                            question_id=question.id,
                            question_number=question_number,
                            question=question.question,  # Use original question text only
                            summary=f"Question skipped - Client code mismatch. Question is for client '{question.client_code}' but current report is for client '{order_client_code}'.",
                            confidence_score=0.0,
                            relevant_sections=[],
                            status="skipped",
                            client_match_status="skipped"
                        )
                    else:
                        # Question matches client code
                        client_match_status = "matched"
                else:
                    # Question has no client code - applies to all clients
                    client_match_status = "no_client_code"
            
            # Extract relevant XML content for this question
            xml_content = await self._extract_xml_content_for_question(report_data, question.question)
            
            # Build enhanced question text for LLM analysis (but use original question in response)
            enhanced_question_text = self._build_enhanced_question(question)
            
            if self.llm:
                # Use LLM for validation
                response = await self._generate_enhanced_llm_response(question, xml_content, question_number)
            else:
                # Fallback to rule-based validation
                response = self._generate_fallback_response(question, xml_content)
            
            # Parse response
            parsed_response = self._parse_llm_response(response)
            
            # Check for duplicate summary
            is_duplicate = self._check_for_duplicate_summary(parsed_response["summary"], question.question)
            if is_duplicate:
                # Regenerate response
                retry_response = await self._generate_enhanced_llm_response(question, xml_content, question_number, retry=True)
                parsed_response = self._parse_llm_response(retry_response)
            
            # Add summary to tracking
            self._add_summary_to_tracked(parsed_response["summary"])
            
            # Determine status
            status = self._determine_status_from_summary(parsed_response["summary"], question.question)
            
            return ValidationResult(
                question_id=question.id,
                question_number=question_number,
                question=question.question,  # Use original question text only
                summary=parsed_response["summary"],
                confidence_score=parsed_response["confidence_score"],
                relevant_sections=parsed_response["relevant_sections"],
                status=status,
                client_match_status=client_match_status
            )
            
        except Exception as e:
            return ValidationResult(
                question_id=question.id,
                question_number=question_number,
                question=question.question,  # Use original question text only
                summary=f"Error validating question: {str(e)}",
                confidence_score=0.0,
                relevant_sections=[],
                status="error",
                client_match_status=client_match_status if 'client_match_status' in locals() else "no_client_code"
            )

    async def _extract_xml_content_for_question(self, report_data: Dict[str, Any], question: str) -> str:
        """Extract relevant XML content for a specific question."""
        try:
            # Use LLM-based semantic search if LLM is available
            if self.llm:
                return await self._llm_semantic_extract_xml_content(report_data, question)
            else:
                # Fallback to direct extraction
                return self._direct_extract_xml_content(report_data, question)
            
        except Exception as e:
            return f"Error extracting XML content: {str(e)}"

    async def _llm_semantic_extract_xml_content(self, report_data: Dict[str, Any], question: str) -> str:
        """Use LLM to semantically extract relevant XML sections."""
        try:
            # Check if LLM semantic search is enabled
            if not settings.ENABLE_LLM_SEMANTIC_SEARCH or not self.llm:
                return self._direct_extract_xml_content(report_data, question)
            
            # Get the XML structure and text content
            xml_structure = report_data.get("xml_structure", {})
            text_content = report_data.get("text_content", [])
            
            # Prepare content for semantic analysis
            xml_str = self._dict_to_xml_string(xml_structure)
            
            # Create semantic search prompt with increased content limit
            content_sections = []
            for item in text_content[:50]:  # Increased limit for better semantic search
                if item.get("text"):
                    content_sections.append(f"Path: {item['path']}\nContent: {item['text']}")
            
            full_content = f"XML Structure:\n{xml_str}\n\nText Content:\n" + "\n\n".join(content_sections)
            
            # Use configured content limit for semantic search
            if len(full_content) > settings.SEMANTIC_SEARCH_CONTENT_LIMIT:
                return await self._llm_extract_relevant_sections(full_content, question)
            else:
                return full_content[:settings.SEMANTIC_SEARCH_CHUNK_SIZE]
                
        except Exception as e:
            # Fallback to direct extraction
            print(f"LLM semantic search failed: {e}")
            return self._direct_extract_xml_content(report_data, question)

    async def _llm_extract_relevant_sections(self, full_content: str, question: str) -> str:
        """Use LLM to extract only the most relevant sections from large XML content."""
        try:
            # Split content into manageable chunks using configured size
            chunks = self._split_content_into_chunks(full_content, settings.SEMANTIC_SEARCH_CHUNK_SIZE)
            
            # Enhanced semantic extraction prompt
            extraction_prompt = f"""
You are an expert semantic content extraction assistant specializing in XML document analysis. 

QUESTION: {question}

ANALYSIS INSTRUCTIONS:
1. Identify XML sections that are directly relevant to answering the question
2. Look for semantic relationships, not just keyword matches
3. Include context sections that provide supporting information
4. Consider business logic and domain-specific meanings
5. Exclude irrelevant sections to keep the response concise
6. Preserve the original XML structure and paths
7. Focus on content that would help answer the specific question asked

SEMANTIC MATCHING PRIORITIES:
- Direct content matches (highest priority)
- Contextually related content
- Supporting information and metadata
- Business logic relationships

XML CONTENT:
{chunks[0]}

Return only the extracted relevant content with preserved XML structure.
"""
            
            # Use LLM for semantic extraction
            messages = [
                SystemMessage(content="You are an expert semantic content extraction assistant. Extract only the most relevant XML sections for the given question using semantic understanding. Preserve original structure and paths."),
                HumanMessage(content=extraction_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            extracted_content = response.content
            
            # Process additional chunks if needed and configured
            if len(chunks) > 1 and len(chunks) <= settings.SEMANTIC_SEARCH_MAX_CHUNKS:
                # Process additional chunks if the first extraction seems insufficient
                if len(extracted_content) < settings.SEMANTIC_SEARCH_MIN_EXTRACTION_SIZE:
                    for chunk in chunks[1:settings.SEMANTIC_SEARCH_MAX_CHUNKS]:
                        additional_prompt = f"""
Continue semantic extraction for the question: {question}

Additional XML content to analyze:
{chunk}

SEMANTIC ANALYSIS REQUIREMENTS:
- Only include sections that add new relevant information not already covered
- Maintain semantic coherence with previously extracted content
- Focus on complementary information that enhances understanding
- Preserve XML structure and context

Return only new relevant content that complements the previous extraction.
"""
                        
                        additional_messages = [
                            SystemMessage(content="Continue semantic extraction. Only include new relevant information that complements previous extraction."),
                            HumanMessage(content=additional_prompt)
                        ]
                        
                        additional_response = await self.llm.ainvoke(additional_messages)
                        extracted_content += "\n\n" + additional_response.content
                        
                        if len(extracted_content) > settings.SEMANTIC_SEARCH_CHUNK_SIZE:
                            break
            
            return extracted_content[:settings.SEMANTIC_SEARCH_CHUNK_SIZE]
            
        except Exception as e:
            # Fallback to truncated content
            print(f"LLM semantic section extraction failed: {e}")
            return full_content[:settings.SEMANTIC_SEARCH_CHUNK_SIZE]

    def _split_content_into_chunks(self, content: str, chunk_size: int) -> List[str]:
        """Split content into chunks while trying to preserve XML structure."""
        chunks = []
        
        # First try to split by XML sections (double newlines)
        sections = content.split('\n\n')
        
        current_chunk = ""
        for section in sections:
            if len(current_chunk) + len(section) + 2 <= chunk_size:
                current_chunk += section + "\n\n"
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = section + "\n\n"
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # If chunks are still too large, split by lines
        if chunks and max(len(chunk) for chunk in chunks) > chunk_size:
            refined_chunks = []
            for chunk in chunks:
                if len(chunk) <= chunk_size:
                    refined_chunks.append(chunk)
                else:
                    # Split large chunk by lines
                    lines = chunk.split('\n')
                    current_line_chunk = ""
                    for line in lines:
                        if len(current_line_chunk) + len(line) + 1 <= chunk_size:
                            current_line_chunk += line + "\n"
                        else:
                            if current_line_chunk:
                                refined_chunks.append(current_line_chunk.strip())
                            current_line_chunk = line + "\n"
                    if current_line_chunk:
                        refined_chunks.append(current_line_chunk.strip())
            chunks = refined_chunks
        
        return chunks

    def _direct_extract_xml_content(self, report_data: Dict[str, Any], question: str) -> str:
        """Direct extraction method (original implementation)."""
        try:
            # Get the XML structure and text content
            xml_structure = report_data.get("xml_structure", {})
            text_content = report_data.get("text_content", [])
            
            # Convert XML structure to string representation
            xml_str = self._dict_to_xml_string(xml_structure)
            
            # Add text content sections
            content_sections = []
            for item in text_content[:10]:  # Limit to first 10 sections for performance
                if item.get("text"):
                    content_sections.append(f"Path: {item['path']}\nContent: {item['text']}")
            
            # Combine all content
            full_content = f"XML Structure:\n{xml_str}\n\nText Content:\n" + "\n\n".join(content_sections)
            
            return full_content[:8000]  # Limit content length for LLM
            
        except Exception as e:
            return f"Error extracting XML content: {str(e)}"

    def _dict_to_xml_string(self, data: Dict[str, Any], indent: int = 0) -> str:
        """Convert dictionary to XML-like string representation."""
        if not isinstance(data, dict):
            return str(data)
        
        result = []
        for key, value in data.items():
            if isinstance(value, dict):
                result.append("  " * indent + f"<{key}>")
                result.append(self._dict_to_xml_string(value, indent + 1))
                result.append("  " * indent + f"</{key}>")
            elif isinstance(value, list):
                for item in value:
                    result.append("  " * indent + f"<{key}>")
                    if isinstance(item, dict):
                        result.append(self._dict_to_xml_string(item, indent + 1))
                    else:
                        result.append("  " * (indent + 1) + str(item))
                    result.append("  " * indent + f"</{key}>")
            else:
                result.append("  " * indent + f"<{key}>{value}</{key}>")
        
        return "\n".join(result)

    def _build_enhanced_question(self, question: Question) -> str:
        """Build enhanced question text with additional context from new fields."""
        enhanced_parts = [question.question]
        
        if question.darwin_reference_sections:
            enhanced_parts.append(f"[Reference Sections: {question.darwin_reference_sections}]")
        
        if question.expected_outcome:
            enhanced_parts.append(f"[Expected Outcome: {question.expected_outcome}]")
        
        if question.client_specific_type:
            enhanced_parts.append(f"[Client Type: {question.client_specific_type}]")
        
        return " ".join(enhanced_parts)

    async def _generate_enhanced_llm_response(self, question: Question, xml_content: str, question_number: int, retry: bool = False) -> str:
        """Generate enhanced LLM response using new question fields for better context."""
        try:
            # Build enhanced prompt with additional context
            enhanced_prompt = self._build_enhanced_validation_prompt(question, xml_content, question_number, retry)
            
            # Enhanced system message for compliance validation
            system_prompt = """You are an expert financial and business report analyst specializing in XML document compliance validation.

Your expertise includes:
- Financial statements analysis (balance sheets, P&L, cash flow)
- Corporate structure and governance
- Legal and regulatory compliance
- Business entity information
- Professional service providers (lawyers, accountants)
- Related entities and subsidiaries

Key principles:
1. Focus on COMPLIANCE CHECKING rather than information extraction
2. Use Darwin reference sections to focus your analysis on the right areas
3. Align your findings with the expected outcome when provided
4. Report COMPLIANT/VIOLATION/CANNOT-VERIFY with specific findings
5. Each response must be unique and contain different compliance results
6. Always respond with valid JSON format only
7. Be precise and concise - maximum 150 characters for summaries

Focus on delivering specific compliance validation results from the XML data while considering the provided reference sections and expected outcomes."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=enhanced_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content
            
        except Exception as e:
            raise Exception(f"Error generating enhanced LLM response: {str(e)}")

    def _build_enhanced_validation_prompt(self, question: Question, xml_content: str, question_number: int, retry: bool = False) -> str:
        """Build enhanced validation prompt incorporating new question fields with natural language compliance focus."""
        
        base_prompt = f"""You are an XML report compliance validator. Check if the XML data follows the validation rule and provide accurate findings.

VALIDATION RULE: {question.question}"""
        
        # Add Darwin reference sections if available
        if question.darwin_reference_sections:
            base_prompt += f"""

FOCUS AREAS (Darwin Reference Sections): {question.darwin_reference_sections}
Pay special attention to these sections when checking compliance."""
        
        # Add expected outcome if available
        if question.expected_outcome:
            base_prompt += f"""

EXPECTED OUTCOME: {question.expected_outcome}
Verify if the XML data meets this expected validation outcome."""
        
        # Add client-specific information if available
        if question.client_specific_type:
            base_prompt += f"""

CLIENT SCOPE: {question.client_specific_type}
Apply validation rules considering the client-specific requirements."""
        
        # Add retry instruction if this is a retry
        if retry:
            base_prompt += """

RETRY INSTRUCTION: Provide a different validation finding to avoid duplicating previous responses."""
        
        base_prompt += f"""

XML DATA TO CHECK:
{xml_content}

VALIDATION APPROACH:
1. UNDERSTAND THE RULE: What specific requirement is being checked?
2. FIND RELEVANT DATA: Look for XML elements needed to verify the rule
3. CHECK COMPLIANCE: Does the data meet the requirement or violate it?
4. REPORT FINDINGS: State the validation result clearly and specifically

CRITICAL REQUIREMENTS:
1. ACCURACY FOCUS: Provide precise validation findings, not data descriptions
2. SPECIFIC RESULTS: State exactly what was checked and the outcome
3. NATURAL LANGUAGE: Use clear, professional language without tags
4. ONE-LINER: Provide finding in one sentence (max 150 characters)
5. ACTIONABLE: Make it clear if rule is met, violated, or cannot be verified

RESPONSE FORMAT - JSON only:
{{
    "summary": "Clear validation finding with specific details (max 150 characters)",
    "confidence_score": 0.95,
    "relevant_sections": ["xml_path1", "xml_path2"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Brief justification for the validation result"
}}

STATUS GUIDELINES:
- "approved": Rule is followed correctly / No violations found
- "rejected": Rule is violated / Non-compliant condition exists
- "manual_intervention_needed": Cannot verify due to missing data or unclear rule

REMEMBER: Focus on accurate validation findings using natural language."""
        
        return base_prompt

    async def _generate_llm_response(self, question: str, xml_content: str, question_number: int) -> str:
        """Generate response using LLM."""
        try:
            prompt = self.validation_prompt.format(
                question=question,
                xml_content=xml_content,
                question_number=question_number
            )
            
            # Enhanced system message for one-liner summaries
            system_prompt = """You are an expert financial and business report analyst specializing in XML document analysis.

Your expertise includes:
- Financial statements analysis (balance sheets, P&L, cash flow)
- Corporate structure and governance
- Legal and regulatory compliance
- Business entity information
- Professional service providers (lawyers, accountants)
- Related entities and subsidiaries

Key principles:
1. Extract EXACT values, names, numbers, and dates from the XML content
2. Provide ONE-LINER summaries with specific factual information
3. Each response must be unique and contain different specific data
4. Always respond with valid JSON format only
5. Be precise and concise - maximum 150 characters for summaries

Focus on delivering specific, factual one-liner summaries from the XML data."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content
            
        except Exception as e:
            raise Exception(f"Error generating LLM response: {str(e)}")

    def _generate_fallback_response(self, question: Question, xml_content: str) -> str:
        """Generate fallback response without LLM."""
        if not xml_content:
            summary = "No XML content available for analysis"
            return json.dumps({
                "summary": summary,
                "confidence_score": 0.1,
                "relevant_sections": [],
                "status": self._determine_status_from_summary(summary, question.question),
                "reasoning": "No LLM available and no XML content found"
            })
        
        # Simple keyword matching for one-liner summary
        summary = f"XML content available but requires LLM processing"
        
        return json.dumps({
            "summary": summary,
            "confidence_score": 0.3,
            "relevant_sections": [],
            "status": "manual_intervention_needed",
            "reasoning": "Generated using fallback method without LLM"
        })

    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response to extract structured data and clean up formatting."""
        try:
            # Clean up the response first
            clean_response = response.strip()
            
            # Remove markdown code blocks if present
            if clean_response.startswith("```json"):
                clean_response = clean_response.replace("```json", "").replace("```", "").strip()
            elif clean_response.startswith("```"):
                clean_response = clean_response.replace("```", "").strip()
            
            # Try to parse as JSON
            parsed = json.loads(clean_response)
            
            # Clean up the summary field to remove any JSON artifacts
            if "summary" in parsed:
                summary = parsed["summary"].strip()
                # Remove any nested JSON formatting in the summary
                if summary.startswith('```json') or summary.startswith('{'):
                    # If summary contains JSON, try to extract just the summary value
                    try:
                        if summary.startswith('```json'):
                            json_part = summary.replace('```json', '').replace('```', '').strip()
                            nested_json = json.loads(json_part)
                            summary = nested_json.get("summary", summary)
                        elif summary.startswith('{'):
                            nested_json = json.loads(summary)
                            summary = nested_json.get("summary", summary)
                    except:
                        # If parsing fails, keep original but clean it
                        summary = summary.replace('```json', '').replace('```', '').replace('{', '').replace('}', '').strip()
                
                # Enforce 150 character limit for one-liner summaries
                if len(summary) > 150:
                    # Find the last complete sentence or phrase within 150 characters
                    truncated = summary[:147]
                    if '.' in truncated:
                        summary = truncated[:truncated.rfind('.') + 1]
                    else:
                        summary = truncated + "..."
                
                parsed["summary"] = summary
            
            # Use compliance-aware status determination if status not provided
            if "status" not in parsed or not parsed["status"]:
                if "summary" in parsed:
                    parsed["status"] = self._determine_compliance_status(parsed["summary"], "")
            
            # Ensure we have 'summary' field for backward compatibility
            if "answer" in parsed and "summary" not in parsed:
                parsed["summary"] = parsed["answer"]
                
            return parsed
            
        except json.JSONDecodeError:
            # Fallback parsing
            clean_text = response.replace('```json', '').replace('```', '').strip()
            return {
                "summary": clean_text,
                "confidence_score": 0.5,
                "relevant_sections": [],
                "status": "manual_intervention_needed",
                "reasoning": "Unable to parse structured response"
            }


    async def _save_validation_results(self, validation_id: str, validation_data: Dict[str, Any]):
        """Save validation results to file."""
        try:
            results_path = self.file_processor.processed_path / f"{validation_id}_validation.json"
            
            # Convert datetime objects to strings for JSON serialization
            serializable_data = json.loads(json.dumps(validation_data, default=str))
            
            with open(results_path, "w") as f:
                json.dump(serializable_data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving validation results: {str(e)}")

    async def get_validation_results(self, validation_id: str) -> Optional[Dict[str, Any]]:
        """Get validation results by ID."""
        try:
            results_path = self.file_processor.processed_path / f"{validation_id}_validation.json"
            if results_path.exists():
                with open(results_path, "r") as f:
                    return json.load(f)
            return None
        except Exception:
            return None 

    def _check_for_duplicate_summary(self, summary: str, question_text: str) -> bool:
        """Check if a summary is too similar to previously processed summaries."""
        if not summary or len(summary) < 10:
            return False
            
        # Normalize the summary for comparison
        normalized_summary = summary.lower().strip()
        
        # Check against previously processed summaries
        for existing_summary in self.processed_summaries:
            if self._calculate_similarity(normalized_summary, existing_summary) > 0.8:
                return True
        
        return False
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts using simple word overlap."""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _add_summary_to_tracked(self, summary: str):
        """Add a summary to the tracked set."""
        if summary:
            self.processed_summaries.add(summary.lower().strip())

    def _determine_status_from_summary(self, summary: str, question: str) -> str:
        """Determine status based on summary content if not provided by LLM."""
        if not summary:
            return "manual_intervention_needed"
        
        summary_lower = summary.lower()
        question_lower = question.lower()
        
        # Check for insufficient data indicators first
        insufficient_patterns = [
            "limited", "partial", "insufficient", "unclear", "ambiguous", "vague",
            "cannot determine", "unable to determine", "not enough", "not sufficient",
            "requires clarification", "needs more information", "incomplete data",
            "missing context", "lacks detail", "too vague", "clarification needed"
        ]
        
        if any(pattern in summary_lower for pattern in insufficient_patterns):
            return "manual_intervention_needed"
        
        # Check for explicit positive statements (no problems found)
        positive_patterns = [
            "no error", "no errors", "no issue", "no issues", "no problem", "no problems",
            "no warning", "no warnings", "no duplicate", "no duplicates", "not found",
            "zero errors", "0 errors", "successful", "complete", "completed", "compliant",
            "approved", "valid", "good", "excellent", "satisfactory", "confirmed", "verified"
        ]
        
        if any(pattern in summary_lower for pattern in positive_patterns):
                    return "approved"
        
        # Check for explicit negative statements
        negative_patterns = [
            "error", "errors", "failed", "failure", "issue", "issues", "problem", "problems",
            "missing", "incomplete", "invalid", "non-compliant", "violation", "violations",
            "warning", "warnings", "rejected", "declined", "denied", "poor", "bad",
            "duplicate", "duplicates", "discrepancy", "discrepancies", "mismatch"
        ]
        
        if any(pattern in summary_lower for pattern in negative_patterns):
            return "rejected"
        
        # For very short summaries or single-word questions, require manual intervention
        if len(summary_lower.split()) < 3 or question_lower.strip() in ["all", "client", "quality", "status"]:
            return "manual_intervention_needed"
        
        # Default to manual intervention if unclear
        return "manual_intervention_needed"

    async def _fetch_order_client_code(self, order_details_params: Dict[str, Any]) -> Optional[str]:
        """
        Fetch client code from order details API.
        
        Expected API response structure:
        {
            "clientId": 383,
            "clientShortCode": "0447",  # This is the primary client code field
            "clientReference": "tudip1Report",
            "country": "India",
            "companyName": "tudip1",
            ...
        }
        """
        try:
            import httpx
            
            # API configuration
            api_url = f"https://wapaz-uks-uta-icpcertsol-api01.azurewebsites.net/api/order/getOrderDetails?csr={order_details_params['csr_id']}&copy={order_details_params['copy']}&version={order_details_params['version']}"
            headers = {
                "Authorization": f"Bearer {order_details_params['bearer_token']}",
                "Content-Type": "application/json"
            }
            
            # Fetch data from external API
            async with httpx.AsyncClient(timeout=30.0) as client:
                api_response = await client.get(api_url, headers=headers)
                
                if api_response.status_code != 200:
                    print(f"Error fetching order details: {api_response.status_code} - {api_response.text}")
                    return None
                
                # Parse the API response
                try:
                    api_data = api_response.json()
                except Exception as e:
                    print(f"Error parsing order details response: {str(e)}")
                    return None
                
                # Extract client code from response
                client_code = None
                # Updated to prioritize clientShortCode based on API response structure
                possible_client_keys = [
                    'clientShortCode',  # Primary field based on API response
                    'clientCode', 'client_code', 'ClientCode', 'Client_Code',
                    'client', 'Client', 'clientId', 'ClientId', 'client_id'
                ]
                
                # Search for client code in the response
                if isinstance(api_data, dict):
                    for key in possible_client_keys:
                        if key in api_data:
                            client_code = str(api_data[key])
                            break
                    
                    # If not found in root, search nested objects
                    if not client_code:
                        for key, value in api_data.items():
                            if isinstance(value, dict):
                                for nested_key in possible_client_keys:
                                    if nested_key in value:
                                        client_code = str(value[nested_key])
                                        break
                                if client_code:
                                    break
                
                return client_code
                
        except Exception as e:
            print(f"Error fetching order client code: {str(e)}")
            return None
    
    def _filter_questions_by_client(self, questions: List[Question], order_client_code: Optional[str]) -> tuple[List[Question], int]:
        """Filter questions based on client code matching."""
        if not order_client_code:
            # If no order client code, process all questions
            return questions, 0
        
        filtered_questions = []
        skipped_count = 0
        
        for question in questions:
            if question.client_code:
                # If question has client code, only include if it matches
                if question.client_code.strip() == order_client_code.strip():
                    filtered_questions.append(question)
                else:
                    skipped_count += 1
            else:
                # If question has no client code, include it (applies to all clients)
                filtered_questions.append(question)
        
        return filtered_questions, skipped_count 