{"validation_id": "0057b11a-7129-4b6e-bc93-6d4c35b5d7ab", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181489", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "5f3d1544-655e-423c-ae1e-0759c0e365c7", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "fcc003f0-606d-4cd8-be41-fb08382db8fe", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "4a8cd180-6f79-47ad-8722-5b5d97d317cb", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing, cannot verify comments and payment compliance", "confidence_score": 0.9, "relevant_sections": ["OrderDetails/ClientName", "SpecialNotes/TextForSpecificClients", "Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "69c7864d-020b-4115-9536-08a6ce8b1299", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name missing, but Payment Section note 'test' present", "confidence_score": 0.95, "relevant_sections": ["Order Details/Client Name", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a1a166aa-5725-4515-b134-3ebed47ffd64", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income; data missing in XML", "confidence_score": 0.9, "relevant_sections": ["FinancialSection/ProfitAndLoss", "FinancialSection/TotalIncome", "FinancialSection/TotalGrossProfit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9f691cf0-9657-4c25-aea6-2d91bc6d4098", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials in 'Operating Costs', violates Direct Costs rule", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d7ce77ff-0d4f-4eaf-91f1-f63cb89a2946", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name missing, cannot verify compliance with 'AishwaryaTest'", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "508cd133-c1d4-4c16-8c59-95b69e383ae5", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address '456 Secondary St' is inactive; comment populated.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressSection/Address", "Report/ClientSection/ClientAddress", "Report/ClientSection/ClientSpecificComment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "359ab7ce-987c-4797-9a02-5b6dde8f9e39", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max Credit Currency 'AMD' not EUR/USD/GBP, violates special note rule", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e434708f-4cb8-4c5d-a6cb-dd9292158c67", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/License numbers missing in LegalSection/SpecialNote", "confidence_score": 0.95, "relevant_sections": ["Report/LegalSection", "Report/SpecialNote"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fa307c05-7a67-4d59-a197-c45ac7f6c918", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1159. Please try again in 2.318s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "db0e9305-c08b-40c1-b237-62506c52e743", "question_number": 12, "question": "Spelling Check", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1380. Please try again in 2.76s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "2f739578-ebf9-48d3-8453-5ec9b9323e2d", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1080. Please try again in 2.16s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4929f065-64aa-4a3f-8d85-da3db4c693ef", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2960. Please try again in 5.92s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "3ba20e9d-2e93-49fc-9e3c-92910c185f81", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 923. Please try again in 1.846s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "a6838bb3-2dec-4732-aec8-d4139158c2cd", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1103. Please try again in 2.206s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "2b4ec155-3310-410f-9999-4e58795463c3", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1390. Please try again in 2.78s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "a538de55-ad7d-4240-9de0-7ecd9d29f384", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1169. Please try again in 2.338s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4bdb917f-bea0-445a-83a4-ab884af5faaa", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "TRA 'Average' not 'High', no editor notification needed", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2b74fb75-1da8-4707-be0a-4a8eaf3dfee4", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 944. Please try again in 1.887s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "9b2567c7-571f-4b88-9738-929d6faf575d", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "TRA 'Average' found, no classification for dormant/ceased trading", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "753fb976-585f-4aac-882c-886d3905b92b", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License '123456' not listed as Branch, no special note for transfer.", "confidence_score": 0.95, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/LicenseNumber", "Report/SpecialNotesSection/SpecialNote"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2a42faaf-d405-41e5-b40c-1aa4badea68e", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration numbers section missing, cannot verify compliance", "confidence_score": 0.9, "relevant_sections": ["Report/Legal/RegistrationNumbers", "Report/Comments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ea733809-8bbc-4462-a4dd-e214685ef94f", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28080, Requested 2841. Please try again in 1.842s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d4a3dbb5-7fc8-4d7f-b957-2adb19fa0f22", "question_number": 25, "question": "Large with small amount", "summary": "CreditOpinion 'Large' with Total Current Assets '79987.00' violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2e44682d-bffe-4827-8f65-78924d2c71b4", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not found, cannot verify compliance", "confidence_score": 0.85, "relevant_sections": ["Personnel/Position", "PositionClassifications"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "9a4b00f2-a31d-40fb-871a-b5feec747bc9", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29918, Requested 1059. Please try again in 1.954s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c2627a89-66e0-41b7-8ec9-fa792ba4213f", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type 'Decline' violates 'No Contact' requirement", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotes/PersonInterviewed"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2cdb97a9-0d3b-42e4-948d-f0afebb6a1fc", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type 'Decline' lacks comment in Payments section.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/PaymentsSection/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "06e8bccc-f6ae-4ac4-bf3f-9d646f34583f", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27955, Requested 2984. Please try again in 1.878s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "ba748937-c217-4a64-8543-06b28bf60a4a", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanction search tag missing, violates requirement", "confidence_score": 0.95, "relevant_sections": ["Report/Sanction"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ec382344-6bdd-47bb-aefa-750f37138c38", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1014. Please try again in 2.027s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "168769f3-b774-4991-8860-763efed4f4e5", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "a1c856d8-8b76-4202-9bfd-d838eab009a7", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in FinancialSection/BalanceSheets", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0c3d95d9-68e4-4f0d-a1d2-9cda5948e789", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1009. Please try again in 2.018s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "9954123b-a17c-4052-bef8-6ffebe9a986e", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 992. Please try again in 1.984s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "3a76d37f-b8bd-4614-97f6-1a282b824005", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2853. Please try again in 5.706s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "67a4a52d-613c-4ce4-bd91-8cc197e9857d", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2901. Please try again in 5.802s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "60cbcc79-4876-4f9a-a686-20724f6cbc18", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 990. Please try again in 1.98s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "714ce495-8d09-42c0-9cf3-c0b57137d73c", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 950. Please try again in 1.9s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "ab148d1b-b457-419a-910a-ed779c89bc3e", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status unknown, credit granted; notify editor", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 17:13:11.460962", "processing_time": 147.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}