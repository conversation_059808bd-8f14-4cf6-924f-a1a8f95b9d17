#!/usr/bin/env python3
"""
Debug expiry dates for report 1981692 to check if dates are actually expired.
"""

import sys
import os
import asyncio
from datetime import datetime, date
import re

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor

def parse_date(date_str):
    """Parse various date formats and return a date object."""
    if not date_str or date_str.strip() == "":
        return None
    
    date_str = date_str.strip()
    
    # Common date formats
    formats = [
        "%d-%b-%Y",    # 14-Aug-2026 (DD-MMM-YYYY)
        "%d/%m/%Y",    # 31/12/2025
        "%d-%m-%Y",    # 31-12-2025
        "%Y-%m-%d",    # 2025-12-31
        "%d.%m.%Y",    # 31.12.2025
        "%d %m %Y",    # 31 12 2025
        "%m/%d/%Y",    # 12/31/2025
        "%Y/%m/%d",    # 2025/12/31
        "%d-%B-%Y",    # 14-August-2026 (DD-MMMM-YYYY)
        "%d %b %Y",    # 14 Aug 2026
        "%d %B %Y",    # 14 August 2026
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    return None

async def debug_expiry_dates():
    """Debug expiry dates for report 1981692."""
    print("Debugging Expiry Dates for Report 1981692")
    print("=" * 50)
    
    current_date = date.today()
    print(f"Current date: {current_date} ({current_date.strftime('%d/%m/%Y')})")
    print()
    
    file_processor = FileProcessor()
    validation_service = ValidationService()
    
    try:
        # Get the report data
        report_data = await file_processor.get_processed_report_by_report_id("1981692")
        
        if not report_data:
            print("❌ Report 1981692 not found")
            return
        
        print("✅ Report 1981692 found")
        print(f"Available keys: {list(report_data.keys())}")
        print()
        
        # Get the XML content
        xml_data = report_data.get('xml_data', {})
        if not xml_data:
            xml_data = report_data.get('xml_structure', {})
        
        if xml_data:
            full_xml_content = validation_service._dict_to_xml_string(xml_data)
            print(f"XML content length: {len(full_xml_content)} characters")
            
            # Look for LegalStatusSection and RegistrationNumbers
            legal_section_start = full_xml_content.find('<LegalStatusSection>')
            if legal_section_start != -1:
                legal_section_end = full_xml_content.find('</LegalStatusSection>', legal_section_start)
                if legal_section_end != -1:
                    legal_section = full_xml_content[legal_section_start:legal_section_end + 21]
                    print("\n✅ LegalStatusSection found:")
                    print("-" * 60)
                    print(legal_section)
                    print("-" * 60)
                    
                    # Look for registration numbers and expiry dates
                    registration_pattern = r'<RegistrationNumber[^>]*>(.*?)</RegistrationNumber>'
                    expiry_pattern = r'<ExpiryDate[^>]*>(.*?)</ExpiryDate>'
                    comment_pattern = r'<Comment[^>]*>(.*?)</Comment>'
                    
                    registration_matches = re.findall(registration_pattern, legal_section, re.DOTALL)
                    expiry_matches = re.findall(expiry_pattern, legal_section, re.DOTALL)
                    comment_matches = re.findall(comment_pattern, legal_section, re.DOTALL)
                    
                    print(f"\n📋 Registration Analysis:")
                    print(f"  Registration numbers found: {len(registration_matches)}")
                    print(f"  Expiry dates found: {len(expiry_matches)}")
                    print(f"  Comments found: {len(comment_matches)}")
                    print()
                    
                    if registration_matches:
                        print("📝 Registration Numbers:")
                        for i, reg_num in enumerate(registration_matches, 1):
                            print(f"  {i}. {reg_num.strip()}")
                    
                    if expiry_matches:
                        print("\n📅 Expiry Dates Analysis:")
                        for i, expiry_date in enumerate(expiry_matches, 1):
                            expiry_date = expiry_date.strip()
                            print(f"  {i}. Raw expiry date: '{expiry_date}'")
                            
                            parsed_date = parse_date(expiry_date)
                            if parsed_date:
                                print(f"     Parsed date: {parsed_date} ({parsed_date.strftime('%d/%m/%Y')})")
                                
                                if parsed_date < current_date:
                                    days_expired = (current_date - parsed_date).days
                                    print(f"     ❌ EXPIRED: {days_expired} days ago")
                                elif parsed_date > current_date:
                                    days_until_expiry = (parsed_date - current_date).days
                                    print(f"     ✅ VALID: Expires in {days_until_expiry} days")
                                else:
                                    print(f"     ⚠️ EXPIRES TODAY")
                            else:
                                print(f"     ❌ Could not parse date format")
                            print()
                    
                    if comment_matches:
                        print("💬 Comments:")
                        for i, comment in enumerate(comment_matches, 1):
                            print(f"  {i}. {comment.strip()}")
                        print()
                    
                    # Check for specific expiry-related patterns
                    print("🔍 Searching for expiry-related content:")
                    expiry_keywords = ['expir', 'expire', 'expired', 'expiry', 'valid', 'invalid']
                    legal_lower = legal_section.lower()
                    
                    for keyword in expiry_keywords:
                        if keyword in legal_lower:
                            print(f"  ✅ Found keyword: '{keyword}'")
                            # Show context around the keyword
                            index = legal_lower.find(keyword)
                            start = max(0, index - 50)
                            end = min(len(legal_section), index + 50)
                            context = legal_section[start:end].replace('\n', ' ').replace('\t', ' ')
                            print(f"     Context: ...{context}...")
                    
                    # Look for DateExpired specifically
                    print("\n📆 DateExpired Analysis:")
                    date_expired_pattern = r'<DateExpired[^>]*>(.*?)</DateExpired>'
                    date_expired_matches = re.findall(date_expired_pattern, legal_section, re.DOTALL)

                    if date_expired_matches:
                        for i, date_str in enumerate(date_expired_matches, 1):
                            date_str = date_str.strip()
                            print(f"  {i}. DateExpired: '{date_str}'")

                            parsed = parse_date(date_str)
                            if parsed:
                                if parsed < current_date:
                                    days_expired = (current_date - parsed).days
                                    print(f"     ❌ EXPIRED: {days_expired} days ago")
                                elif parsed > current_date:
                                    days_until_expiry = (parsed - current_date).days
                                    print(f"     ✅ VALID: Expires in {days_until_expiry} days")
                                else:
                                    print(f"     ⚠️ EXPIRES TODAY")
                            else:
                                print(f"     ❌ Could not parse date")
                    else:
                        print("  No DateExpired tags found")

                    # Look for any date patterns in the legal section
                    print("\n📆 All date patterns found:")
                    date_patterns = [
                        r'\d{1,2}[/-]\d{1,2}[/-]\d{4}',  # DD/MM/YYYY or DD-MM-YYYY
                        r'\d{4}[/-]\d{1,2}[/-]\d{1,2}',  # YYYY/MM/DD or YYYY-MM-DD
                        r'\d{1,2}\.\d{1,2}\.\d{4}',      # DD.MM.YYYY
                    ]

                    all_dates = []
                    for pattern in date_patterns:
                        matches = re.findall(pattern, legal_section)
                        all_dates.extend(matches)

                    if all_dates:
                        for i, date_str in enumerate(set(all_dates), 1):
                            parsed = parse_date(date_str)
                            if parsed:
                                status = "EXPIRED" if parsed < current_date else "VALID"
                                print(f"  {i}. {date_str} → {parsed} ({status})")
                    else:
                        print("  No date patterns found")
                
                else:
                    print("❌ LegalStatusSection start found but no end tag")
            else:
                print("❌ LegalStatusSection not found in XML")
                
                # Search for any registration-related content
                print("\n🔍 Searching for registration-related content in entire XML:")
                xml_lower = full_xml_content.lower()
                registration_keywords = ['registration', 'expiry', 'expire', 'expired', 'comment']
                
                for keyword in registration_keywords:
                    if keyword in xml_lower:
                        print(f"  ✅ Found keyword: '{keyword}'")
                        index = xml_lower.find(keyword)
                        start = max(0, index - 100)
                        end = min(len(full_xml_content), index + 100)
                        context = full_xml_content[start:end].replace('\n', ' ').replace('\t', ' ')
                        print(f"     Context: ...{context}...")
                        print()
        else:
            print("❌ No XML data found")
        
    except Exception as e:
        print(f"❌ Error debugging expiry dates: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_expiry_dates())
