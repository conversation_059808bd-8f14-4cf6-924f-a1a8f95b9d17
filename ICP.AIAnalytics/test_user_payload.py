#!/usr/bin/env python3
"""
Test the user's exact payload to see if credit validation works.
"""

import sys
import os
import asyncio
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService

async def test_user_payload():
    """Test the user's exact payload."""
    print("Testing User's Exact Payload")
    print("=" * 50)
    
    # User's payload - Report 1981692 (Expiry date validation issue)
    payload = {
        "report_id": "1981692",
        "validation_options": {},
        "enable_client_filtering": True,
        "order_details_params": {
            "csr_id": "91347797",
            "copy": "2",
            "version": "1"
        },
        "direct_client_code": "",
        "bearer_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nU6vPh5iSrAWw27Fax4ks7oZS1jlXfAMSghWkweeyXE"
    }
    
    print(f"Report ID: {payload['report_id']}")
    print()
    
    # Initialize validation service
    validation_service = ValidationService()
    
    try:
        print("Step 1: Attempting validation with user's payload...")
        
        # Try to validate using the exact payload
        result = await validation_service.validate_report(
            report_id=payload["report_id"],
            validation_options=payload["validation_options"],
            enable_client_filtering=payload["enable_client_filtering"],
            order_details_params=payload["order_details_params"],
            direct_client_code=payload["direct_client_code"],
            bearer_token=payload["bearer_token"]
        )
        
        print("✅ Validation completed successfully!")
        print(f"Total questions processed: {result['processed_questions']}")
        print(f"Skipped questions: {result['skipped_questions']}")
        print()
        
        # Look for credit validation results
        credit_results = []
        for result_data in result['results']:
            question = result_data['question'].lower()
            if 'credit' in question and ('company' in question or 'size' in question):
                credit_results.append(result_data)
        
        print(f"Step 2: Found {len(credit_results)} credit validation results:")
        for i, credit_result in enumerate(credit_results, 1):
            print(f"\nCredit Result {i}:")
            print(f"  Question: {credit_result['question']}")
            print(f"  Summary: {credit_result['summary']}")
            print(f"  Status: {credit_result['status']}")
            print(f"  Reasoning: {credit_result.get('reasoning', 'N/A')}")
            print(f"  Confidence: {credit_result['confidence_score']}")
            
            # Check if currency conversion is mentioned
            reasoning = credit_result.get('reasoning', '') or ''
            summary = credit_result['summary'] or ''

            reasoning_lower = reasoning.lower()
            summary_lower = summary.lower()

            has_currency = 'xpf' in reasoning_lower or 'gbp' in reasoning_lower or 'xpf' in summary_lower or 'gbp' in summary_lower
            has_conversion = '×' in reasoning or '*' in reasoning or 'convert' in reasoning_lower
            
            print(f"  Currency mentioned: {'✅' if has_currency else '❌'}")
            print(f"  Conversion shown: {'✅' if has_conversion else '❌'}")
        
        if not credit_results:
            print("❌ No credit validation results found!")
            print("This suggests the questions might not be in the permanent question bank.")
            
            # Show all questions for debugging
            print("\nAll questions processed:")
            for i, result_data in enumerate(result['results'][:5], 1):  # Show first 5
                print(f"  {i}. {result_data['question']}")
            if len(result['results']) > 5:
                print(f"  ... and {len(result['results']) - 5} more")
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        
        # Check if the report exists
        try:
            from app.services.file_processor import FileProcessor
            file_processor = FileProcessor()
            report_data = await file_processor.get_processed_report_by_report_id(payload["report_id"])
            
            if report_data:
                print("✅ Report data found")
                print(f"Report keys: {list(report_data.keys())}")
                
                # Check if it has credit information
                xml_data = report_data.get('xml_data', {})
                if xml_data:
                    # Look for payment/credit sections
                    def find_credit_info(data, path=""):
                        credit_info = []
                        if isinstance(data, dict):
                            for key, value in data.items():
                                current_path = f"{path}/{key}" if path else key
                                if 'credit' in key.lower() or 'payment' in key.lower():
                                    credit_info.append(f"{current_path}: {value}")
                                credit_info.extend(find_credit_info(value, current_path))
                        elif isinstance(data, list):
                            for i, item in enumerate(data):
                                credit_info.extend(find_credit_info(item, f"{path}[{i}]"))
                        return credit_info
                    
                    credit_info = find_credit_info(xml_data)
                    if credit_info:
                        print("Credit information found in report:")
                        for info in credit_info[:5]:  # Show first 5
                            print(f"  {info}")
                    else:
                        print("❌ No credit information found in report")
                else:
                    print("❌ No XML data found in report")
            else:
                print(f"❌ Report with ID {payload['report_id']} not found")
                
        except Exception as inner_e:
            print(f"❌ Error checking report: {inner_e}")

if __name__ == "__main__":
    asyncio.run(test_user_payload())
