#!/usr/bin/env python3
"""
Test script to verify that current date is properly injected into validation prompts.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.rag_prompt_builder import RAGPromptBuilder
from app.models.schemas import Question

def test_current_date_in_prompts():
    """Test that current date is properly injected into validation prompts."""
    print("Testing Current Date Injection in Validation Prompts")
    print("=" * 60)
    
    # Initialize services
    validation_service = ValidationService()
    rag_prompt_builder = RAGPromptBuilder()
    
    # Get current date
    current_date = validation_service.get_current_date()
    print(f"Current date: {current_date}")
    print()
    
    # Create a sample temporal validation question
    temporal_question = Question(
        id="test-1",
        question="Check if any registration numbers have expired and require explanatory comments",
        category="temporal_validation",
        client_code=None,
        darwin_reference_sections="LegalStatusSection/RegistrationNumbers",
        expected_outcome="approved",
        client_specific_type="All"
    )
    
    # Test ValidationService prompt building
    print("Testing ValidationService prompt building:")
    print("-" * 40)
    
    sample_xml = "<Report><LegalStatusSection><RegistrationNumbers><RegistrationNumber>12345</RegistrationNumber><DateExpired>2024-06-01</DateExpired></RegistrationNumbers></LegalStatusSection></Report>"
    
    try:
        enhanced_prompt = validation_service._build_enhanced_validation_prompt(
            temporal_question, 
            sample_xml, 
            1, 
            retry=False, 
            focus_prompt=None
        )
        
        # Check if current date is in the prompt
        if current_date in enhanced_prompt:
            print("✅ Current date found in ValidationService prompt")
            # Find and print the temporal validation section
            lines = enhanced_prompt.split('\n')
            temporal_section_found = False
            for i, line in enumerate(lines):
                if "TEMPORAL VALIDATION REQUIREMENTS" in line:
                    temporal_section_found = True
                    print("Temporal validation section:")
                    for j in range(i, min(i+10, len(lines))):
                        if current_date in lines[j]:
                            print(f"  >>> {lines[j]}")
                        else:
                            print(f"      {lines[j]}")
                    break
            
            if not temporal_section_found:
                print("❌ Temporal validation section not found in prompt")
        else:
            print("❌ Current date NOT found in ValidationService prompt")
            print("First 500 characters of prompt:")
            print(enhanced_prompt[:500])
    
    except Exception as e:
        print(f"❌ Error testing ValidationService prompt: {e}")
    
    print()
    
    # Test RAGPromptBuilder
    print("Testing RAGPromptBuilder:")
    print("-" * 40)
    
    try:
        rag_prompt = rag_prompt_builder.build_rag_enhanced_prompt(
            temporal_question,
            sample_xml,
            1,
            retrieved_data={},
            retry=False,
            focus_prompt=None
        )
        
        # Check if current date is in the RAG prompt
        if current_date in rag_prompt:
            print("✅ Current date found in RAGPromptBuilder prompt")
            # Find and print the temporal validation section
            lines = rag_prompt.split('\n')
            for i, line in enumerate(lines):
                if "TEMPORAL VALIDATION - REGISTRATION NUMBER EXPIRY LOGIC" in line:
                    print("Temporal validation section:")
                    for j in range(i, min(i+8, len(lines))):
                        if current_date in lines[j]:
                            print(f"  >>> {lines[j]}")
                        else:
                            print(f"      {lines[j]}")
                    break
        else:
            print("❌ Current date NOT found in RAGPromptBuilder prompt")
            print("First 500 characters of prompt:")
            print(rag_prompt[:500])
    
    except Exception as e:
        print(f"❌ Error testing RAGPromptBuilder: {e}")
    
    print()
    
    # Test holistic validation prompt
    print("Testing Holistic Validation Prompt:")
    print("-" * 40)
    
    try:
        holistic_prompt = validation_service._build_holistic_validation_prompt(
            [temporal_question],
            sample_xml,
            {},
            focus_prompt=None
        )
        
        # Check if current date is in the holistic prompt
        if current_date in holistic_prompt:
            print("✅ Current date found in holistic validation prompt")
            # Find and print the temporal validation section
            lines = holistic_prompt.split('\n')
            for i, line in enumerate(lines):
                if "TEMPORAL VALIDATION - REGISTRATION NUMBER EXPIRY" in line:
                    print("Temporal validation section:")
                    for j in range(i, min(i+8, len(lines))):
                        if current_date in lines[j]:
                            print(f"  >>> {lines[j]}")
                        else:
                            print(f"      {lines[j]}")
                    break
        else:
            print("❌ Current date NOT found in holistic validation prompt")
            print("First 500 characters of prompt:")
            print(holistic_prompt[:500])
    
    except Exception as e:
        print(f"❌ Error testing holistic validation prompt: {e}")
    
    print()
    print("=" * 60)
    print("Current date injection test completed!")

if __name__ == "__main__":
    test_current_date_in_prompts()
