#!/usr/bin/env python3
"""
Check the text_content to see if registration numbers are there.
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.file_processor import FileProcessor

async def check_text_content():
    """Check the text_content for registration numbers."""
    print("Checking Text Content for Registration Numbers")
    print("=" * 50)
    
    report_id = "1984940"
    print(f"Report ID: {report_id}")
    print()
    
    # Initialize file processor
    file_processor = FileProcessor()
    
    try:
        print("Loading report data...")
        report_data = await file_processor.get_processed_report_by_report_id(report_id)
        
        if not report_data:
            print(f"❌ No report found")
            return
        
        print(f"✅ Report data loaded")
        print()
        
        # Get text_content
        text_content = report_data.get('text_content', [])
        if not text_content:
            print("❌ No text_content found")
            return
        
        print(f"Text content has {len(text_content)} items")
        print()
        
        # Search for registration-related content
        registration_items = []
        for i, item in enumerate(text_content):
            if isinstance(item, dict):
                path = item.get('path', '')
                text = item.get('text', '')
                
                # Check if this item contains registration-related content
                if any(word in path.lower() for word in ['registration', 'reg']) or \
                   any(word in text.lower() for word in ['registration', '322327812993', '12-jan-2025', 'dateexpired']):
                    registration_items.append((i, item))
        
        if registration_items:
            print(f"Found {len(registration_items)} registration-related items:")
            for i, (index, item) in enumerate(registration_items):
                print(f"\nItem {i+1} (index {index}):")
                print(f"  Path: {item.get('path', 'N/A')}")
                print(f"  Text: {item.get('text', 'N/A')}")
                
                # Check if this contains our specific registration number
                text = item.get('text', '')
                if '322327812993' in text:
                    print(f"  ✅ Contains our registration number!")
                if '12-Jan-2025' in text:
                    print(f"  ✅ Contains our expiry date!")
        else:
            print("❌ No registration-related content found in text_content")
            
            # Show first few items for debugging
            print("\nFirst 5 text_content items:")
            for i, item in enumerate(text_content[:5]):
                if isinstance(item, dict):
                    print(f"  Item {i}: Path={item.get('path', 'N/A')}, Text={item.get('text', 'N/A')[:100]}...")
        
        print()
        print("=" * 50)
        print("Check completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(check_text_content())
