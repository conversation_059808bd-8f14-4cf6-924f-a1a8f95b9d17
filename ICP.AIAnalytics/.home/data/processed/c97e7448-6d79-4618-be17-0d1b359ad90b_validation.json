{"validation_id": "c97e7448-6d79-4618-be17-0d1b359ad90b", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "178980", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "d297ec1d-9019-4a31-9db9-4f3dd376e749", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "04ccc544-a17f-4c26-bdcb-87b9a3969065", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f37194c3-7d17-4e6b-ae8e-c966cfa25585", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present, but Client Specific Comments and Payment sections missing", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4d7213f6-f891-46b3-a969-b0b61e391153", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing, but no notes in comments or payment section.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6f0f8dfe-c6ee-4cf7-99ed-91a083aca9cc", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income due to missing data", "confidence_score": 0.9, "relevant_sections": ["Financial"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ad31072b-ce94-4f46-b26c-8b3bb5686141", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Cannot verify compliance; financial info section missing.", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d43186bc-b2d7-48f7-97b4-fda21c251f86", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'MOGZAURI PLUS T/A MPLUS' matches requested name 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "129dd1fd-fb2a-4f64-854e-933b9fe7e001", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present but Client Specific Comments empty", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c24ae68e-9750-4f0a-845d-421928c018a4", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not in special note, violates rule for EUR, USD, GBP", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5408b3ca-1422-4402-908a-5f8587984751", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License/Registration numbers missing in Legal/Special Notes sections", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2b483375-2029-4d66-a718-20a25f3300c5", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language in Payments activities special note", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "282a6ffa-b9d3-4014-96c7-c6c55bcb9fba", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection", "Report/WaiverSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2e462c02-3bc9-4259-97ab-6c3a32d4f304", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No significant changes or payments sections found; cannot verify rule compliance", "confidence_score": 0.85, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "356f6f25-21cc-43b9-a33a-a3962bf1ac52", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Cannot verify adverse announcements due to missing sections", "confidence_score": 0.8, "relevant_sections": ["Report/SignificantChanges", "Report/Payments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "1b23512e-485b-4165-aa11-d01237c7d022", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements in Change Classifications or Description.", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1410cae1-866b-4e90-9b07-acf4da057d2f", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section missing sector info for MOGZAURI PLUS T/A MPLUS", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e4ac80e2-b1fc-4868-a24f-3e5fea4a4e4c", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No export-related text errors in activities section found.", "confidence_score": 0.9, "relevant_sections": ["Report/Activities"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2aeb9a3f-b9b1-4fb8-aa55-4c654f51f7ab", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income elements are missing, violating rule.", "confidence_score": 0.95, "relevant_sections": ["Report/Financial/ProfitAndLoss"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5180600b-98f8-46b1-9338-f049c5af7730", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "No credit given, TRA high not found, cannot verify rule", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/LegalStatusSection/Capital"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a8f5ce21-42a2-4818-81f1-810ff18d8887", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not found in Payment section", "confidence_score": 0.95, "relevant_sections": ["Report"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a145222e-caf4-4243-a740-235eb1b49126", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29409, Requested 1715. Please try again in 2.248s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f8d9e90f-169d-4306-b8c1-3a8f8ade8b2b", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29119, Requested 1796. Please try again in 1.83s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c1ab13ee-10ed-4c0c-be91-478d5f6ddca3", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29322, Requested 1745. Please try again in 2.134s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e7a1d926-8491-4b7b-94fd-afe589c9bf63", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29475, Requested 1649. Please try again in 2.248s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "61f893a4-698c-4c02-a4f8-3f655615e1bd", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29459, Requested 1649. Please try again in 2.216s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "8fd5b9c4-4489-416e-95b2-e492179a70a3", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29292, Requested 1731. Please try again in 2.046s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c7f08a82-d91c-4745-a8ea-7f2612eaed7b", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29254, Requested 1780. Please try again in 2.068s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "7ad11176-ee0b-491c-9d74-5d01e5dff75e", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29295, Requested 1748. Please try again in 2.086s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4d8a116c-2b32-476e-992c-608f10d79349", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29187, Requested 1798. Please try again in 1.97s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "05e2c433-4d73-47ad-9bab-b21083364fe4", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29239, Requested 1791. Please try again in 2.06s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "50187a7b-1e81-4341-999a-c7372eedb6d6", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1724. Please try again in 3.448s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "41e721b9-2e6c-4091-a93b-0996500e8bff", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1764. Please try again in 3.528s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "87d110c5-0da8-4015-9a10-98dc030d2f10", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "05fadf61-2db9-4e8e-85f1-e507958f7160", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1735. Please try again in 3.47s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "25d2bf1a-7e9b-41f8-8a89-165a2d8b1887", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1753. Please try again in 3.506s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d2dae0bd-6a93-4a43-89fc-4665db555f6a", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1717. Please try again in 3.434s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "00b6aced-631f-4494-b5b9-dc8549afdac3", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "ICPLegalGroup 'Government' lacks populated Shareholder, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9e91ba6f-3b4f-4528-aa71-74a904c64bf4", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot calculate credit opinion; Payments section missing", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5d6c216b-00af-401e-a8e5-048dfbd3e68a", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28471, Requested 1759. Please try again in 460ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e88aa111-626d-4b95-af81-c75a2d9e0911", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1729. Please try again in 3.458s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "49bedee9-970a-4f36-acd5-4da7b1e95ec9", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status 'Government Ministry' known, credit can be granted", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 18:35:37.609608", "processing_time": 27.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "EHTR", "rag_enabled": true, "permanent_question_bank_used": true}