{"validation_id": "bee6f378-b2db-46a2-870b-2a19db1b6929", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "bb066d94-ed68-409c-a40a-f47a4422f406", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "e8692427-d5f5-4eee-9469-826a0f2278e0", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "********-0d1b-49c0-bed2-84fa7458e5f8", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is missing; cannot verify compliance on comments and payment sections.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/WaiverSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "77f2c7db-71c9-412f-a337-e7640923325b", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; Client Specific Comments and Payment Section should not be filled.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8eb7ec7c-5387-414d-b9e8-155d2677f5d7", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit and Total Income; missing financial data.", "confidence_score": 0.85, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "25e3a20b-05c9-4d80-a651-5e44dc001574", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses for raw materials found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "08021023-c624-480c-ae58-ae23006a9b16", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name matches Company Name; Client Specific Comments not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "aa05fb96-0d81-43f0-a7a8-d069f85b9d96", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific Comments not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "38fc086a-07ec-41e4-a1fa-4aedfa0499de", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special notes; compliance violated.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ae64b53d-d4f3-496c-b62d-c4300e290673", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Violation: License and registration numbers are missing from the report.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/RegistrationNumbers", "Report/SpecialNotesSection/TextForSpecificClients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "aef008f9-765f-4193-b904-f80f74049ffa", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a115eac9-dfbd-417f-ad89-416b6d46dd18", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check passed; no errors found in relevant sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/ICPLegalGroup", "Report/WaiverSection/Waiver"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5e41960c-34ce-45a0-b809-7c6f8fa3ff86", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements or significant changes found; compliance met.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "849b49b2-5369-4f6d-aee4-4fbb743a77bd", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No adverse announcements found; compliance with expected outcome is violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges", "Report/Payments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d66a6c2b-e783-40ca-8ca9-79d92a570ba1", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "f8d7f45f-0384-47d6-a9f3-90268e793311", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant sector information from Subject activities.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "21287491-bcc7-4555-8909-7890173ab8af", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Violation found: incorrect text regarding export activities present.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "80da9f4a-ddb7-4005-9222-dd2e395f9cd3", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Neither Income nor Total Income is present in the report.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitLossSection/Income", "Report/ProfitLossSection/TotalIncome"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "270d705f-9a13-470f-94c7-dea7f84f7d04", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Credit opinion currency not requested; TRA status is high, rule violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9c38ae44-b59f-4646-9117-82bc8081343e", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not added where applicable, violation found.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4f6b7e45-c780-48f6-8b67-46e86954d005", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is active; no violation of dormant classification found.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0c9e906e-5e59-4b3c-a1d0-c228f63c4e9c", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not provided; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b3a98fbc-9c26-4130-9858-91d570e27639", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no comment provided.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1e636908-dabc-4d4b-9d3b-a9ac65ac7b24", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp capital of 123.00 is small compared to the large amount expected.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "09c23ffa-5cbe-4f04-b24a-d38dfdd3b5ac", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital of 123.00 is too small for the large entity size.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "68f2a6e1-70f7-4dca-951a-6e7108b7b425", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Cannot verify position classifications; no relevant data present in XML.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ff687e52-f722-4e2e-9ded-a3efdab01da5", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation failed: 'Negative' research type not indicated; other sections populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "11a5b971-2479-4f32-92ee-8b18af49bb7d", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline' but Person Interviewed is missing; compliance violated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection/PersonInterviewed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b4ba8ba0-9637-4454-8c7d-d9aa32dc044a", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type is not 'Decline'; compliance rule not met.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/Payments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f91d574d-4acb-49cd-8866-053e6bb3b9e9", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation failed: Research Type is blank, missing comments and Person Interviewed.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ae7c2fd4-d31d-465f-9967-453b5bf375de", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "da8e1046-cc3b-42f0-86e8-bec2ad552202", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No sanctions found; compliance rule is violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed", "Report/SanctionSection/SanctionFound"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8a33bdea-cf7b-4406-bbd0-57a351663e82", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "5ea13f20-8368-433b-a9ba-1fb0ecb3e0d0", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a4057e29-8e8e-4bbd-ae8a-22a8d0d1c657", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details present in the address section, rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2445b832-011d-4af9-9100-ba0412553fb2", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; compliance violated.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2d933d4d-8cda-417e-ad56-c43d66f55804", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder not populated while being part of a related entity group, rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9e8b9c6b-8ac9-4cab-a427-0932e8e2bc7e", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot calculate credit opinion; no payment data present in XML.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "df434fa4-61a4-49eb-8d24-d4df406c36df", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Names in financials match header; compliance rule satisfied.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5864e929-9c99-4bfb-b9e7-7e93442bf704", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty; report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "10899afa-dfbe-4af6-b0b7-b6a7baf56315", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is active; credit can be granted as per validation rule.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 16:27:55.658728", "processing_time": 13.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}