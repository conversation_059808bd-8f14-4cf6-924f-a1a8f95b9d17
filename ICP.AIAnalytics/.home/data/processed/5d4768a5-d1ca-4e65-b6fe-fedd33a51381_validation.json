{"validation_id": "5d4768a5-d1ca-4e65-b6fe-fedd33a51381", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181458", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "0b640927-5eeb-4c2b-8f51-e0318e951984", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "dbf97d28-bd19-4771-8acd-290ae7536647", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "d279ac20-5622-4b4f-9469-265c64a4553c", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name not found; cannot verify comments and payment section compliance.", "confidence_score": 0.85, "relevant_sections": ["PaymentsSection/CreditOpinionNotes", "SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5220f2d2-d3ec-4d7f-9e46-5b0da67c49a8", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name field is missing; comments in Payment and Special Notes sections present, violating rule.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditOpinionNotes", "SpecialNotes/TextForSpecificClients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e3f466f4-58ca-405b-919d-ab0471859b4b", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit '5000' is less than Total Income '10000', compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/FinancialSection/GrossProfit", "Report/FinancialSection/TotalIncome"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "e65803ae-30e7-4ffe-b089-90f19e56c095", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials classified under Operating Costs, violates direct cost rule.", "confidence_score": 0.9, "relevant_sections": ["Report/ChangesSection/Change/ChangeDescription"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5efcd191-18cd-4f6a-93e8-aed993eef970", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'GenAI' matches requested name 'GenAI', comments not needed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "976f3292-f6c7-4281-9c98-5fc58df55d77", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address 'AA**' does not match any active address, comments are empty.", "confidence_score": 0.9, "relevant_sections": ["Report/TradeReferencesSection/TradeReference/Street", "Report/AccountantsSection/Accountant/Building"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f200da83-2b53-4339-89e3-f99d29e1a7ca", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit currency is 'INR', violates EUR, USD, GBP requirement.", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2875ba8f-9903-4e85-825a-e760af77e0b1", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License and registration numbers missing from report sections.", "confidence_score": 0.98, "relevant_sections": ["Report/PublicRecordSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "eb50a8cc-fd7d-487d-a8fb-1c7c3c2338fd", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "JudgementComments contain colloquial language, violates compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/PublicRecordSection/JudgementsSearch/JudgementComments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "effc6f08-e81a-45b2-a430-f10f609a3257", "question_number": 12, "question": "Spelling Check", "summary": "Spelling errors found in 'QualificationNote' and 'JudgementComments'.", "confidence_score": 0.85, "relevant_sections": ["Report/PaymentsSection/QualificationNote", "Report/PublicRecordSection/JudgementsSearch/JudgementComments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9ac18edf-a0b3-44b5-aacc-d0b1284b0289", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Adverse announcements not referenced in payments, violates compliance rule.", "confidence_score": 0.98, "relevant_sections": ["ChangesSection/ChangeClassification", "PaymentsSection/CreditOpinionNotes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "17de49f3-97cf-4702-b701-edc7d87b41ea", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "PaymentsSection has CreditOpinion and MaxCredit populated, violates compliance rule.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/MaxCredit", "Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "351bc43d-13f8-48f9-a474-15f9754fe686", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Change description 'ASFDSF SFDSFSDF' is not negative, violates adverse announcement rule.", "confidence_score": 0.9, "relevant_sections": ["Report/ChangesSection/Change/ChangeDescription"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "59fd7f31-9bc2-423a-9f4c-4600f7eaf234", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-related info; violates compliance rule.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditOpinionNotes", "PaymentsSection/QualificationNote"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7afbe034-519a-4f8a-b5f1-793043ed41aa", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Change description contains incorrect text, violates export activity rule.", "confidence_score": 0.9, "relevant_sections": ["Report/ChangesSection/Change/ChangeDescription"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2bdf0ac3-82a5-4269-a9e0-2df90f554b0b", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing; compliance violation detected.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitAndLoss/Income", "Report/ProfitAndLoss/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "55183193-391a-4612-a6c3-e1eab505fbf0", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Trade Risk Assessment is 'No Classification', violates TRA high requirement", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f44f978f-230d-4faf-bad9-e298e5977593", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "TradeRiskAssessment is 'No Classification', violates country risk classification rule.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d288f730-3a6c-45e9-9cfd-7b912cd8814f", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "TRA 'No Classification' found, but company status is 'Active', violates dormant rule.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/TradeRiskAssessment", "LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "747f86c3-70b1-4ca5-a91d-e90f94c393b9", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not found; Transfer to Principal is false, compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1d7c85ca-8540-471f-96ba-bb1e932573ab", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "No registration numbers found; cannot verify expiry comment requirement.", "confidence_score": 0.85, "relevant_sections": ["PaymentsSection", "Comments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7e17ef19-fb0d-4926-9614-0b2f3935c60d", "question_number": 24, "question": "Small with large amount", "summary": "Credit Opinion 'Small' with Max Credit '50' violates large amount rule.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0c5f29a4-6f13-42a8-9fef-addd0b8ff59f", "question_number": 25, "question": "Large with small amount", "summary": "CreditFigure '5554' exceeds MaxCredit '50', violates large with small amount rule.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit", "PaymentsSection/CreditFigure"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ed528a59-941a-4b06-8955-8b891d678667", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications are not present for comparison, cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["ChangesSection/Change", "LawyersSection/Lawyer/Position"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "39ac5ed9-2f92-4df0-bb1f-dcc4c6206d58", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Header present, but no Address or General Info; violates Negative report requirements.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "05ef3b91-cc6c-4c4a-8515-b172bf61e916", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'No Contact', Person Interviewed is absent, compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/ChangesSection/Change/ChangeHeading", "Report/ChangesSection/Change/ChangeDescription"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "09482f45-a5d7-47ba-8592-2d7f3396bb7c", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Comment states 'The company has declined to give any information', compliant with rule.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/Comment"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1b85b261-4a62-4063-8362-41a4eb5e51e0", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is 'Blank', but no comment in Payments confirms contact, violates rule.", "confidence_score": 0.9, "relevant_sections": ["ResearchType/Type", "PaymentsSection/QualificationNote", "PersonInterviewed/Presence"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4c273a53-999e-4d60-af4f-50f8bc4b9522", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search performed: 'true', compliant with validation rule.", "confidence_score": 1.0, "relevant_sections": ["PublicRecordSection/SanctionsSearch/SanctionsSearchPerfomed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3321c90f-14a4-4a30-84c9-18c948366129", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanctions found, but payments section lacks sanction indication, violates compliance.", "confidence_score": 0.9, "relevant_sections": ["PublicRecordSection/SanctionsSearch/SanctionsFound", "PaymentsSection/TradeRiskAssessment", "PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit", "PaymentsSection/OpinionOnMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0beff616-4b55-4042-b1d3-90503f5e3905", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f9354dfe-31b8-404f-a337-710641fbf26b", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in ChangesSection, violates compliance rule.", "confidence_score": 0.9, "relevant_sections": ["ChangesSection/Change"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "cf81577b-6122-4717-ad6d-6b9291a9310a", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type is 'Select' but no contact details found in Address section.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6b31ec26-59b7-4f6b-846c-b5d8e213132f", "question_number": 36, "question": "Capital missing currency", "summary": "Capital field is not provided, violates currency requirement.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4097b48a-9911-4135-b52b-fcc0a0f47ddc", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder information is missing for related entities, violates compliance rule.", "confidence_score": 0.98, "relevant_sections": ["Report/ChangesSection/Change"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "049bf51e-e938-4022-8c32-2af447d92ab6", "question_number": 38, "question": "Calculate credit opinion", "summary": "Credit opinion is 'Small', but notes are invalid. Violates compliance.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/CreditOpinionNotes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3edae33c-d98f-4da4-8762-e6dc82eaf394", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'GenAI' matches requested name 'GenAI', compliance confirmed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3da3dffb-99cb-4d86-b1ec-971d2068c5f4", "question_number": 40, "question": "Town missing in town box", "summary": "Town box in Company Secretary Section is empty, violates validation rule.", "confidence_score": 0.98, "relevant_sections": ["CompanySecretarySection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "f4987f42-e85c-4ab3-867b-13b7218489ee", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is 'Active', compliant with credit granting rules.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/CompanyStatus", "PaymentsSection/CreditOpinion"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 16:56:52.367073", "processing_time": 121.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}