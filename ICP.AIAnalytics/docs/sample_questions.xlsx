# Sample Questions Excel File Format

This document describes how to create a sample Excel file for testing the XML Report Validation System.

## File Format: sample_questions.xlsx

Create an Excel file with the following columns:

| question_text | category | priority | expected_format |
|---------------|----------|----------|-----------------|
| What is the total number of records processed? | data_processing | high | numeric |
| Are there any data quality issues identified? | data_quality | high | yes/no |
| What is the processing completion percentage? | processing | medium | percentage |
| What is the timestamp of the last update? | metadata | medium | datetime |
| Are all required fields populated? | validation | high | yes/no |
| What is the data source system? | metadata | low | text |
| How many validation errors were found? | validation | high | numeric |
| What is the file size of the processed data? | processing | low | size_in_mb |
| Are there any duplicate records? | data_quality | medium | yes/no |
| What is the data processing method used? | processing | medium | text |

## Instructions:

1. Create a new Excel file
2. Add the column headers as shown above
3. Fill in the data rows with your validation questions
4. Save as .xlsx format
5. Upload via the API endpoint: POST /api/v1/upload/questions

## Column Descriptions:

- **question_text** (required): The actual validation question to ask about the XML report
- **category** (optional): Group questions by category (e.g., data_quality, processing, metadata)
- **priority** (optional): Set priority level (low, medium, high)
- **expected_format** (optional): Hint about expected answer format

The system will automatically process this Excel file and extract the questions for validation against XML reports. 