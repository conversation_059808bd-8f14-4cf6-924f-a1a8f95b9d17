{"validation_id": "c5041fef-b56f-49b0-885f-af6da476aa16", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "3453fd59-8807-4e30-a6f8-19b09003856e", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "9ea2b220-b844-4617-8969-f0eb4a54e426", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "bf9c0b48-4097-47f3-aa04-2ab7265f44db", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is missing; cannot verify compliance with comments and payment sections.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection", "Report/WaiverSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "845eabeb-d97e-4883-b6df-e636d7a77f74", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name missing; Client Specific Comments and Payment Section must be empty.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fb8b8861-23e6-480c-91a4-0c48e0e0b4da", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit not provided; cannot verify against Total Income.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8d71af15-0629-4a34-a9fb-f0984ced5407", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses data found to verify compliance with direct costs rule.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "979d574a-dc4e-48aa-96aa-93f69526de8f", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name matches Company Name; Client Specific Comments not populated.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5076945c-a7d8-466f-ae72-2a14d6d90f1f", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; specific comments not populated.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4686cb68-21ac-4c41-aad3-72fec01c2893", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not specified in special notes; compliance violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c44eafa5-8be1-440c-93fd-5af1e069a2d5", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License and registration numbers are missing from the report sections.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9dfeaaa9-ae5b-4975-82d9-e8bfe429d158", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in the special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0b5c8373-600b-41a7-9064-4cf5229c2d0a", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check passed; no violations found in relevant sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/ICPLegalGroup", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "670c0cf3-f528-453b-a4ee-493a12b4ab73", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Payments section lacks reference to significant changes, violating compliance.", "confidence_score": 0.98, "relevant_sections": ["Report/Payments", "Report/SignificantChanges"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6b9ef520-11f0-4a03-9d86-b73462146d5b", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Validation failed: Missing required sections for compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChangesSection", "Report/PaymentsSection", "Report/CreditOpinionSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b0f6b033-2436-439a-bf78-814ffbca1c13", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "bac8ee94-7910-4b65-b803-15a8a1d96d76", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant information related to the company's sector.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f23b979c-cb42-487b-bd84-5508ddccceda", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Violation found: Incorrect text added in services activities section.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "70dc4377-a4da-4d5f-b566-a8d372570492", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing; validation rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitLossSection/Income", "Report/ProfitLossSection/TotalIncome"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4ba03783-997c-4dbe-abe6-57b371ff17b1", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "No credit opinion currency requested; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/NotGiven", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3e06b721-2d67-4508-b109-ae113c46270f", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not applicable; compliance rule violated.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6a304d46-0536-4077-ae72-b06bb1f61ee0", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is active; no dormant or ceased trading classification found.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "679fba44-5087-409d-bc28-d85644f1c79a", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not provided; compliance cannot be verified.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1a22d86d-bb67-4598-a0d7-28e1b482e3c9", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no Comment present.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2eab7f19-6dd1-4057-9ca9-ca74a1fbf711", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp capital is small (123.00) while the amount is large, violating the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "29518df7-b8d3-4323-9117-90420613689d", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital of 123.00 does not meet the large with small amount rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "24f180f9-2591-4179-a7a2-0c3131d71689", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications are not present for compliance checking.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d7f8ab6e-47a3-416e-84a1-43790307102e", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation failed: 'Negative' research type not specified; other sections are populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "2d86dc39-83ed-4cb8-8e09-8556c9188410", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Validation failed: Research Type is 'Decline' but Person Interviewed is missing.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4028b1bb-ed24-4a48-af8c-e7b71341896c", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type is not Decline; payments section lacks decline comment.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "49a2a4a1-5b74-4771-af5f-40b4ddb9eec8", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation failed: Research Type is missing, Payments comment not found, Person Interviewed absent.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "35a0c1c8-8a38-47b0-adb7-1d411e442f36", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; required tag is missing.", "confidence_score": 0.98, "relevant_sections": ["Report/SanctionSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8d3b13f5-9d5f-42f1-a8fd-b533bc1f03ec", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No sanctions found; compliance check cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/WaiverSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "18f80e04-a982-41df-88c5-0b824c89ac90", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "9ac05c09-46b6-4074-8ab7-928ab6da430a", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8d2a059c-23d7-4e89-8c11-6d33597739bb", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Contact details missing; no telephone or email in the address section.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/AddressesSection/Addresses/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5c380094-0786-4044-a455-f9b0105bfbf3", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; expected a currency indication.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "441b7b37-bc45-41f0-9a47-da146cd8265a", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Violation: Shareholder not populated while being part of a related entity group.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4244534e-77e9-4f2c-ae85-f4eb7e941496", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot verify credit opinion due to lack of payment data in XML.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3e1fbbc9-76a2-4d5b-8edc-16100b170384", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Names in financials match header; compliance rule satisfied.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "48b4429d-236f-48e7-a4f8-aa5480447a57", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty; report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ca51ebb3-fa67-438d-97bc-d39458beeec0", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is known as 'Active'; credit can be granted.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 12:29:31.982485", "processing_time": 13.*************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}