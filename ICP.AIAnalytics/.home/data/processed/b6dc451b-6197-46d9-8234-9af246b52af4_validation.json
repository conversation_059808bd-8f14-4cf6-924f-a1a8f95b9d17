{"validation_id": "b6dc451b-6197-46d9-8234-9af246b52af4", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "178980", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "e100ed0e-441a-4b0d-86ce-fc9c5fb1103f", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "2cefbd4f-879f-4330-98d4-5fab700279b0", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f293ca48-15b5-4b88-95d2-d7a3da7fe77a", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present, but no Client Specific Comments or Payment details found.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9cd92c1c-cf3e-4f9f-95a7-b1213b894f66", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing, no notes should be present in comments/payment sections.", "confidence_score": 0.95, "relevant_sections": ["Order Details", "Payments", "Special Notes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6ee28685-4ea5-4cb6-8710-c9650793a5de", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income; data missing", "confidence_score": 0.9, "relevant_sections": ["Financial/ProfitAndLoss", "Financial/TotalIncome", "Financial/TotalGrossProfit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f9c8dfaf-bcec-4ce7-9433-7be784a42dfd", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Cannot verify Expenses for raw materials in Direct Costs due to missing P&L data", "confidence_score": 0.85, "relevant_sections": ["Financial/Profit and loss"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "bc43c749-ab91-47c6-8c9b-b28d4e8c4a02", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'MOGZAURI PLUS T/A MPLUS' matches requested name 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "109f25c7-54e6-4996-b254-e51d5a54ce36", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present but Client Specific Comments missing", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2ad88531-a7e4-4e26-b8b6-96d4698f0a66", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not in special note, violates rule for EUR, USD, GBP", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "82d78cef-fabe-437d-93a0-9c8bdabff376", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License/Registration numbers missing in Legal/Special Notes sections", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "083764e1-1b17-4700-9adf-206e7bf83a89", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in Special Notes", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b5f592be-cb9c-4e65-99cb-49a14a202a74", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4da0218e-96aa-4a73-a4e5-0c563e3a07fe", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No significant changes or payments sections found in XML", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "444b6993-6543-4676-8260-6381c19616cb", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Trade risk assessment and credit fields missing, cannot verify compliance", "confidence_score": 0.85, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e07fd141-722b-4aef-a577-0c822592faf6", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found, cannot verify negative description requirement", "confidence_score": 0.85, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "27430fa8-cc31-4220-acaa-d222ab33e11d", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section missing sector info for 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "1f77d9af-c482-4ffa-89ae-696fabc04d2f", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No export-related text errors in Activities section found.", "confidence_score": 0.9, "relevant_sections": ["Report/ActivitiesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "bedcd4b4-a232-4da4-9650-08caf19de346", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income elements missing, violates rule", "confidence_score": 0.95, "relevant_sections": ["Financial/Profit and Loss", "Income", "Total Income"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ab1c4d8b-568f-4bc4-aedf-6e24e0ff40ca", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "No credit given, TRA high not verified; notify editor", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "01920716-4c66-43a7-9af9-478c44be6bfa", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not applicable in Payment section.", "confidence_score": 0.95, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8e5f60e8-41f2-4c01-ac91-d79dbe63b3ad", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "No classification for dormant, ceased trading found in Payment section", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a71a2d2c-e391-468c-8049-0a37dfc1819d", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number missing in Related Entities as Branch; Transfer to Principal false", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "78d005e7-5461-4753-bd0b-549cd4cf3b36", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired but no comment provided", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "631444b5-cd9d-4aa4-a011-e6789a42741b", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1674. Please try again in 3.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4f270ff3-ddf9-4314-9144-2666d95b6430", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1674. Please try again in 3.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "99da11a5-8338-4686-a3b2-96998fa13d64", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "No Position data found, cannot verify classification compliance", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "781606bb-7649-4c4b-9b2c-d93d42a692b4", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Research type 'Negative' but LegalStatusSection populated, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "865585de-16da-4a87-ae40-91a66fe69462", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research type 'Decline' found, Person Interviewed not applicable", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7fe8f9ea-3f16-4763-9360-f0c121dc60f6", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type 'Decline' missing comment in Payments section", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e7ce5b76-00cf-48e9-9661-172677629720", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type 'Select' missing, no payment comment or interviewee found", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6cae02fa-8d6c-4634-85ea-e39ee4b69507", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanction search tag missing, violates requirement", "confidence_score": 0.95, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b597d157-f67a-46b8-b369-d7a080b68f30", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanctions section missing, cannot verify compliance with rule", "confidence_score": 0.9, "relevant_sections": ["Sanction Search Performed", "Sanction Found"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c3749719-9b5e-49e0-8052-fb6cfdc99341", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "c5c6f89e-d778-424e-ad98-b3f94a91fb94", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and Legal sections", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a1099add-529d-413d-92c9-16dbd36ecf9a", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type 'Select' but no contact details found", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c2f1fac4-342b-412a-bfe0-3dc35ed1d356", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency missing in LegalStatusSection, field blank", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d40fb7fc-1831-4f64-b2e9-6e673853f884", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "No Shareholder populated, violates related entities rule", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f892c02e-d048-4861-a511-d2c2a1b919fd", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot calculate credit opinion; Payments section missing", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "3ea9ac8a-a6ec-4125-bc23-fde358129bbe", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1734. Please try again in 3.468s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "041883eb-7010-4496-b6b2-e822431907ef", "question_number": 40, "question": "Town missing in town box", "summary": "Town box missing in address-related sections, cannot verify compliance", "confidence_score": 0.9, "relevant_sections": ["address", "related entities", "facilities", "registered address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c57c0ebb-549a-4d8e-a09c-fc49ef056c1c", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28733, Requested 1726. Please try again in 918ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 18:33:27.919758", "processing_time": 61.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "EHTR", "rag_enabled": true, "permanent_question_bank_used": true}