#!/usr/bin/env python3
"""
Test isolated expiry validation to debug the issue.
"""

import sys
import os
import asyncio
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor

# Create a simple Question class for testing
class Question:
    def __init__(self, question_id, question_number, question, darwin_reference_sections, expected_outcome, client_specific_type, editor):
        self.question_id = question_id
        self.question_number = question_number
        self.question = question
        self.darwin_reference_sections = darwin_reference_sections
        self.expected_outcome = expected_outcome
        self.client_specific_type = client_specific_type
        self.editor = editor

async def test_isolated_expiry():
    """Test isolated expiry validation."""
    print("Testing Isolated Expiry Validation for Report 1981692")
    print("=" * 60)
    
    try:
        validation_service = ValidationService()
        file_processor = FileProcessor()
        
        # Get the report data
        report_data = await file_processor.get_processed_report_by_report_id("1981692")
        
        if not report_data:
            print("❌ Report 1981692 not found")
            return
        
        print("✅ Report 1981692 found")
        print()
        
        # Create the specific expiry question
        expiry_question = Question(
            question_id="test-expiry",
            question_number=1,
            question="If any registration number has expired, a comment should be added explaining the expiry.",
            darwin_reference_sections="LegalStatusSection/RegistrationNumbers",
            expected_outcome="Check for expired registrations and comments",
            client_specific_type="All",
            editor=""
        )
        
        print("Question to test:")
        print(f"  {expiry_question.question}")
        print()
        
        # Extract XML content for this question
        xml_content = await validation_service._extract_xml_content_for_question_with_darwin(report_data, expiry_question)
        
        print(f"XML content length: {len(xml_content)} characters")
        print()
        
        # Show relevant XML section
        if '<DateExpired>' in xml_content:
            start = xml_content.find('<DateExpired>')
            end = xml_content.find('</DateExpired>', start) + 14
            date_section = xml_content[start:end]
            print(f"DateExpired found: {date_section}")
        else:
            print("❌ DateExpired not found in XML content")
        
        if '<Comments>' in xml_content:
            start = xml_content.find('<Comments>')
            end = xml_content.find('</Comments>', start) + 11
            comment_section = xml_content[start:end]
            print(f"Comments found: {comment_section}")
        else:
            print("❌ Comments not found in XML content")
        
        print()
        
        # Build the validation prompt
        prompt = validation_service._build_enhanced_validation_prompt(
            expiry_question, 
            xml_content, 
            1
        )
        
        print("Validation Prompt (first 2000 chars):")
        print("-" * 50)
        print(prompt[:2000])
        print("..." if len(prompt) > 2000 else "")
        print("-" * 50)
        print()
        
        # Run the validation
        print("Running LLM validation...")
        if validation_service.llm:
            from langchain_core.messages import HumanMessage
            response = await validation_service.llm.agenerate([[HumanMessage(content=prompt)]])
            raw_response = response.generations[0][0].text
            
            print("Raw LLM Response:")
            print("-" * 50)
            print(raw_response)
            print("-" * 50)
            print()
            
            # Try to parse the JSON response
            try:
                parsed_response = json.loads(raw_response)
                print("Parsed Response:")
                print(f"  Summary: {parsed_response.get('summary', 'N/A')}")
                print(f"  Status: {parsed_response.get('status', 'N/A')}")
                print(f"  Reasoning: {parsed_response.get('reasoning', 'N/A')}")
                print(f"  Confidence: {parsed_response.get('confidence_score', 'N/A')}")
                
                # Analyze the response
                summary = parsed_response.get('summary', '').lower()
                reasoning = parsed_response.get('reasoning', '').lower()
                
                if 'expired' in summary or 'expired' in reasoning:
                    print("\n❌ ISSUE: LLM incorrectly identified expired registration")
                    print("   The registration 14-Aug-2026 is VALID (expires in 374 days)")
                else:
                    print("\n✅ CORRECT: LLM correctly identified no expired registrations")
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
        else:
            print("❌ No LLM available")
        
    except Exception as e:
        print(f"❌ Error during isolated validation: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_isolated_expiry())
