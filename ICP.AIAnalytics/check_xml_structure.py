#!/usr/bin/env python3
"""
Check the actual xml_structure data to see if RegistrationNumbers are present.
"""

import sys
import os
import asyncio
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.file_processor import FileProcessor

async def check_xml_structure():
    """Check the actual xml_structure data."""
    print("Checking XML Structure Data")
    print("=" * 30)
    
    report_id = "1984940"
    print(f"Report ID: {report_id}")
    print()
    
    # Initialize file processor
    file_processor = FileProcessor()
    
    try:
        print("Loading report data...")
        report_data = await file_processor.get_processed_report_by_report_id(report_id)
        
        if not report_data:
            print(f"❌ No report found")
            return
        
        print(f"✅ Report data loaded")
        print(f"Available keys: {list(report_data.keys())}")
        print()
        
        # Get xml_structure
        xml_structure = report_data.get('xml_structure', {})
        if not xml_structure:
            print("❌ No xml_structure found")
            return
        
        print(f"XML structure root keys: {list(xml_structure.keys())}")
        print()
        
        # Check for Report/LegalStatusSection
        if 'Report' in xml_structure:
            report_section = xml_structure['Report']
            print(f"Report keys: {list(report_section.keys()) if isinstance(report_section, dict) else 'Not a dict'}")
            
            if 'LegalStatusSection' in report_section:
                legal_section = report_section['LegalStatusSection']
                print(f"LegalStatusSection keys: {list(legal_section.keys()) if isinstance(legal_section, dict) else 'Not a dict'}")
                
                if 'RegistrationNumbers' in legal_section:
                    reg_numbers = legal_section['RegistrationNumbers']
                    print(f"✅ Found RegistrationNumbers!")
                    print(f"RegistrationNumbers type: {type(reg_numbers)}")
                    print(f"RegistrationNumbers content:")
                    print(json.dumps(reg_numbers, indent=2))
                    
                    # Check for nested RegistrationNumber
                    if isinstance(reg_numbers, dict) and 'RegistrationNumber' in reg_numbers:
                        reg_number = reg_numbers['RegistrationNumber']
                        print(f"\n✅ Found nested RegistrationNumber!")
                        print(f"RegistrationNumber type: {type(reg_number)}")
                        print(f"RegistrationNumber content:")
                        print(json.dumps(reg_number, indent=2))
                        
                        # Check for specific fields
                        if isinstance(reg_number, dict):
                            if 'RegistrationNumberValue' in reg_number:
                                print(f"\n✅ Registration Number Value: {reg_number['RegistrationNumberValue']}")
                            if 'DateExpired' in reg_number:
                                print(f"✅ Date Expired: {reg_number['DateExpired']}")
                            if 'Comments' in reg_number:
                                print(f"✅ Comments: {reg_number['Comments']}")
                    else:
                        print(f"❌ No nested RegistrationNumber found")
                        print(f"RegistrationNumbers structure: {reg_numbers}")
                else:
                    print(f"❌ No RegistrationNumbers in LegalStatusSection")
                    print(f"Available keys: {list(legal_section.keys())}")
            else:
                print(f"❌ No LegalStatusSection in Report")
                print(f"Available keys: {list(report_section.keys())}")
        else:
            print(f"❌ No Report in xml_structure")
            print(f"Available keys: {list(xml_structure.keys())}")
        
        print()
        print("=" * 30)
        print("Check completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(check_xml_structure())
