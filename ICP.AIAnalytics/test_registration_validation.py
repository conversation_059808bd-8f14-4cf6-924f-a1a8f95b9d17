#!/usr/bin/env python3
"""
Test script to verify registration number expiry validation functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService

def test_registration_validation():
    """Test registration number expiry validation."""
    print("Testing Registration Number Expiry Validation")
    print("=" * 50)
    
    vs = ValidationService()
    
    # Test case 1: Mixed expired and non-expired registrations
    xml_data1 = {
        'Report': {
            'LegalStatusSection': {
                'RegistrationNumbers': [
                    {
                        'RegistrationNumber': '12345',
                        'DateExpired': '2024-07-01',
                        'Comments': 'Expired with comment'
                    },
                    {
                        'RegistrationNumber': '67890',
                        'DateExpired': '2024-06-01'
                        # No comment - should cause rejection
                    },
                    {
                        'RegistrationNumber': '11111',
                        'DateExpired': '2025-12-31'
                        # Not expired
                    }
                ]
            }
        }
    }
    
    print("Test Case 1: Mixed expired/non-expired with missing comment")
    result1 = vs.validate_registration_number_expiry(xml_data1)
    print(f"Status: {result1['status']}")
    print(f"Summary: {result1['summary']}")
    print(f"Expired registrations: {result1['details']['expired_registrations']}")
    print(f"Missing comments: {result1['details']['missing_comments']}")
    print()
    
    # Test case 2: No registration numbers
    xml_data2 = {'Report': {'LegalStatusSection': {}}}
    
    print("Test Case 2: No registration numbers found")
    result2 = vs.validate_registration_number_expiry(xml_data2)
    print(f"Status: {result2['status']}")
    print(f"Summary: {result2['summary']}")
    print()
    
    # Test case 3: All registrations current
    xml_data3 = {
        'Report': {
            'LegalStatusSection': {
                'RegistrationNumbers': [
                    {
                        'RegistrationNumber': '11111',
                        'DateExpired': '2025-12-31'
                    },
                    {
                        'RegistrationNumber': '22222',
                        'DateExpired': '2026-01-15'
                    }
                ]
            }
        }
    }
    
    print("Test Case 3: All registrations current")
    result3 = vs.validate_registration_number_expiry(xml_data3)
    print(f"Status: {result3['status']}")
    print(f"Summary: {result3['summary']}")
    print()
    
    print("=" * 50)
    print("All tests completed!")

if __name__ == "__main__":
    test_registration_validation()
