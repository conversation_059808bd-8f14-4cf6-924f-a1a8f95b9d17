#!/usr/bin/env python3
"""
Test script to validate the actual XML data with registration numbers.
"""

import sys
import os
import xml.etree.ElementTree as ET
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService

def test_actual_xml():
    """Test the actual XML data with registration numbers."""
    print("Testing Actual XML Data with Registration Numbers")
    print("=" * 60)
    
    # The actual XML data structure (converted to dict format)
    xml_data = {
        'Report': {
            'LegalStatusSection': {
                'DateStarted': '23-Mar-2025',
                'CompanyStatus': 'Active',
                'RegistrationNumbers': {
                    'RegistrationNumber': {
                        'ICPRegistrationNumberName': 'Commercial Registration Number',
                        'LocalRegistrationNumberName': 'Permanent Account Number',
                        'LocalAbbreviation': 'PAN',
                        'IssuingAuthority': 'Income Tax Department',
                        'RegistrationNumberValue': '************',
                        'DateIssued': '12-Jan-2024',
                        'DateExpired': '12-Jan-2025',
                        'RenewalFrequency': 'Annually',
                        'Comments': 'Commercial Registration has expired and not been renew by the owner of the company',
                        'ICPRegistrationNumberTypeId': '4'
                    }
                },
                'Capital': {
                    'Currency': 'INR',
                    'Capital': '**********.00',
                    'PaidUp': '200000.00',
                    'NotGiven': 'false'
                }
            }
        }
    }
    
    validation_service = ValidationService()
    current_date = validation_service.get_current_date()
    
    print(f"Current date: {current_date}")
    print(f"Registration expiry date: 12-Jan-2025")
    print(f"Should be expired: {'YES' if '2025-01-12' <= current_date else 'NO'}")
    print()
    
    print("Step 1: Testing date expiry logic directly...")
    is_expired, parsed_date = validation_service.is_date_expired("12-Jan-2025")
    print(f"Date '12-Jan-2025' is expired: {is_expired}")
    print(f"Parsed date: {parsed_date}")
    print()
    
    print("Step 2: Testing registration number validation...")
    result = validation_service.validate_registration_number_expiry(xml_data)
    
    print(f"Validation result:")
    print(f"  Status: {result['status']}")
    print(f"  Summary: {result['summary']}")
    print(f"  Details: {result['details']}")
    print()
    
    # Check if the result is correct
    expected_status = "approved"  # Because there's a comment explaining the expiry
    if result['status'] == expected_status:
        print("✅ Validation result is CORRECT")
        print("   - Registration number is expired")
        print("   - Comment is present explaining the expiry")
        print("   - Status should be 'approved'")
    elif result['status'] == "rejected":
        print("⚠️  Validation result shows 'rejected'")
        print("   - This might be correct if comment is not detected properly")
    else:
        print("❌ Unexpected validation result")
    
    print()
    
    print("Step 3: Testing with RegistrationNumbers as list format...")
    # Test with list format (in case XML parsing creates a list)
    xml_data_list = {
        'Report': {
            'LegalStatusSection': {
                'RegistrationNumbers': [
                    {
                        'ICPRegistrationNumberName': 'Commercial Registration Number',
                        'LocalRegistrationNumberName': 'Permanent Account Number',
                        'LocalAbbreviation': 'PAN',
                        'IssuingAuthority': 'Income Tax Department',
                        'RegistrationNumberValue': '************',
                        'DateIssued': '12-Jan-2024',
                        'DateExpired': '12-Jan-2025',
                        'RenewalFrequency': 'Annually',
                        'Comments': 'Commercial Registration has expired and not been renew by the owner of the company',
                        'ICPRegistrationNumberTypeId': '4'
                    }
                ]
            }
        }
    }
    
    result_list = validation_service.validate_registration_number_expiry(xml_data_list)
    print(f"List format validation result:")
    print(f"  Status: {result_list['status']}")
    print(f"  Summary: {result_list['summary']}")
    print(f"  Details: {result_list['details']}")
    print()
    
    print("Step 4: Manual verification of the logic...")
    print("Registration Number Details:")
    print("  - Value: ************")
    print("  - Date Issued: 12-Jan-2024")
    print("  - Date Expired: 12-Jan-2025")
    print("  - Current Date: 2025-08-04")
    print("  - Is Expired: YES (12-Jan-2025 < 2025-08-04)")
    print("  - Has Comment: YES ('Commercial Registration has expired and not been renew by the owner of the company')")
    print("  - Expected Status: APPROVED (expired but has explanatory comment)")
    print()
    
    print("=" * 60)
    print("Test completed!")
    print()
    print("CONCLUSION:")
    print("The XML report DOES contain a registration number that is EXPIRED.")
    print("The validation should detect this and return 'approved' because there's a comment.")

if __name__ == "__main__":
    test_actual_xml()
