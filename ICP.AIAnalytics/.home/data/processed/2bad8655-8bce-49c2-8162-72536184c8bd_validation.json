{"validation_id": "2bad8655-8bce-49c2-8162-72536184c8bd", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181305", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "2d0ea17b-0ac7-4477-9e38-590247c66467", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client 'TURKEXIM'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "1f6ed329-6cce-422a-85f8-101b66590d0e", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client '??'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "24f954ba-ae1c-4ff1-b4df-************", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is absent; cannot verify comments or payment section compliance.", "confidence_score": 0.9, "relevant_sections": ["Order Details/Client Name", "Payments/Credit Opinion Notes", "Special Notes/Text for Specific Clients"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6a4b13ff-d76e-4435-95bf-f03530d2b130", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'Not Present' leads to filled comments and payment notes, violating compliance.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/ClientName", "Report/ClientSpecificCommentsSection/Comments/Comment", "Report/PaymentSection/PaymentNotes/Note"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "21458685-4d13-461e-8cf7-3e901a4190e7", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit '80.00' is less than Total Income '100.00', compliant.", "confidence_score": 0.98, "relevant_sections": ["RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ad51a15f-d2c1-424d-8523-0e1316743333", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials and supplies in 'Direct Costs', compliant with rules.", "confidence_score": 0.98, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6bfc33b9-876e-45c6-86da-7e3363e985a7", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'RE test 2' does not match requested name 'test', comments needed.", "confidence_score": 0.9, "relevant_sections": ["HeaderSection/CompanyName", "HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e97c87a1-dc72-42f5-8da4-343e48369454", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address 'test' matches active address, comments not needed.", "confidence_score": 0.98, "relevant_sections": ["AddressesSection/Addresses/Address", "RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8fbe33ff-b11e-4d6f-8b65-868f31da4d03", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit currencies 'EUR', 'USD', 'GBP' present; complies with rule.", "confidence_score": 0.98, "relevant_sections": ["SpecialNote/MaxCredit/Currency", "SpecialNote/MaxCredit/NotCurrency"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c3123156-e543-46fa-a0a6-56a27e2f23ac", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License number not recorded in legal section; missing info needed.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/RegistrationNumbers", "RelatedEntitiesLegalStatusSection/RegistrationNumbers"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d167c4f0-f542-4c10-86e4-d568a97eff97", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in payment activities section.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/RelatedEntitiesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5c090fde-24c3-4e58-8094-52fdc3b535d2", "question_number": 12, "question": "Spelling Check", "summary": "Spelling error found: 'atest' in Shareholder Name section.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Shareholders/Shareholder/Name"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "452cf84e-dc12-4567-a312-efffe4c08538", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No significant changes or payments data present, cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["LegalStatusSection", "RelatedEntitiesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "52a152ad-6b64-4d80-9738-ebe57a88b4cb", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Trade risk assessment has 'High' risk level, violates expected outcome.", "confidence_score": 0.95, "relevant_sections": ["Report/TradeRiskAssessment/RiskLevel", "Report/TradeRiskAssessment/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3247f61f-357e-41b5-b318-312ef3d6eb2b", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/LegalStatus", "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6f016a46-ca61-4019-880c-bc2b42446119", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-related information; compliance violated.", "confidence_score": 0.9, "relevant_sections": ["Report/Payments/CreditOpinionNotes", "Report/Activities/SubjectActivities"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "539bb2cc-1ba4-41ed-93a7-3d703f2cdd5d", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Registration number 'Other Registration Number1' contains incorrect text, violates export rule.", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "21e81e18-6e39-429f-a753-0893772837e3", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing, cannot verify compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/ProfitAndLoss/Income", "Report/ProfitAndLoss/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "650e8c4f-79e6-4f96-b77f-63ee9784f25a", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Currency 'ANG' requested, no credit given, TRA high condition violated.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/Capital/NotGiven", "RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/Currency"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c91a7697-c4c6-4376-b8a4-ced05166a605", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not found; compliance issue detected.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection", "Report/RelatedEntitiesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6b5d79a3-55e4-4f52-bb3f-9717c8550369", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is 'Active', violates dormant/ceased trading rule.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3518e35e-59b8-4100-8f60-72b880e3080a", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number '3456789' not listed as Branch; compliance violated.", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9f927ec2-1c1e-4b24-825e-696895ac20dc", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number '32433333' expired, but comment not updated.", "confidence_score": 0.95, "relevant_sections": ["LegalStatusSection/RegistrationNumbers", "RelatedEntitiesLegalStatusSection/RegistrationNumbers"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ef9ce5cf-e2a3-4361-befa-290f7abf5422", "question_number": 24, "question": "Small with large amount", "summary": "Shareholder 'atest' has 0 shares but total shares are 110, violates small with large rule.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder/NoOfShares", "LegalStatusSection/Shareholders"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f450c1ee-dba7-49dd-ad63-2118152e205d", "question_number": 25, "question": "Large with small amount", "summary": "Shareholder 'atest' has 0 shares, violating large with small amount rule.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder/NoOfShares"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b49b7f1d-4b6b-44ac-b14d-67a7d136cbfd", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not provided; cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["RelatedEntitiesSection/RelatedEntity/CompanyName", "RelatedEntitiesLegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bc54769c-004a-406a-ab66-50fe35ad5eb8", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Header and Address sections populated; LegalStatus and RelatedEntities violate the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/AddressesSection/Addresses/Address", "Report/SpecialNotes/Note"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "54913c3b-c247-4793-b7b4-0d13fcbc234f", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline', Person Interviewed section missing, violates No Contact rule.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotes/PersonInterviewed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "80a95d87-78a5-4842-a73f-9775fb808991", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research Type is missing; cannot verify compliance with decline rule.", "confidence_score": 0.85, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "270d932f-20c3-4066-aa5d-c1cbf830745a", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is blank, but Person Interviewed is present, compliance met.", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/Comment", "Report/PersonInterviewedSection/PersonInterviewed"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3a8d13f0-a880-480b-8e06-826d742f384a", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; no relevant tag found in XML.", "confidence_score": 0.98, "relevant_sections": ["Report/SanctionSearchPerformed"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e86a836f-934e-4598-a84e-ad951d30813e", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanctions present, Risk level 'High', Credit opinion and Max Credit are blank, compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection/Sanctions", "Report/TradeRiskAssessmentSection/RiskLevel", "Report/CreditOpinionSection/CreditOpinion", "Report/CreditOpinionSection/MaxCredit", "Report/CreditOpinionSection/OpinionOnMaxCredit"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c559bade-914c-42e1-9e63-545d87d11f8c", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client 'TURKEXIM'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "b77443ed-2d71-4e72-b49b-cde892cf00c5", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in RelatedEntitiesLegalStatusSection.", "confidence_score": 0.9, "relevant_sections": ["RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "72ae8544-64b3-4da6-801f-a34627391b38", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type is 'Select' but no contact details provided.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "70bcf4b7-89c4-4327-b915-d5037049e4d1", "question_number": 36, "question": "Capital missing currency", "summary": "Capital field is missing currency information, violates compliance.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "afb9362b-b133-4e39-9d5e-feba2cd45196", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder 'atest' is not populated as a member of a related entity group.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder/Name", "RelatedEntitiesSection/RelatedEntity/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8a9dacbf-4e01-4137-8687-29c13cf923f3", "question_number": 38, "question": "Calculate credit opinion", "summary": "Credit opinion calculation cannot be verified due to missing payment data.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Shareholders", "Report/RelatedEntitiesSection/RelatedEntitiesFinancialSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "39ceabd6-eb57-4d69-b57b-10f268d03603", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'RE test 2' does not match shareholder names 'test 2', 'atest', etc.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/Shareholders/Shareholder/Name"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4518f416-5429-407e-b88e-ec7dc88d9c0a", "question_number": 40, "question": "Town missing in town box", "summary": "Town 'test' is present, compliant with validation rule.", "confidence_score": 1.0, "relevant_sections": ["AddressesSection/Addresses/Address/Town"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4a3a526c-3f89-4e8f-afdc-d320244f44f7", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is 'Active', complies with credit granting rules.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 12:50:14.052793", "processing_time": 54.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": null, "rag_enabled": true, "permanent_question_bank_used": true}