{"validation_id": "38227aeb-115a-4891-bfef-d61f4e89f92c", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "84921ed6-1d5c-4d05-bbd9-680fedb72d42", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "9f8cbcdb-9b79-477a-bb88-1b54022e3b8a", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "93caf1d2-c946-462e-9118-eaeef5369d53", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name field is not present in the XML, thus comments and payment details are not mandatory.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0d6df91b-98ea-4a52-889d-a34a388c5ee2", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; thus, Client Specific Comments and Payment sections should be empty.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "85fd6dbd-6233-4bfb-a90f-6c9bdef27740", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit is not provided; Total Income comparison cannot be validated.", "confidence_score": 0.9, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "414e7408-692a-4564-9efb-86459010537a", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses for raw materials and supplies found in Direct Costs or Operating Costs sections.", "confidence_score": 0.9, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3771500d-e9d9-4d14-9bc7-501a6146e288", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name 'GenAI25' matches official company name 'GenAI25'; no client comments provided.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "26680059-5fc8-49dc-90d3-f20a80aa88c4", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific comment is not populated.", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "324a3b49-c0a4-41de-9c3d-c15bc412aa0a", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "No max credit in EUR, USD, GBP noted; special note section lacks relevant credit information.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ee9c1ac1-ba86-415f-89fa-07a583189dd6", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "No registration or license numbers are recorded in the report's legal section or special notes.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5a716fc1-1254-4e87-a830-fda9e487246d", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language detected in the report's special notes section.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "48d02b2a-f64c-4f50-ba56-01b76f8e8681", "question_number": 12, "question": "Spelling Check", "summary": "Company GenAI25 is active with a legal status of Civil Partnership since 01-Jan-2025.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/LegalStatusSection/LegalStatus", "Report/LegalStatusSection/DateStarted"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9737e0a6-4963-45db-9c89-e97273c52d1f", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements are referenced in the payments section of the report.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "dc5e8f61-e832-4bb0-9f20-1c7d33b4dc4e", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Company GenAI25 is active with a paid-up capital of 123.00 as of 01-Jul-2025.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/CompanyStatus", "Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "448d4f23-df4f-4dff-bba3-ad9fe08b57ce", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Company GenAI25 is classified as a Civil Partnership with an active status as of 01-Jan-2025.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/LegalStatus", "Report/LegalStatusSection/CompanyStatus", "Report/LegalStatusSection/DateStarted"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e9462b1f-9805-49c0-9eee-7da11ef96943", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks details related to GenAI25's non-commercial activities in India.", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ICPCountryName", "Report/LegalStatusSection/ICPLegalGroup"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d5be8e37-3032-4ffc-be8a-26a9ab87b597", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Company GenAI25 is classified as a Civil Partnership with a paid-up capital of 123.00.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/LegalStatus", "Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a04bf855-d6ce-4b45-a75c-ec60938b5c8e", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Neither Income nor Total Income is present in the XML data.", "confidence_score": 0.98, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "05a2c80d-3606-4c27-bf79-f5f8bd0e8b2d", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Credit opinion not given for GenAI25, TRA is high as indicated in Special Notes.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "63ad7b7a-89de-45b5-8afe-3c8ac97f498a", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification is not applicable; refer to report for details.", "confidence_score": 0.9, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9a621162-0276-4113-8092-ffbb81089bef", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is Active; no classification for dormant or ceased trading found.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d2ece753-2ab5-4d0f-b32e-21b9e286cd09", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number is missing from Related entities; Transfer to Principal is set to false.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8736f210-a78c-45d4-b069-305a0f3b5052", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "No registration number is present; thus, no expiry comment is available.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "033653f3-c9bd-4a9f-a683-73570f564223", "question_number": 24, "question": "Small with large amount", "summary": "GenAI25 has a paid-up capital of 123.00 and is classified as a Civil Partnership.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ef70081e-6f06-4683-8fa8-8a87599f07c3", "question_number": 25, "question": "Large with small amount", "summary": "The paid-up capital for GenAI25 is 123.00, indicating a large entity with a small amount.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "14f37611-0c65-4efe-99d1-6304e50b004c", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications should align with the active status of the company, which is 'Active' as of 01-Jan-2025.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/LegalStatusSection/DateStarted"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "cc2b06b2-5ec1-447f-bc88-be9236d443ec", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Research type is 'Negative'; address details and general info are present but no special notes found.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "67a10032-3a15-4dd0-a0aa-32cfea3c1c23", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research type is 'Decline'; no contact confirmed with GenAI25 as of 01-Jul-2025.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/Date", "Report/HeaderSection/CompanyName"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "adcaa48c-b265-4f51-b0ef-a83ae7d2acae", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "The report does not indicate a decline in information provision by GenAI25.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "03137a28-7684-4185-896b-353679a3b3fe", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is blank; no comment on payments or Person Interviewed present.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/Date", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9fbeeccd-226f-49d0-9d19-a1766c748dca", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "The XML does not indicate if a sanctions search was performed, as the relevant tag is missing.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "38474a67-efa4-432e-be6b-bffba9267af3", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No sanctions found in the report as no relevant sections indicate sanctions present.", "confidence_score": 0.95, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "46934a57-36fa-47e1-8a53-43b1aba597cf", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "e93129be-e335-4d30-a76f-0f9af0fc88d4", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon is missing in the LegalStatusSection.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f4dcb6f8-a7c7-4f8c-81b0-c18a70623c89", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No telephone number or email address is present in the address section of the report.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "cdfc699a-7cb2-4023-8b70-fb30de7bdbf9", "question_number": 36, "question": "Capital missing currency", "summary": "The Capital currency field is blank as no currency is specified in the XML content.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c2ea78b9-b78f-4c48-baa3-369597df3187", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder information is not populated in the report for company GenAI25.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/ParentCompany"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "188ba030-a52b-4f47-84c2-9a304ffe88f2", "question_number": 38, "question": "Calculate credit opinion", "summary": "The paid-up capital of GenAI25 is 123.00 as of 01-Jan-2025.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/DateStarted"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "99d0b30e-4098-46f7-924c-f0685203e083", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "CompanyName 'GenAI25' matches Requested 'GenAI25', no alert triggered for mismatch.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f826a8ef-644f-4afa-8546-2bcfafb9c9fa", "question_number": 40, "question": "Town missing in town box", "summary": "The town box is empty as no town information is provided in the AddressesSection.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "04723332-279c-4206-a029-222655aad02d", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is 'Active', thus credit can be granted as per legal section.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 11:29:58.125665", "processing_time": 15.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}