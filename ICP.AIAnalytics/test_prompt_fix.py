#!/usr/bin/env python3
"""
Test the updated prompts with XML structure guidance.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.models.schemas import Question

def test_prompt_fix():
    """Test the updated prompts with XML structure guidance."""
    print("Testing Updated Prompts with XML Structure Guidance")
    print("=" * 55)
    
    validation_service = ValidationService()
    current_date = validation_service.get_current_date()
    
    # Create a temporal validation question
    temporal_question = Question(
        id="test-temporal",
        question="If any registration number has expired, a comment should be added explaining the expiry",
        category="temporal_validation",
        client_code=None,
        darwin_reference_sections="LegalStatusSection/RegistrationNumbers",
        expected_outcome="approved",
        client_specific_type="All"
    )
    
    # Sample XML content with nested RegistrationNumber structure
    xml_content = """
    <Report>
        <LegalStatusSection>
            <RegistrationNumbers>
                <RegistrationNumber>
                    <ICPRegistrationNumberName>Commercial Registration Number</ICPRegistrationNumberName>
                    <RegistrationNumberValue>************</RegistrationNumberValue>
                    <DateIssued>12-Jan-2024</DateIssued>
                    <DateExpired>12-Jan-2025</DateExpired>
                    <Comments>Commercial Registration has expired and not been renew by the owner of the company</Comments>
                </RegistrationNumber>
            </RegistrationNumbers>
        </LegalStatusSection>
    </Report>
    """
    
    print(f"Current date: {current_date}")
    print("XML contains registration number ************ expired 12-Jan-2025")
    print()
    
    print("Testing enhanced validation prompt...")
    enhanced_prompt = validation_service._build_enhanced_validation_prompt(
        temporal_question, 
        xml_content, 
        1, 
        retry=False, 
        focus_prompt=None
    )
    
    # Check for key elements in the prompt
    checks = [
        (f"CURRENT DATE: {current_date}", "Current date declaration"),
        ("TEMPORAL VALIDATION ALERT", "Temporal alert"),
        ("RegistrationNumbers may contain nested RegistrationNumber", "XML structure warning"),
        ("<RegistrationNumbers><RegistrationNumber>", "XML structure example"),
        ("************", "Specific registration number example"),
        ("12-Jan-2025", "Specific expiry date example"),
    ]
    
    print("Enhanced prompt validation:")
    for check_text, description in checks:
        if check_text in enhanced_prompt:
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    print()
    
    print("Testing holistic validation prompt...")
    holistic_prompt = validation_service._build_holistic_validation_prompt(
        [temporal_question],
        xml_content,
        {},
        focus_prompt=None
    )
    
    holistic_checks = [
        (f"CURRENT DATE: {current_date}", "Current date declaration"),
        ("TEMPORAL VALIDATION DETECTED", "Temporal detection"),
        ("RegistrationNumbers contains nested RegistrationNumber", "XML structure warning"),
        ("Look INSIDE RegistrationNumber elements", "Structure instruction"),
    ]
    
    print("Holistic prompt validation:")
    for check_text, description in holistic_checks:
        if check_text in holistic_prompt:
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    print()
    print("=" * 55)
    print("Prompt fix test completed!")
    print()
    print("The prompts now include:")
    print("1. ✅ Explicit XML structure guidance")
    print("2. ✅ Warning about nested RegistrationNumber elements")
    print("3. ✅ Example XML structure")
    print("4. ✅ Current date injection")
    print("5. ✅ Clear instructions to look INSIDE RegistrationNumber elements")
    print()
    print("This should help the LLM correctly identify registration numbers")
    print("in the nested XML structure!")

if __name__ == "__main__":
    test_prompt_fix()
