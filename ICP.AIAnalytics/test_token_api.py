#!/usr/bin/env python3
"""
Test script for the token analysis API endpoints.
"""

import asyncio
import json
from fastapi.testclient import TestClient
from app.main import app

def test_token_analysis_endpoints():
    """Test the token analysis API endpoints."""
    
    print("Testing Token Analysis API Endpoints")
    print("=" * 50)
    
    # Create test client
    client = TestClient(app)
    
    # Test 1: Get token info
    print("\n1. Testing /validate/token-info endpoint...")
    response = client.get("/api/v1/validate/token-info")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ Model: {data['model']}")
        print(f"   ✓ Token limit: {data['token_limit']:,}")
        print(f"   ✓ Encoding: {data['encoding']}")
        print(f"   ✓ Sample cost (1K input + 500 output): ${data['sample_cost']['estimated_cost']:.6f}")
    else:
        print(f"   ✗ Failed: {response.status_code} - {response.text}")
    
    # Test 2: Analyze tokens for a sample request
    print("\n2. Testing /validate/token-analysis endpoint...")
    
    test_data = {
        "questions": [
            "Is the company name spelled correctly?",
            "Are all financial figures properly formatted?",
            "Does the report comply with regulatory standards?"
        ],
        "xml_content": """
        <report>
            <company>Test Company Ltd</company>
            <revenue currency="GBP">1000000</revenue>
            <profit currency="GBP">200000</profit>
        </report>
        """,
        "focus_prompt": "Focus on accuracy and compliance"
    }
    
    response = client.post("/api/v1/validate/token-analysis", json=test_data)
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ Model: {data['model']}")
        print(f"   ✓ Total input tokens: {data['input_analysis']['total_input_tokens']:,}")
        print(f"   ✓ Estimated cost: ${data['input_analysis']['estimated_cost']:.6f}")
        print(f"   ✓ Usage percentage: {data['limit_check']['usage_percentage']:.2f}%")
        print(f"   ✓ Within limit: {data['limit_check']['within_limit']}")
        
        if data['recommendations']:
            print("   ✓ Recommendations:")
            for rec in data['recommendations']:
                print(f"     - {rec}")
        else:
            print("   ✓ No recommendations (optimal usage)")
            
        # Show detailed breakdown
        breakdown = data['input_analysis']['token_breakdown']
        print("   ✓ Token breakdown:")
        print(f"     - XML content: {breakdown['xml_content']:,}")
        print(f"     - Questions: {breakdown['questions']:,}")
        print(f"     - RAG data: {breakdown['rag_data']:,}")
        print(f"     - Focus prompt: {breakdown['focus_prompt']:,}")
        print(f"     - System prompt: {breakdown['system_prompt']:,}")
        print(f"     - Instructions: {breakdown['instruction_prompt']:,}")
        
    else:
        print(f"   ✗ Failed: {response.status_code} - {response.text}")
    
    # Test 3: Test with large content to trigger recommendations
    print("\n3. Testing with large content (should trigger recommendations)...")
    
    large_xml = "<report>" + "<data>test</data>" * 1000 + "</report>"
    many_questions = [f"Question {i}?" for i in range(25)]
    
    large_test_data = {
        "questions": many_questions,
        "xml_content": large_xml,
        "focus_prompt": "This is a very detailed focus prompt that explains exactly what we want to validate in great detail with many specific requirements and criteria that need to be checked thoroughly."
    }
    
    response = client.post("/api/v1/validate/token-analysis", json=large_test_data)
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ Total input tokens: {data['input_analysis']['total_input_tokens']:,}")
        print(f"   ✓ Estimated cost: ${data['input_analysis']['estimated_cost']:.6f}")
        print(f"   ✓ Usage percentage: {data['limit_check']['usage_percentage']:.2f}%")
        
        if data['recommendations']:
            print("   ✓ Recommendations triggered:")
            for rec in data['recommendations']:
                print(f"     - {rec}")
        
    else:
        print(f"   ✗ Failed: {response.status_code} - {response.text}")
    
    print("\n" + "=" * 50)
    print("Token Analysis API Test Completed")
    print("\nThese endpoints can be used to:")
    print("• Estimate costs before running validation")
    print("• Optimize content to stay within token limits")
    print("• Monitor token usage patterns")
    print("• Get model-specific pricing information")


if __name__ == "__main__":
    test_token_analysis_endpoints()
