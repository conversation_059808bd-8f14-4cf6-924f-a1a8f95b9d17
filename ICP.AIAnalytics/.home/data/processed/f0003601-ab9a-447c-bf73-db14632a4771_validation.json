{"validation_id": "f0003601-ab9a-447c-bf73-db14632a4771", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "d67b0c90-2273-4a73-be06-aa2608df0119", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "fd1ca69a-3525-4479-8dbd-533dd64516b8", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f2a5063e-ed42-43db-8f93-fc0ee84d3b48", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is missing; cannot verify compliance with required fields.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/Payments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a2cced8e-c212-4d84-8a97-e267f7aa03d8", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; comments and payment notes must not be present.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0cbc36b5-5087-473f-a7b7-2435e2a6e80a", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify as Gross Profit and Total Income data are missing.", "confidence_score": 0.85, "relevant_sections": ["Report/FinancialSection/GrossProfit", "Report/FinancialSection/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4ed130d6-509d-431b-bb8a-9b0972a0ab98", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses data found to check compliance with cost classification.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3c22b390-a6de-45a6-a7fb-8e9306ea41a8", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name matches Company Name; Client Specific Comments not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "48345cff-9d09-4c36-8720-5e7807394db3", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific comment missing.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7bfd189c-8ae0-4f59-8928-3223e269bc77", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not specified in special notes; rule violated.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "48baaf36-d88f-47c5-b47e-d7e880cd4927", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License and registration numbers are missing from the report sections.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0a67d7b7-3dbc-472e-bca3-97c7edd4b7b8", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/WaiverSection/Waiver"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6ba398c8-6547-442c-8742-7be878113196", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check passed; no errors found in specified sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "924755ff-bf9c-492f-bff8-7743068be281", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Payments section lacks reference to significant changes; rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "acb94714-42ae-4750-8af6-f14769b45a76", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No adverse announcements found; compliance with validation rule not met.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges", "Report/Payment"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "88fd417c-644e-45eb-86db-6cb0bd8d7eb1", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance rule cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b6907788-1a74-41a3-b225-de93b254701d", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant sector information from Subject activities.", "confidence_score": 0.98, "relevant_sections": ["Report/Activities/Subject Activities", "Report/Payments/Credit Opinion Notes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3c63ed34-f9ff-4fa2-97d6-094e22291e37", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Violation found: Incorrect texts added regarding export activities.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0d5a5726-f916-4124-a771-9717e5b920a7", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Neither Income nor Total Income is present in the report.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitLossSection/Income", "Report/ProfitLossSection/TotalIncome"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c8098efa-4b4d-4f37-8220-b7ec907656df", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "No currency requested, credit not given; compliance violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/Capital/NotGiven"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "39b0745d-7236-4c0a-84dc-dadb3a77894c", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not added; compliance violation detected.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6a83a4b3-448c-4446-ac67-baf73620a7ce", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is active; no violation found for dormant classification.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5962839f-29d8-42e4-9e86-8221dd24dc89", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not provided; compliance cannot be verified.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "56ab69d0-f67a-4596-bae8-c6c41940617c", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no Comment present.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bf4dade7-67bb-4cef-8dfd-075f63a037d6", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp amount 123.00 is small compared to the large amount expected, rule violated.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f78651ab-d890-4462-b038-989e5aff2f46", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital of 123.00 is small compared to the company's large status, violating the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4e4263d3-19c6-42aa-8450-a5818392b8c0", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications are not present for compliance checking.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "124df6b1-7641-4097-b3a1-c2a59bcf6589", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation failed: 'Negative' research type not specified; other sections are populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "40a51288-fba5-472d-bba8-eeb8f917c7a6", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Validation failed: Research Type is not 'No Contact' as required.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "9eb5cd41-4a34-419c-a4ed-7b91439dc156", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Validation failed: Payments section lacks comment on information decline.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/PaymentsSection/CreditOpinionNotes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "362a9e0f-8b8f-49cf-933a-8accbfcde54b", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation failed: Research Type is missing, no comment on payments, and no Person Interviewed.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "aa38ef03-5d88-495a-b1d8-3f201b4368af", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b956ef6a-5be8-4230-9930-9af781e161bf", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No sanctions found; compliance check failed on sanctions requirement.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "93fcddb6-389f-4b22-a554-ed288231fc7e", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "54610884-398f-4a1f-ba36-5d9e211d3ecc", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon is missing in Header and LegalStatus sections.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "44cd1f9a-7029-418e-9ba8-440a3763ac2f", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details present in the address section as required.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5a4e806a-1169-498c-b16a-19c9a6c02c0a", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; compliance not met.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b99be1ed-c7f4-47f9-9859-e05e695237a5", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder not populated while related entities exist; rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6595078e-119e-4fec-86bc-ff0c2d43f015", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot verify credit opinion calculation; no payment data present.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b9eef7b2-6e06-46ba-9268-c31087ace545", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Names in financials match header; compliance rule satisfied.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "dd64d0cd-fc6f-4aa8-a4b9-87e5cae7a633", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty; report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c5505068-58e5-4ddf-ac90-ad38c02a088d", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is active; credit can be granted as per validation rule.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 14:19:04.424123", "processing_time": 13.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}