{"file_id": "777f9039-3d36-4717-8fc8-5a14a80a9f31", "xml_structure": {"Report": {"@xmlns:xsd": "http://www.w3.org/2001/XMLSchema", "@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "HeaderSection": {"Date": "09-Nov-2023", "DeliveryDate": "14-Jul-2025", "OurRef": "999039.1.1", "YourRef": "0136058279", "CompanyName": "MOGZAURI PLUS T/A MPLUS", "Requested": "MOGZAURI PLUS T/A MPLUS"}, "LegalStatusSection": {"Capital": {"NotGiven": "false"}, "ICPLegalGroup": "Government", "LegalStatusCode": "3500", "LegalStatus": "Government Ministry"}, "SpecialNotesSection": {"TransferToPrincipal": "false"}, "WaiverSection": {"Waiver": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP."}}}, "text_content": [{"path": "Report/HeaderSection/Date", "tag": "Date", "text": "09-Nov-2023", "attributes": {}}, {"path": "Report/HeaderSection/DeliveryDate", "tag": "DeliveryDate", "text": "14-Jul-2025", "attributes": {}}, {"path": "Report/HeaderSection/OurRef", "tag": "OurRef", "text": "999039.1.1", "attributes": {}}, {"path": "Report/HeaderSection/YourRef", "tag": "YourRef", "text": "0136058279", "attributes": {}}, {"path": "Report/HeaderSection/CompanyName", "tag": "CompanyName", "text": "MOGZAURI PLUS T/A MPLUS", "attributes": {}}, {"path": "Report/HeaderSection/Requested", "tag": "Requested", "text": "MOGZAURI PLUS T/A MPLUS", "attributes": {}}, {"path": "Report/LegalStatusSection/Capital/NotGiven", "tag": "NotGiven", "text": "false", "attributes": {}}, {"path": "Report/LegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Government", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "3500", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Government Ministry", "attributes": {}}, {"path": "Report/SpecialNotesSection/TransferToPrincipal", "tag": "TransferToPrincipal", "text": "false", "attributes": {}}, {"path": "Report/WaiverSection/Waiver", "tag": "Waiver", "text": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP.", "attributes": {}}], "root_element": "Report", "namespace": {}, "processing_timestamp": "2025-07-14T18:33:50.744870"}