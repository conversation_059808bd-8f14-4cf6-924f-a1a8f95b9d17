{"validation_id": "abb5fdea-b748-4036-a7c4-74095879fecc", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "8ef73ffa-3c2e-4a03-b8b7-afce075dc382", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "e540e4b3-f52c-4857-8141-f11b5bce5ede", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "b5cd365d-a693-4e3d-9282-a65c541007be", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is missing; cannot verify compliance for comments and payment sections.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d01c02f4-e2ed-492d-9017-60947729feb2", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; Client Specific Comments and Payment Section must not have notes.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5cb99f9e-3b14-43e7-b9ec-3ec57a775e7c", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit and Total Income; data not provided.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c041f59b-31b0-41f6-a0d7-9fa9cff4130e", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Cannot verify expenses section; no financial data provided for compliance check.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "48557cdd-5bcf-4b73-a2e2-b6cd6e44ba3e", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name matches Company Name; Client Specific Comments not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3c8b30f3-c829-476e-854d-574b83957791", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific Comments not populated.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ce6d76a2-5946-49e6-8ecb-51666485196c", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special notes; compliance violated.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c926d559-6e0b-4160-b2e3-c830d53e14ac", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Missing registration/license numbers in legal section and special notes.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "88f16138-8ef7-4672-b281-a2a4692ca025", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ada0e49e-e620-43af-bf58-81a35e41a787", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check passed; no violations found in relevant sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c8b2ee2f-8f90-46e0-a8d9-832a02a1cb14", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Payments section lacks reference to significant changes; rule violated.", "confidence_score": 0.97, "relevant_sections": ["Report/LegalStatusSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "af3838ae-87cc-4625-a34b-3215aa056323", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Validation failed: Missing required sections for adverse announcements.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges", "Report/Payments"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "beb60c47-c323-47c7-9d38-8cc0594e31b9", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c631d42b-e02d-422d-b7fe-8ecacb00e3b8", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant information from Subject activities.", "confidence_score": 0.98, "relevant_sections": ["Report/Activities/SubjectActivities", "Report/Payments/CreditOpinionNotes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a241961b-d727-45fb-a4f6-c6e9bf32f510", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Violation found: incorrect texts added in services activities.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "49cc09f7-0711-4baa-a6c3-70bed27af76b", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing, violating the validation rule.", "confidence_score": 0.95, "relevant_sections": ["Report/ProfitLossSection/Income", "Report/ProfitLossSection/TotalIncome"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6653847b-efa7-47c4-a7ab-ae6d416bc7dc", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Violation: No credit opinion for requested currency, TRA high not met.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a5c37aa8-eaf9-408b-b220-de235566fc4f", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not added where applicable; violation found.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ef626d1b-5a9d-4593-8791-fdf5a2947afe", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is active; no dormant or ceased trading classification found.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "912f917c-605b-4091-a82b-52ebfb29d52f", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not provided; compliance check cannot be verified.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "36ff311b-410b-436c-b438-193c393443be", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no Comment present.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4a119d0b-f88f-4c73-87f2-6ed73ada0d16", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp capital of 123.00 is small compared to the business scale, violating the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "01f228df-efa9-47a6-8134-77592302e397", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital of 123.00 violates the large with small amount rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f4fd8875-6c3f-4a5d-94b8-62edf68c32d9", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "No position classifications found to verify compliance.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f4a35b75-fa07-4028-aa9a-e53d8ff44d13", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation failed: 'Research Type' not set to 'Negative'; other sections populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3571d75a-bd57-4a18-8f24-f7632c4ede5d", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline' but Person Interviewed is missing, rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection/PersonInterviewed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "feeef9d1-3448-4252-83fd-6ac3a8c36554", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type is missing; cannot verify compliance with decline rule.", "confidence_score": 0.85, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "75f0ef56-a2e8-4151-96fa-b43fd50880c3", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation failed: Research Type is blank, missing comments and Person Interviewed.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "e4be13b8-7d29-4a3f-959e-a552dce8600f", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "82605918-1e62-4d6c-8af5-a2215b3f94a3", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanction not found; compliance rule violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed", "Report/SanctionSection/SanctionFound"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "249c3c57-b1f0-4bc2-99ca-424b9ce5cf14", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "d962cffd-895f-4c60-80fe-fdd2c4a0c2c4", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "18f20da7-3af6-45d7-9fe4-b532dfad74a0", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details present in the address section, violating the validation rule.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4132fc13-a883-489c-985f-46edfb04442d", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; expected blank notification.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0d714ff0-a485-41f2-a21a-e41cec309f7e", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder information is missing despite related entities present.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f8b47a1c-1c17-4a49-9e19-ec8478e472a9", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot verify credit opinion calculation; no payment data present.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1c7a425a-c3c8-4b7f-a58c-46499118e3f5", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Names in financials match header; compliance rule satisfied.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "02bf25cd-3719-4438-9ee7-805d3a6a6d03", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty; report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "280c0e72-86b4-40e8-b9c2-e0697e0567cd", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is active; credit can be granted without issue.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/LegalStatusSection/LegalStatus"], "status": "rejected", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 16:20:47.786176", "processing_time": 15.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}