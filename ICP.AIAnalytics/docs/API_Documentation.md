# XML Validation System API Documentation

This API provides endpoints for uploading XML reports, questions files, and performing validation using a permanent question bank with RAG (Retrieval-Augmented Generation) capabilities.

## Base URL
```
http://localhost:8000/api/v1
```

## Authentication
Currently, no authentication is required for local API usage.

## Endpoints

### 1. Upload XML Report

#### From File Upload
**POST** `/upload/report`

Upload an XML report file directly.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: file (XML file)

**Response:**
```json
{
    "file_id": "550e8400-e29b-41d4-a716-************",
  "filename": "report.xml",
  "file_type": ".xml",
    "file_size": 2048,
    "upload_timestamp": "2024-01-15T10:30:00Z",
    "status": "uploaded"
}
```

#### From External API
**POST** `/upload/report-from-api`

Fetch an XML report from an external API.

**Request:**
```json
{
    "report_id": "RPT-2024-001",
    "bearer_token": "your-api-token"
}
```

**Response:**
```json
{
    "file_id": "550e8400-e29b-41d4-a716-************",
    "filename": "report_RPT-2024-001_xml.xml",
  "file_type": ".xml",
  "file_size": 2048,
    "upload_timestamp": "2024-01-15T10:30:00Z",
  "status": "uploaded",
  "source": "external_api",
    "report_id": "RPT-2024-001"
}
```

### 2. Validate Report (New - Using report_id)

**POST** `/validate`

Validate an XML report against the permanent question bank.

**Request:**
```json
{
    "report_id": "RPT-2024-001",
  "validation_options": {
    "batch_size": 5,
    "include_low_confidence": true,
    "min_confidence_threshold": 0.3,
    "focus_prompt": "Focus on payment-related sections and analyze currency compliance in detail"
    },
    "enable_client_filtering": true,
    "order_details_params": {
        "csr_id": "CSR123",
        "copy": "final",
        "version": "v1.0"
    },
    "direct_client_code": "TURKEXIM"
}
```

**Key Changes:**
- **`report_id`**: Now uses `report_id` instead of `report_file_id`
- **Flexible Input**: Can accept either:
  - **External API report_id**: From reports fetched via `/upload/report-from-api`
  - **File upload file_id**: For backward compatibility with direct file uploads
- **`validation_options.focus_prompt`** (Optional): Frontend input to regenerate report with specific focus. Can include:
  - New questions or angles to explore
  - Specific sections to emphasize in analysis
  - Additional context or requirements for validation
  - Queries regarding specific aspects of the report

**Response:**
```json
{
    "validation_id": "750e8400-e29b-41d4-a716-************",
  "status": "completed",
    "questions_file_id": "permanent",
    "report_id": "RPT-2024-001",
    "total_questions": 41,
    "processed_questions": 41,
    "skipped_questions": 3,
  "results": [
    {
            "question_id": "q1",
      "question_number": 1,
            "question": "What is the primary client information?",
            "summary": "Primary client is ABC Corporation Ltd, registered in London",
            "confidence_score": 0.95,
            "relevant_sections": ["client_info", "corporate_structure"],
            "status": "approved",
            "client_match_status": "skipped"
        }
    ],
    "validation_timestamp": "2024-01-15T10:35:00Z",
    "processing_time": 45.2,
    "client_filtering_enabled": true,
    "order_client_code": "TURKEXIM",
    "rag_enabled": true,
    "permanent_question_bank_used": true
}
```

### 3. Legacy Upload Questions (For Manual Question Bank)

**POST** `/upload/questions`

Upload an Excel file containing validation questions (only needed if not using permanent question bank).

### 4. Get Order Details (Client Code Resolution)

**POST** `/upload/order-details`

Fetch order details from external API to resolve client codes for filtering.

### 5. Permanent Question Bank Management

#### Upload New Question Bank
**POST** `/validate/permanent-questions/upload`

#### Get Status
**GET** `/validate/permanent-questions/status`

#### Reload Question Bank
**POST** `/validate/permanent-questions/reload`

### 6. Validation Management

#### Get Validation Status
**GET** `/validate/status/{validation_id}`

#### Get Validation Results
**GET** `/validate/result/{validation_id}`

#### Get Validation Summary
**GET** `/validate/summary/{validation_id}`

#### Delete Validation
**DELETE** `/validate/{validation_id}`

#### Batch Validation
**POST** `/validate/batch`

## Usage Examples

### Example 1: External API Report Validation
   ```bash
# 1. Fetch report from external API
   curl -X POST "http://localhost:8000/api/v1/upload/report-from-api" \
     -H "Content-Type: application/json" \
     -d '{
    "report_id": "RPT-2024-001",
    "bearer_token": "your-api-token"
     }'

# 2. Validate using report_id
   curl -X POST "http://localhost:8000/api/v1/validate" \
     -H "Content-Type: application/json" \
     -d '{
    "report_id": "RPT-2024-001",
    "enable_client_filtering": true,
    "validation_options": {
      "batch_size": 5,
      "include_low_confidence": true
    }
     }'
   ```

### Example 2: File Upload Report Validation
   ```bash
# 1. Upload XML file directly
   curl -X POST "http://localhost:8000/api/v1/upload/report" \
  -H "Content-Type: multipart/form-data" \
     -F "file=@report.xml"

# 2. Validate using the returned file_id as report_id
   curl -X POST "http://localhost:8000/api/v1/validate" \
     -H "Content-Type: application/json" \
     -d '{
    "report_id": "550e8400-e29b-41d4-a716-************",
    "enable_client_filtering": false
     }'
   ```

## Response Formats

All endpoints support both JSON and XML response formats:
- JSON (default): Standard JSON responses
- XML: Add `?response_format=xml` to any endpoint

## Error Handling

Standard HTTP status codes are used:
- 200: Success
- 400: Bad Request
- 404: Not Found
- 422: Unprocessable Entity
- 500: Internal Server Error

Error responses include detailed error messages:
```json
{
    "detail": "Report not found or not processed"
}
```

## Features

### Permanent Question Bank
- **41 questions** loaded from `Copy of Prompts Checking AI (1).xlsx`
- **Client filtering** with 3 client-specific questions
- **Enhanced metadata** including Darwin Reference Sections and Expected Outcomes

### Client Filtering
- Questions marked as `"skipped"` when client codes don't match
- All questions processed and returned (no filtering out)
- Status tracking: `"matched"`, `"skipped"`, `"no_client_code"`

### RAG (Retrieval-Augmented Generation)
- Vector database integration with ChromaDB
- Semantic search and content extraction
- Enhanced LLM analysis with context-aware validation

### Validation Statuses
- **`approved`**: Clear, positive/satisfactory results
- **`rejected`**: Clear, negative/unsatisfactory results  
- **`manual_intervention_needed`**: Insufficient/unclear information

## Migration Notes

### Changes from v1.0
- **ValidationRequest**: `report_file_id` → `report_id`
- **ValidationResponse**: `report_file_id` → `report_id`
- **Backward Compatibility**: File upload `file_id` can still be used as `report_id`
- **Enhanced Workflow**: Supports both external API reports and direct file uploads

The system now provides a unified interface where `report_id` can represent either:
1. An external API report ID (e.g., `"RPT-2024-001"`)
2. A file upload ID (e.g., `"550e8400-e29b-41d4-a716-************"`)

This change simplifies the API and makes it more intuitive for external integrations. 

## Enhanced Validation API

The validation API has been enhanced to support both traditional validation of existing reports and combined upload+validation from external API in a single call.

### Endpoint: POST `/api/v1/validate`

This endpoint now supports two modes of operation:

1. **Traditional Mode**: Validate an existing report that was previously uploaded
2. **Upload & Validate Mode**: Upload a report from external API and immediately validate it

#### Request Schema

```json
{
  "report_id": "string",              // Required: Report ID
  "bearer_token": "string",           // Optional: Used for both upload and order details API calls
  "validation_options": {},           // Optional: Validation configuration
  "enable_client_filtering": true,    // Optional: Enable client-specific filtering
  "order_details_params": {           // Optional: For fetching client code from API
    "csr_id": "string",
    "copy": "string", 
    "version": "string"
    // Note: bearer_token is inherited from parent if not specified here
  },
  "direct_client_code": "string",     // Optional: Direct client code
  "async_processing": false           // Optional: Run in background
}
```

**Note**: The `bearer_token` field serves dual purposes:
1. **Upload**: If provided, uploads the report from external API first
2. **Order Details**: Used for order details API calls when `order_details_params` is provided but doesn't include its own bearer_token

#### Usage Examples

##### 1. Traditional Validation (Existing Report)
```bash
curl -X POST "http://your-api/api/v1/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493",
    "direct_client_code": "ONLINEMISC"
  }'
```

##### 2. Upload & Validate in Single Call (Synchronous)
```bash
curl -X POST "http://your-api/api/v1/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493",
    "bearer_token": "your_bearer_token_here",
    "direct_client_code": "ONLINEMISC",
    "async_processing": false
  }'
```

##### 3. Upload & Validate with Order Details API
```bash
curl -X POST "http://your-api/api/v1/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493",
    "bearer_token": "your_bearer_token_here",
    "enable_client_filtering": true,
    "order_details_params": {
      "csr_id": "12345",
      "copy": "1",
      "version": "2"
    }
  }'
```

##### 4. Upload & Validate in Single Call (Asynchronous)
```bash
curl -X POST "http://your-api/api/v1/validate?async_processing=true" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "181493",
    "bearer_token": "your_bearer_token_here",
    "direct_client_code": "ONLINEMISC"
  }'
```

#### Response Examples

##### Synchronous Response (with upload info)
```json
{
  "validation_id": "uuid",
  "status": "completed",
  "report_id": "181493",
  "total_questions": 41,
  "processed_questions": 38,
  "skipped_questions": 3,
  "results": [...],
  "validation_timestamp": "2025-07-11T12:29:31.982485",
  "processing_time": 15.2,
  "upload_info": {
    "file_id": "uuid",
    "filename": "report_181493_xml.xml",
    "file_type": ".xml",
    "file_size": 2320,
    "upload_timestamp": "2025-07-11T12:29:30.000000",
    "status": "uploaded",
    "source": "external_api",
    "report_id": "181493"
  }
}
```

##### Asynchronous Response (with upload info)
```json
{
  "validation_id": "uuid",
  "status": "processing",
  "message": "Validation started in background (report uploaded)",
  "check_status_url": "/api/v1/validate/status/uuid",
  "using_permanent_question_bank": true,
  "upload_info": {
    "file_id": "uuid",
    "filename": "report_181493_xml.xml",
    "file_type": ".xml",
    "file_size": 2320,
    "upload_timestamp": "2025-07-11T12:29:30.000000",
    "status": "uploaded",
    "source": "external_api",
    "report_id": "181493"
  }
}
```

#### Key Features

1. **Backward Compatibility**: Existing API calls without `bearer_token` work exactly as before
2. **Single API Call**: When `bearer_token` is provided, both upload and validation happen in one request
3. **Upload Information**: Response includes upload details when report is fetched from external API
4. **Error Handling**: Comprehensive error handling for both upload and validation phases
5. **Async Support**: Both synchronous and asynchronous processing modes supported

#### Benefits

- **Simplified Integration**: One API call instead of separate upload and validate calls
- **Atomic Operations**: Either both upload and validation succeed, or the entire operation fails
- **Performance**: Eliminates intermediate API calls and reduces latency
- **Consistency**: Single point of error handling and logging 