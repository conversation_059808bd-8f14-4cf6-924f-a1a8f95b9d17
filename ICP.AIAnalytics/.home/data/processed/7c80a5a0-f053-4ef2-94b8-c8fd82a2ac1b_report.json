{"file_id": "7c80a5a0-f053-4ef2-94b8-c8fd82a2ac1b", "xml_structure": {"Report": {"@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "@xmlns:xsd": "http://www.w3.org/2001/XMLSchema", "HeaderSection": {"Date": "29-Aug-2024", "DeliveryDate": "30-Aug-2024", "OurRef": "998772.22.1", "YourRef": "Test22.1", "CompanyName": "AishwaryaTest"}, "PaymentsSection": {"PaymentHistory": "PaymentsNoTrace", "CreditOpinion": "Large", "MaxCreditCurrency": "AMD", "MaxCredit": "2321", "OpinionOnMaxCredit": "TermsStipulated", "AdditionalNoteMaxCredit": "test", "TradeRiskAssessment": "Average", "CreditFigureCurrency": "AMD", "CreditFigure": "2122"}, "FinancialSection": {"BalanceSheetFigures": {"AccountType": "Consolidated (Subject & Subsidiaries)", "CompanyName": "AishwaryaTest", "ScaleTheme": "Units"}, "BalanceSheets": {"BalanceSheet": {"FiscalYear": "2024", "FiscalYearEndDate": "01 January", "InformationType": "Complete", "InformationLevel": "Full", "FinancialPeriodEndDate": "01-Jan-2024", "NumberOfMonths": "1", "Currency": "AUD", "Scale": "Units", "LineItems": {"LineItem": [{"Level1": "Assets", "Level2": "Total Non-Current Assets", "Level3": "Total Non-Current Assets", "Level4": "Subtotal", "SourceDescription": "Total Non-Current Assets", "Amount": "8268525.22", "KeyFinancialId": "1", "LineItemTypeId": "2"}, {"Level1": "Assets", "Level2": "Total Current Assets", "Level3": "Total Current Assets", "Level4": "Subtotal", "SourceDescription": "Total Current Assets", "Amount": "79987.00", "KeyFinancialId": "2", "LineItemTypeId": "2"}, {"Level1": "Liabilities", "Level2": "Total Liabilities", "Level3": "Total Liabilities", "Level4": "Total", "SourceDescription": "Total Liabilities", "Amount": "44000.00", "KeyFinancialId": "6", "LineItemTypeId": "3"}, {"Level1": "Total Assets less Liabilities", "Level2": "Total Assets less Liabilities", "Level3": "Total Assets less Liabilities", "Level4": "Total", "SourceDescription": "Total Assets less Liabilities", "Amount": "8304512.22", "KeyFinancialId": "7", "LineItemTypeId": "3"}, {"Level1": "Liabilities", "Level2": "Total Current Liabilities", "Level3": "Total Current Liabilities", "Level4": "Subtotal", "SourceDescription": "Total Current Liabilities", "Amount": "4000.00", "KeyFinancialId": "4", "LineItemTypeId": "2"}, {"Level1": "Assets", "Level2": "Total Assets", "Level3": "Total Assets", "Level4": "Total", "SourceDescription": "Total Assets", "Amount": "8348512.22", "KeyFinancialId": "3", "LineItemTypeId": "3"}, {"Level1": "Liabilities", "Level2": "Total Non-Current Liabilities", "Level3": "Total Non-Current Liabilities", "Level4": "Subtotal", "SourceDescription": "Total Non-Current Liabilities", "Amount": "40000.00", "KeyFinancialId": "5", "LineItemTypeId": "2"}, {"Level1": "Assets", "Level2": "Non Current Assets", "Level3": "Fixed Assets", "Level4": "Line Item", "SourceDescription": "testt", "Amount": "5000.00", "CategoryId": "1", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Non Current Assets", "Level3": "Deferred Tax Asset", "Level4": "Line Item", "SourceDescription": "test", "Amount": "-675.55", "CategoryId": "1", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Non Current Assets", "Level3": "Goodwill", "Level4": "Line Item", "SourceDescription": "12", "Amount": "-98.23", "CategoryId": "1", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Non Current Assets", "Level3": "Long Term Debtors", "Level4": "Line Item", "SourceDescription": "test3", "Amount": "454343.00", "CategoryId": "1", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Non Current Assets", "Level3": "Other Non-Current Assets", "Level4": "Line Item", "SourceDescription": "tes", "Amount": "7809956.00", "CategoryId": "1", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Current Assets", "Level3": "Short Term Non-Financial Investments", "Level4": "Line Item", "SourceDescription": "test", "Amount": "40000.00", "CategoryId": "2", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Current Assets", "Level3": "Cash & Bank - Asset", "Level4": "Line Item", "SourceDescription": "13", "Amount": "-234.00", "CategoryId": "2", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Current Assets", "Level3": "Inventories", "Level4": "Line Item", "SourceDescription": "123", "Amount": "123.00", "CategoryId": "2", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Current Assets", "Level3": "Other Current Assets", "Level4": "Line Item", "SourceDescription": "12", "Amount": "40098.00", "CategoryId": "2", "LineItemTypeId": "1"}, {"Level1": "Liabilities", "Level2": "Current Liabilities", "Level3": "Other Liabilities", "Level4": "Line Item", "SourceDescription": "test", "Amount": "4000.00", "CategoryId": "3", "LineItemTypeId": "1"}, {"Level1": "Liabilities", "Level2": "Non-Current Liabilities", "Level3": "Deferred Tax Liability", "Level4": "Line Item", "SourceDescription": "test", "Amount": "40000.00", "CategoryId": "4", "LineItemTypeId": "1"}]}}}, "ProfitAndLossFigures": {"AccountType": "Subject", "CompanyName": "AishwaryaTest", "ScaleTheme": "Units"}, "ProfitAndLossAccts": {"ProfitAndLossAcct": [{"FiscalYear": "2022", "FiscalYearEndDate": "01 January", "InformationType": "Extract", "InformationLevel": "Full", "FinancialPeriodEndDate": "01-Jan-2022", "NumberOfMonths": "2", "Currency": "ANG", "Scale": "Units", "LineItems": {"LineItem": [{"Level1": "Profit After Tax", "Level2": "Total Net Profit", "Level3": "Net Profit", "Level4": "Subtotal", "SourceDescription": "Net Profit", "Amount": "67.00", "KeyFinancialId": "15", "LineItemTypeId": "2"}, {"Level1": "Income", "Level2": "Total Income", "Level3": "Total Income", "Level4": "Subtotal", "SourceDescription": "Total Income", "Amount": "67000.00", "KeyFinancialId": "9", "LineItemTypeId": "2"}, {"Level1": "Gross Profit", "Level2": "Gross Profit", "Level3": "Gross Profit", "Level4": "Subtotal", "SourceDescription": "Gross Profit", "Amount": "67.00", "KeyFinancialId": "11", "LineItemTypeId": "2"}, {"Level1": "Operating Profit", "Level2": "Total Operating Profit", "Level3": "Total Operating Profit", "Level4": "Subtotal", "SourceDescription": "Total Operating Profit", "Amount": "67.00", "KeyFinancialId": "13", "LineItemTypeId": "2"}, {"Level1": "Net P&L", "Level2": "Total Profit Before Tax", "Level3": "Profit Before Tax", "Level4": "Subtotal", "SourceDescription": "Profit Before Tax", "Amount": "67.00", "KeyFinancialId": "14", "LineItemTypeId": "2"}, {"Level1": "Adjusted P&L", "Level2": "Total Adjusted P&L", "Level3": "Total Adjusted Profit", "Level4": "Total", "SourceDescription": "Total Adjusted Profit", "Amount": "67.00", "KeyFinancialId": "16", "LineItemTypeId": "3"}, {"Level1": "Income", "Level2": "Income", "Level3": "Income", "Level4": "Line Item", "SourceDescription": "test", "Amount": "67.00", "CategoryId": "6", "LineItemTypeId": "1"}]}}, {"FiscalYear": "2023", "FiscalYearEndDate": "01 January", "InformationType": "Complete", "InformationLevel": "Full", "FinancialPeriodEndDate": "01-Jan-2023", "NumberOfMonths": "2", "Currency": "ANG", "Scale": "Units"}]}}, "LegalStatusSection": {"DateStarted": "08-Mar-2024", "CompanyStatus": "Active", "Capital": {"NotGiven": "false"}, "ICPLegalGroup": "Public Limited Company", "LegalStatusCode": "2000", "LegalStatus": "Public Limited Liability Company"}, "RelatedEntitiesSection": {"RelatedEntity": [{"Relationship": "Associate", "CompanyName": "Aish89", "CSRNumber": "999002", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "DateOccupiedFrom": "01-Jul-2024", "DateOccupiedTo": "31-Jul-2024", "CareOf": "1.1", "Building": "1.1", "BuildingCommercialName": "1.1", "Street": "1.1", "POBox": "1.1", "Town": "1.124", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania", "GPSLocation": "1.1"}, "Telephone": {"InternationalDialingCode": "355", "Number": "1.1", "Extension": "1.1"}}, "RelatedEntitiesTradingStyles": {"TradingStyle": {"Type": "Trading Style", "LocalLanguage": "1.1", "LocalName": "1.1", "EnglishName": "1.1"}}, "RelatedEntitiesLegalStatusSection": {"DateStarted": "2024", "CompanyStatus": "Cancelled", "History": "1.1", "RegistrationNumbers": {"RegistrationNumber": {"ICPRegistrationNumberName": "Tax Number", "LocalRegistrationNumberName": "Tax Numbe", "LocalAbbreviation": "TN", "IssuingAuthority": "AQ", "RegistrationNumberValue": "1.1", "ICPRegistrationNumberTypeId": "27"}}, "ICPLegalGroup": "Government", "LegalStatusCode": "2088", "LegalStatus": "Government Agency"}}, {"Relationship": "Subsidiary", "CompanyName": "check Existing process", "CSRNumber": "998702", "RelatedEntitiesLegalStatusSection": {"DateStarted": "2023", "CompanyStatus": "Active", "History": "test", "ICPLegalGroup": "Unlimited Partnership", "LegalStatusCode": "3000", "LegalStatus": "General Partnership"}}, {"Relationship": "Subsidiary", "CompanyName": "check2", "CSRNumber": "999077", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "Building": "checky", "Street": "check", "Town": "checek", "ProvinceType": "Province", "ISO2CountryCode": "AF", "ICPCountryCode": "AFG", "ICPCountryName": "Afghanistan"}}}, {"Relationship": "Subsidiary", "CompanyName": "test44", "CSRNumber": "999158", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "Building": "test", "Street": "test", "Town": "test44", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Associate", "CompanyName": "test78", "CSRNumber": "999081", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "Town": "etes", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Associate", "CompanyName": "test89", "CSRNumber": "999078", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "Building": "test", "Street": "test", "Town": "test", "ISO2CountryCode": "PG", "ICPCountryCode": "ADM", "ICPCountryName": "Admiralty Islands"}}}, {"Relationship": "Affiliate", "CompanyName": "test9", "CSRNumber": "999076", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "Building": "test", "Street": "test", "Town": "test", "ISO2CountryCode": "PG", "ICPCountryCode": "ADM", "ICPCountryName": "Admiralty Islands"}}}, {"Relationship": "Subsidiary", "CompanyName": "test900", "CSRNumber": "999083", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AF", "ICPCountryCode": "AFG", "ICPCountryName": "Afghanistan"}}}, {"Relationship": "Affiliate", "CompanyName": "TestCompanyB!", "CSRNumber": "998791", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "DateOccupiedFrom": "14-Apr-2024", "DateOccupiedTo": "24-Aug-2024", "Building": "AQ", "BuildingCommercialName": "Xr", "PostCode": "232342", "ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India", "OccupancyType": "Own Freehold"}, "Telephone": {"InternationalDialingCode": "91", "Number": "7656567876", "Type": "Mobile"}}, "RelatedEntitiesTradingStyles": {"TradingStyle": {"Type": "Formerly Known", "EnglishName": "Test"}}, "RelatedEntitiesLegalStatusSection": {"DateStarted": "15-Apr-2024", "CompanyStatus": "Active", "RegistrationNumbers": {"RegistrationNumber": {"ICPRegistrationNumberName": "Business Registration Number", "LocalRegistrationNumberName": "test", "RegistrationNumberValue": "234232", "DateIssued": "15-Apr-2024", "DateExpired": "24-Oct-2025", "RenewalFrequency": "Quarterly", "ICPRegistrationNumberTypeId": "4"}}, "ICPLegalGroup": "Public Limited Company", "LegalStatusCode": "2000", "LegalStatus": "Public Limited Liability Company"}}, {"Relationship": "Affiliate", "CompanyName": "TestCompanyRe444", "CSRNumber": "998805", "RelatedEntitiesTradingStyles": {"TradingStyle": {"Type": "Short Form", "EnglishName": "testtrad"}}, "RelatedEntitiesLegalStatusSection": {"DateStarted": "17-Feb-2024", "CompanyStatus": "Active", "RegistrationNumbers": {"RegistrationNumber": {"ICPRegistrationNumberName": "Other Registration Number2", "LocalRegistrationNumberName": "COCM", "LocalAbbreviation": "CO ab", "RegistrationNumberValue": "2324234", "RenewalFrequency": "Bi Annually", "ICPRegistrationNumberTypeId": "2"}}, "ICPLegalGroup": "Civil Company/Partnership - Non-Commercial", "LegalStatusCode": "3006", "LegalStatus": "Civil Partnership"}}, {"Relationship": "Affiliate", "CompanyName": "testCompanyrel22", "CSRNumber": "998802", "RelatedEntitiesAddressSection": {"Telephone": {"InternationalDialingCode": "91", "Number": "567656", "Type": "Mobile"}}, "RelatedEntitiesTradingStyles": {"TradingStyle": {"Type": "Trading Style", "EnglishName": "testcomrel22"}}, "RelatedEntitiesLegalStatusSection": {"DateStarted": "10-Jan-2024", "CompanyStatus": "Active", "RegistrationNumbers": {"RegistrationNumber": {"ICPRegistrationNumberName": "Other Registration Number3", "LocalRegistrationNumberName": "BN17Sep", "RegistrationNumberValue": "343565", "DateIssued": "07-Feb-2024", "DateExpired": "28-Apr-2024", "RenewalFrequency": "Quarterly", "ICPRegistrationNumberTypeId": "3"}}, "ICPLegalGroup": "Public Limited Company", "LegalStatusCode": "7600", "LegalStatus": "Bank"}}, {"Relationship": "Affiliate", "CompanyName": "testing", "CSRNumber": "999072", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "Building": "test", "Street": "test", "Town": "test", "ISO2CountryCode": "PG", "ICPCountryCode": "ADM", "ICPCountryName": "Admiralty Islands"}}}, {"Relationship": "Affiliate", "CompanyName": "yest", "CSRNumber": "999088", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}]}, "SpecialNotesSection": {"TransferToPrincipal": "false"}, "WaiverSection": {"Waiver": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP."}}}, "text_content": [{"path": "Report/HeaderSection/Date", "tag": "Date", "text": "29-Aug-2024", "attributes": {}}, {"path": "Report/HeaderSection/DeliveryDate", "tag": "DeliveryDate", "text": "30-Aug-2024", "attributes": {}}, {"path": "Report/HeaderSection/OurRef", "tag": "OurRef", "text": "998772.22.1", "attributes": {}}, {"path": "Report/HeaderSection/YourRef", "tag": "YourRef", "text": "Test22.1", "attributes": {}}, {"path": "Report/HeaderSection/CompanyName", "tag": "CompanyName", "text": "AishwaryaTest", "attributes": {}}, {"path": "Report/PaymentsSection/PaymentHistory", "tag": "PaymentHistory", "text": "PaymentsNoTrace", "attributes": {}}, {"path": "Report/PaymentsSection/CreditOpinion", "tag": "CreditOpinion", "text": "Large", "attributes": {}}, {"path": "Report/PaymentsSection/MaxCreditCurrency", "tag": "MaxCreditCurrency", "text": "AMD", "attributes": {}}, {"path": "Report/PaymentsSection/MaxCredit", "tag": "MaxCredit", "text": "2321", "attributes": {}}, {"path": "Report/PaymentsSection/OpinionOnMaxCredit", "tag": "OpinionOnMaxCredit", "text": "TermsStipulated", "attributes": {}}, {"path": "Report/PaymentsSection/AdditionalNoteMaxCredit", "tag": "AdditionalNoteMaxCredit", "text": "test", "attributes": {}}, {"path": "Report/PaymentsSection/TradeRiskAssessment", "tag": "TradeRiskAssessment", "text": "Average", "attributes": {}}, {"path": "Report/PaymentsSection/CreditFigureCurrency", "tag": "CreditFigureCurrency", "text": "AMD", "attributes": {}}, {"path": "Report/PaymentsSection/CreditFigure", "tag": "CreditFigure", "text": "2122", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheetFigures/AccountType", "tag": "AccountType", "text": "Consolidated (Subject & Subsidiaries)", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheetFigures/CompanyName", "tag": "CompanyName", "text": "AishwaryaTest", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheetFigures/ScaleTheme", "tag": "ScaleTheme", "text": "Units", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/FiscalYear", "tag": "FiscalYear", "text": "2024", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/FiscalYearEndDate", "tag": "FiscalYearEndDate", "text": "01 January", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/InformationType", "tag": "InformationType", "text": "Complete", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/InformationLevel", "tag": "InformationLevel", "text": "Full", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/FinancialPeriodEndDate", "tag": "FinancialPeriodEndDate", "text": "01-Jan-2024", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/NumberOfMonths", "tag": "NumberOfMonths", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/Currency", "tag": "<PERSON><PERSON><PERSON><PERSON>", "text": "AUD", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/Scale", "tag": "Scale", "text": "Units", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Non-Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Non-Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Non-Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "8268525.22", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "79987.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Total", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "44000.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "6", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "3", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Total Assets less Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Assets less Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Assets less Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Total", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Assets less Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "8304512.22", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "7", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "3", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "4000.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "4", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Total", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "8348512.22", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "3", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "3", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Non-Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Non-Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Non-Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "40000.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "5", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Non Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Fixed Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "testt", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "5000.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Non Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Deferred Tax Asset", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "-675.55", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Non Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Goodwill", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "12", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "-98.23", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Non Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Long Term Debtors", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test3", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "454343.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Non Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Other Non-Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "tes", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "7809956.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Short Term Non-Financial Investments", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "40000.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Cash & Bank - Asset", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "13", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "-234.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Inventories", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "123", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "123.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Other Current Assets", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "12", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "40098.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Other Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "4000.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "3", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Non-Current Liabilities", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Deferred Tax Liability", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "40000.00", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "4", "attributes": {}}, {"path": "Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossFigures/AccountType", "tag": "AccountType", "text": "Subject", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossFigures/CompanyName", "tag": "CompanyName", "text": "AishwaryaTest", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossFigures/ScaleTheme", "tag": "ScaleTheme", "text": "Units", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FiscalYear", "tag": "FiscalYear", "text": "2022", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FiscalYearEndDate", "tag": "FiscalYearEndDate", "text": "01 January", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/InformationType", "tag": "InformationType", "text": "Extract", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/InformationLevel", "tag": "InformationLevel", "text": "Full", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FinancialPeriodEndDate", "tag": "FinancialPeriodEndDate", "text": "01-Jan-2022", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/NumberOfMonths", "tag": "NumberOfMonths", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/Currency", "tag": "<PERSON><PERSON><PERSON><PERSON>", "text": "ANG", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/Scale", "tag": "Scale", "text": "Units", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Profit After Tax", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Net Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Net Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Net Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "67.00", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "15", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Income", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Income", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Income", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Income", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "67000.00", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "9", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Gross Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Gross Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Gross Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Gross Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "67.00", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "11", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Operating Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Operating Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Operating Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Operating Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "67.00", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "13", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Net P&L", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Profit Before Tax", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Profit Before Tax", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Profit Before Tax", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "67.00", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "14", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Adjusted P&L", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Adjusted P&L", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Adjusted Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Total", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Adjusted Profit", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "67.00", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "16", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "3", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Income", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Income", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Income", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "67.00", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "6", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FiscalYear", "tag": "FiscalYear", "text": "2023", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FiscalYearEndDate", "tag": "FiscalYearEndDate", "text": "01 January", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/InformationType", "tag": "InformationType", "text": "Complete", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/InformationLevel", "tag": "InformationLevel", "text": "Full", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FinancialPeriodEndDate", "tag": "FinancialPeriodEndDate", "text": "01-Jan-2023", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/NumberOfMonths", "tag": "NumberOfMonths", "text": "2", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/Currency", "tag": "<PERSON><PERSON><PERSON><PERSON>", "text": "ANG", "attributes": {}}, {"path": "Report/FinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/Scale", "tag": "Scale", "text": "Units", "attributes": {}}, {"path": "Report/LegalStatusSection/DateStarted", "tag": "DateStarted", "text": "08-Mar-2024", "attributes": {}}, {"path": "Report/LegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/LegalStatusSection/Capital/NotGiven", "tag": "NotGiven", "text": "false", "attributes": {}}, {"path": "Report/LegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Public Limited Company", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "2000", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Public Limited Liability Company", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "Aish89", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999002", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedFrom", "tag": "DateOccupiedFrom", "text": "01-Jul-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedTo", "tag": "DateOccupiedTo", "text": "31-Jul-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/CareOf", "tag": "CareOf", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/BuildingCommercialName", "tag": "BuildingCommercialName", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/POBox", "tag": "POBox", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "1.124", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/GPSLocation", "tag": "GPSLocation", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "355", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Number", "tag": "Number", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Extension", "tag": "Extension", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Trading Style", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/LocalLanguage", "tag": "LocalLanguage", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/LocalName", "tag": "LocalName", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/DateStarted", "tag": "DateStarted", "text": "2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Cancelled", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/History", "tag": "History", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Tax Number", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "Tax Numbe", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalAbbreviation", "tag": "LocalAbbreviation", "text": "TN", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/IssuingAuthority", "tag": "IssuingAuthority", "text": "AQ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "1.1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "27", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Government", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "2088", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Government Agency", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "check Existing process", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "998702", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/DateStarted", "tag": "DateStarted", "text": "2023", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/History", "tag": "History", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Unlimited Partnership", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "3000", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "General Partnership", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "check2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999077", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "checky", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "check", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "checek", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AF", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "AFG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Afghanistan", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test44", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999158", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "test44", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test78", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999081", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "etes", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test89", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999078", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "PG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ADM", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Admiralty Islands", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test9", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999076", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "PG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ADM", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Admiralty Islands", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test900", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999083", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AF", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "AFG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Afghanistan", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "TestCompanyB!", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "998791", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedFrom", "tag": "DateOccupiedFrom", "text": "14-Apr-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedTo", "tag": "DateOccupiedTo", "text": "24-Aug-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "AQ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/BuildingCommercialName", "tag": "BuildingCommercialName", "text": "Xr", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/PostCode", "tag": "PostCode", "text": "232342", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/OccupancyType", "tag": "OccupancyType", "text": "Own Freehold", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "91", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Number", "tag": "Number", "text": "7656567876", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Type", "tag": "Type", "text": "Mobile", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Formerly Known", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "Test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/DateStarted", "tag": "DateStarted", "text": "15-Apr-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Business Registration Number", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "234232", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateIssued", "tag": "DateIssued", "text": "15-Apr-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateExpired", "tag": "DateExpired", "text": "24-Oct-2025", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RenewalFrequency", "tag": "RenewalFrequency", "text": "Quarterly", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "4", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Public Limited Company", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "2000", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Public Limited Liability Company", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "TestCompanyRe444", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "998805", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Short Form", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "testtrad", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/DateStarted", "tag": "DateStarted", "text": "17-Feb-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Other Registration Number2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "COCM", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalAbbreviation", "tag": "LocalAbbreviation", "text": "CO ab", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "2324234", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RenewalFrequency", "tag": "RenewalFrequency", "text": "Bi Annually", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Civil Company/Partnership - Non-Commercial", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "3006", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Civil Partnership", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testCompanyrel22", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "998802", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "91", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Number", "tag": "Number", "text": "567656", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Type", "tag": "Type", "text": "Mobile", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Trading Style", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "testcomrel22", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/DateStarted", "tag": "DateStarted", "text": "10-Jan-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Other Registration Number3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "BN17Sep", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "343565", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateIssued", "tag": "DateIssued", "text": "07-Feb-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateExpired", "tag": "DateExpired", "text": "28-Apr-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RenewalFrequency", "tag": "RenewalFrequency", "text": "Quarterly", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Public Limited Company", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "7600", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Bank", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999072", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "PG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ADM", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Admiralty Islands", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "yest", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999088", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/SpecialNotesSection/TransferToPrincipal", "tag": "TransferToPrincipal", "text": "false", "attributes": {}}, {"path": "Report/WaiverSection/Waiver", "tag": "Waiver", "text": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP.", "attributes": {}}], "root_element": "Report", "namespace": {}, "processing_timestamp": "2025-07-15T20:07:52.001886"}