version: '3.8'

services:
  xml-validation-api:
    build: .
    container_name: xml-validation-system
    ports:
      - "8000:8000"
    environment:
      # OpenAI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4o-mini}
      
      # Application Configuration
      - APP_NAME=XML Report Validation System
      - APP_VERSION=1.0.0
      - DEBUG=${DEBUG:-False}
      - HOST=0.0.0.0
      - PORT=8000
      
      # Local Data Paths
      - UPLOAD_PATH=/app/data/uploads
      - PROCESSED_PATH=/app/data/processed
      
      # Vector Database Configuration
      - CHROMADB_MODE=${CHROMADB_MODE:-server}
      - CHROMADB_HOST=${CHROMADB_HOST:-chromadb}
      - CHROMADB_PORT=${CHROMADB_PORT:-8000}
      - CHROMADB_PATH=/app/data/vector_db
      - VECTOR_DB_PATH=/app/data/vector_db
      
      # LangChain Configuration
      - MAX_TOKENS=${MAX_TOKENS:-4000}
      - CHUNK_SIZE=${CHUNK_SIZE:-1000}
      - CHUNK_OVERLAP=${CHUNK_OVERLAP:-200}
      
      # Performance Configuration
      - DEFAULT_BATCH_SIZE=${DEFAULT_BATCH_SIZE:-10}
      - USE_BULK_PROCESSING=${USE_BULK_PROCESSING:-True}
      - ENABLE_PERFORMANCE_OPTIMIZATIONS=${ENABLE_PERFORMANCE_OPTIMIZATIONS:-True}
      
      # File Configuration
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-10485760}
      - ALLOWED_EXCEL_EXTENSIONS=["xlsx", "xls"]
      - ALLOWED_XML_EXTENSIONS=["xml"]
    volumes:
      # Persist data across container restarts
      - xml_validation_data:/app/data
      # Mount environment file if needed
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - xml-validation-network

  # ChromaDB server (running in server mode)
  chromadb:
    image: chromadb/chroma:latest
    container_name: chromadb-server
    ports:
      - "8001:8000"
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - CHROMA_SERVER_CORS_ALLOW_ORIGINS=["*"]
      - CHROMA_SERVER_AUTH_CREDENTIALS_FILE=""
      - CHROMA_SERVER_AUTH_CREDENTIALS_PROVIDER=""
    volumes:
      - chromadb_data:/chroma/chroma
    restart: unless-stopped
    networks:
      - xml-validation-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  xml_validation_data:
    driver: local
  chromadb_data:
    driver: local

networks:
  xml-validation-network:
    driver: bridge 