# Token Counting Implementation with tiktoken

## Overview

This implementation adds comprehensive token counting functionality to the validation service using tiktoken. The system now measures token usage for all validation API requests and provides detailed analysis for cost estimation and optimization.

## Features Implemented

### 1. Token Counter Utility (`app/utils/token_counter.py`)

**Core Functionality:**
- **Model-aware encoding**: Automatically selects the correct tiktoken encoding based on the configured OpenAI model
- **Token counting**: Accurate token counting for text strings and message arrays
- **Cost estimation**: Real-time cost calculation based on current OpenAI pricing
- **Limit checking**: Validates token usage against model limits
- **Detailed analysis**: Breaks down token usage by component (XML, questions, RAG data, etc.)

**Key Methods:**
- `count_tokens(text)`: Count tokens in a text string
- `analyze_validation_request_tokens()`: Comprehensive analysis of validation request tokens
- `analyze_validation_response_tokens()`: Analysis of LLM response tokens
- `check_token_limit()`: Validate against model token limits
- `get_model_token_limit()`: Get current model's token limit

### 2. Integration with Validation Service

**Enhanced ValidationService:**
- Token counter initialized automatically
- Real-time token analysis during holistic validation
- Token usage logged to console during validation
- Token analysis included in validation response
- Cost estimation before LLM calls

**Token Analysis in Responses:**
- Input token breakdown by component
- Output token counting
- Cost estimation
- Usage percentage of model limits
- Performance recommendations

### 3. API Endpoints

**New Token Analysis Endpoints:**

#### `/api/v1/validate/token-analysis` (POST)
Analyze token usage without running validation:
```json
{
  "questions": ["Question 1?", "Question 2?"],
  "xml_content": "<report>...</report>",
  "rag_data": {...},
  "focus_prompt": "Focus on..."
}
```

**Response includes:**
- Detailed token breakdown
- Cost estimation
- Usage percentage
- Optimization recommendations

#### `/api/v1/validate/token-info` (GET)
Get model information and pricing:
```json
{
  "model": "gpt-4o-mini",
  "token_limit": 128000,
  "encoding": "cl100k_base",
  "sample_cost": {...},
  "cost_per_1k_tokens": {...}
}
```

### 4. Enhanced Validation Response Schema

**Updated ValidationResponse:**
- Added `token_analysis` field with comprehensive token usage data
- Includes input/output token counts
- Cost estimation
- Usage statistics

## Token Analysis Breakdown

### Input Tokens
- **XML Content**: Tokens from the report XML
- **Questions**: Tokens from validation questions
- **RAG Data**: Tokens from retrieved examples
- **Focus Prompt**: Tokens from user-provided focus
- **System Prompt**: Tokens from system instructions
- **Instruction Prompt**: Tokens from validation instructions

### Output Tokens
- **Raw LLM Response**: Tokens in the complete LLM response
- **Processed Results**: Tokens in structured validation results
- **Average per Result**: Token efficiency metrics

### Cost Calculation
- Model-specific pricing (updated for 2024 rates)
- Separate input/output token pricing
- Real-time cost estimation
- Cost comparison across models

## Usage Examples

### 1. Basic Token Analysis
```python
from app.utils.token_counter import TokenCounter

counter = TokenCounter()
tokens = counter.count_tokens("Sample text")
print(f"Tokens: {tokens}")
```

### 2. Validation Request Analysis
```python
analysis = counter.analyze_validation_request_tokens(
    questions=[{"question": "Is data valid?"}],
    xml_content="<report>...</report>",
    rag_data={},
    focus_prompt="Focus on accuracy"
)
print(f"Total tokens: {analysis['total_input_tokens']}")
print(f"Estimated cost: ${analysis['estimated_cost']}")
```

### 3. API Usage
```bash
# Get token info
curl -X GET "http://localhost:8000/api/v1/validate/token-info"

# Analyze tokens
curl -X POST "http://localhost:8000/api/v1/validate/token-analysis" \
  -H "Content-Type: application/json" \
  -d '{
    "questions": ["Is the data valid?"],
    "xml_content": "<report><data>test</data></report>"
  }'
```

## Benefits

### 1. Cost Control
- **Pre-validation cost estimation**: Know costs before running validation
- **Model comparison**: Compare costs across different OpenAI models
- **Budget monitoring**: Track token usage and costs over time

### 2. Performance Optimization
- **Token limit validation**: Prevent requests that exceed model limits
- **Content optimization**: Identify large content that can be reduced
- **Batch optimization**: Recommendations for efficient batching

### 3. Monitoring & Analytics
- **Usage tracking**: Monitor token consumption patterns
- **Efficiency metrics**: Measure tokens per validation result
- **Trend analysis**: Track usage over time

### 4. Development & Debugging
- **Detailed breakdowns**: Understand where tokens are being used
- **Optimization insights**: Identify areas for improvement
- **Testing support**: Validate token usage in tests

## Configuration

### Model Support
- **gpt-4o**: 128K context, premium pricing
- **gpt-4o-mini**: 128K context, cost-effective
- **gpt-4**: 8K context, high-quality
- **gpt-3.5-turbo**: 4K context, fastest/cheapest

### Pricing (2024 rates)
- **gpt-4o**: $0.005/1K input, $0.015/1K output
- **gpt-4o-mini**: $0.00015/1K input, $0.0006/1K output
- **gpt-4**: $0.03/1K input, $0.06/1K output
- **gpt-3.5-turbo**: $0.001/1K input, $0.002/1K output

## Testing

### Test Scripts
- `test_token_counting.py`: Comprehensive demonstration
- `test_token_api.py`: API endpoint testing

### Test Coverage
- Token counting accuracy
- Cost calculation validation
- API endpoint functionality
- Integration with validation service
- Error handling and edge cases

## Monitoring Output

During validation, you'll see output like:
```
Token Analysis - Input:
  Total input tokens: 2,370
  XML content tokens: 249
  Questions tokens: 44
  RAG data tokens: 56
  Estimated cost: $0.023100

Token Analysis - Output:
  Response tokens: 173
  Average tokens per result: 49.5
```

## Future Enhancements

1. **Historical Analytics**: Track token usage trends over time
2. **Budget Alerts**: Notifications when usage exceeds thresholds
3. **Optimization Suggestions**: AI-powered content optimization
4. **Batch Processing**: Intelligent batching based on token limits
5. **Custom Pricing**: Support for enterprise pricing models
