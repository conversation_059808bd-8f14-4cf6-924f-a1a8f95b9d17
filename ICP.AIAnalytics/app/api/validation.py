from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.responses import Response
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import asyncio
import time
import base64
import httpx
import xml.etree.ElementTree as ET
from datetime import datetime
import uuid

from app.core.config import settings
from app.models.schemas import ValidationRequest, ValidationResponse, ValidationStatus, PermanentQuestionBankResponse
from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor
from app.utils.xml_generator import XMLGenerator
from app.utils.token_counter import TokenCounter


router = APIRouter()
validation_service = ValidationService()
xml_generator = XMLGenerator()
file_processor = FileProcessor()
token_counter = TokenCounter()

# Store validation tasks for background processing
validation_tasks: Dict[str, Dict[str, Any]] = {}


@router.post("/validate")
async def validate_report(
    request: ValidationRequest,
    background_tasks: BackgroundTasks,
    response_format: str = "json",
    async_processing: bool = False
):
    """
    Validate XML report against permanent question bank.
    
    Two modes of operation:
    1. If bearer_token is provided: Upload report from external API first, then validate
    2. If bearer_token is not provided: Validate existing report (traditional mode)
    """
    try:
        start_time = time.time()
        upload_result = None
        
        # Check if we need to upload the report first
        if request.bearer_token:
            print(f"Upload mode: Fetching and uploading report {request.report_id} from external API...")
            upload_result = await _fetch_and_upload_report(request.report_id, request.bearer_token)
            print(f"Upload completed. File ID: {upload_result['file_id']}, Report ID: {upload_result['report_id']}")
        else:
            print(f"Validation mode: Validating existing report {request.report_id}")
        
        if async_processing:
            # Start background validation
            validation_id = await start_background_validation(request, background_tasks)
            
            response_data = {
                "validation_id": validation_id,
                "status": ValidationStatus.PROCESSING,
                "message": "Validation started in background" + (" (report uploaded)" if upload_result else ""),
                "check_status_url": f"/api/v1/validate/status/{validation_id}",
                "using_permanent_question_bank": True
            }
            
            # Add upload info if applicable
            if upload_result:
                response_data["upload_info"] = upload_result
            
            if response_format.lower() == "xml":
                xml_response = xml_generator.generate_validation_xml(response_data)
                return Response(content=xml_response, media_type="application/xml")
            else:
                return response_data
        else:
            # Synchronous validation
            # Prepare order_details_params with bearer_token if needed
            order_details_params = None
            if request.order_details_params:
                order_details_params = request.order_details_params.model_dump()
                # Use main bearer_token if order_details_params doesn't have one
                if not order_details_params.get("bearer_token") and request.bearer_token:
                    order_details_params["bearer_token"] = request.bearer_token
            
            validation_result = await validation_service.validate_report(
                request.report_id,
                request.validation_options,
                request.enable_client_filtering,
                order_details_params,
                request.direct_client_code,
                request.bearer_token
            )
            
            # Add upload info if applicable
            if upload_result:
                validation_result["upload_info"] = upload_result
                validation_result["processing_time"] = time.time() - start_time
            
            if response_format.lower() == "xml":
                xml_response = xml_generator.generate_validation_xml(validation_result)
                return Response(content=xml_response, media_type="application/xml")
            else:
                return validation_result
                
    except Exception as e:
        print(f"Error in validate_report: {str(e)}")
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "VALIDATION_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


async def _fetch_and_upload_report(report_id: str, bearer_token: str) -> Dict[str, Any]:
    """
    Internal function to fetch and upload report from external API.
    Returns upload result data.
    """
    # API configuration - Same as in upload.py
    api_url = f"{settings.EXTERNAL_API_BASE_URL}/api/report/getOrderXml?reportId={report_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    
    # Fetch data from external API
    async with httpx.AsyncClient(timeout=30.0) as client:
        api_response = await client.get(api_url, headers=headers)
        
        if api_response.status_code != 200:
            raise HTTPException(
                status_code=api_response.status_code, 
                detail=f"External API error: {api_response.text}"
            )
        
        # Parse the API response (same logic as in upload.py)
        try:
            api_data = api_response.json()
        except Exception as e:
            response_text = api_response.text.strip()
            if len(response_text) > 100:
                base64_xml = response_text
            else:
                raise HTTPException(
                    status_code=422, 
                    detail=f"Invalid API response format. Expected JSON or base64 string. Error: {str(e)}"
                )
        else:
            # Extract base64 XML content from JSON response
            base64_xml = None
            possible_keys = ['data', 'xml', 'content', 'report', 'xmlData', 'xmlContent', 'value', 'result']
            
            if isinstance(api_data, dict):
                for key in possible_keys:
                    if key in api_data:
                        base64_xml = api_data[key]
                        break
                
                if not base64_xml:
                    for key, value in api_data.items():
                        if isinstance(value, str) and len(value) > 100:
                            base64_xml = value
                            break
            elif isinstance(api_data, str):
                base64_xml = api_data
            
            if not base64_xml:
                raise HTTPException(
                    status_code=422, 
                    detail=f"Could not find base64 XML content in API response."
                )
    
    # Decode base64 to XML
    try:
        xml_content = base64.b64decode(base64_xml).decode('utf-8')
    except Exception as e:
        raise HTTPException(
            status_code=422, 
            detail=f"Failed to decode base64 content: {str(e)}"
        )
    
    # Validate that it's XML
    try:
        ET.fromstring(xml_content)
    except ET.ParseError as e:
        raise HTTPException(
            status_code=422, 
            detail=f"Invalid XML content: {str(e)}"
        )
    
    # Create filename based on report ID
    filename = f"report_{report_id}_xml.xml"
    
    # Save the XML content
    file_content = xml_content.encode('utf-8')
    additional_metadata = {
        "source": "external_api",
        "report_id": report_id
    }
    
    file_id = await file_processor.save_uploaded_file(file_content, filename, additional_metadata)
    
    # Process the XML report
    await file_processor.process_xml_report(file_id)
    
    # Return upload result data
    return {
        "file_id": file_id,
        "filename": filename,
        "file_type": ".xml",
        "file_size": len(file_content),
        "upload_timestamp": datetime.now(),
        "status": "uploaded",
        "source": "external_api",
        "report_id": report_id
    }


async def start_background_validation(request: ValidationRequest, background_tasks: BackgroundTasks) -> str:
    """Start background validation task."""
    import uuid
    validation_id = str(uuid.uuid4())
    
    # Store task info
    validation_tasks[validation_id] = {
        "status": ValidationStatus.PROCESSING,
        "started_at": datetime.now(),
        "request": request.model_dump(),
        "result": None,
        "error": None,
        "using_permanent_question_bank": True
    }
    
    # Add background task
    background_tasks.add_task(
        run_background_validation,
        validation_id,
        request.report_id,
        request.validation_options,
        request.enable_client_filtering,
        request.order_details_params,  # Pass the full object instead of dumped dict
        request.direct_client_code,
        request.bearer_token  # Pass bearer_token for upload functionality
    )
    
    return validation_id


async def run_background_validation(
    validation_id: str,
    report_id: str,
    validation_options: Dict[str, Any],
    enable_client_filtering: bool = False,
    order_details_params = None,  # Can be OrderDetailsRequest object or None
    direct_client_code: str = None,
    bearer_token: str = None
):
    """Run validation in background."""
    try:
        # Check if we need to upload the report first
        if bearer_token:
            print(f"Background upload: Fetching and uploading report {report_id} from external API...")
            upload_result = await _fetch_and_upload_report(report_id, bearer_token)
            print(f"Background upload completed. File ID: {upload_result['file_id']}")
        
        # Prepare order_details_params with bearer_token if needed
        order_details_dict = None
        if order_details_params:
            if hasattr(order_details_params, 'model_dump'):
                order_details_dict = order_details_params.model_dump()
            else:
                order_details_dict = order_details_params
            
            # Use main bearer_token if order_details_params doesn't have one
            if not order_details_dict.get("bearer_token") and bearer_token:
                order_details_dict["bearer_token"] = bearer_token
        
        result = await validation_service.validate_report(
            report_id,
            validation_options,
            enable_client_filtering,
            order_details_dict,
            direct_client_code,
            bearer_token
        )
        
        validation_tasks[validation_id].update({
            "status": ValidationStatus.COMPLETED,
            "result": result,
            "completed_at": datetime.now()
        })
        
    except Exception as e:
        validation_tasks[validation_id].update({
            "status": ValidationStatus.FAILED,
            "error": str(e),
            "completed_at": datetime.now()
        })


@router.get("/validate/status/{validation_id}")
async def get_validation_status(validation_id: str, response_format: str = "json"):
    """Get status of background validation."""
    try:
        if validation_id not in validation_tasks:
            raise HTTPException(status_code=404, detail="Validation not found")
        
        task_info = validation_tasks[validation_id]
        
        response_data = {
            "validation_id": validation_id,
            "status": task_info["status"],
            "started_at": task_info["started_at"],
            "completed_at": task_info.get("completed_at"),
            "result": task_info.get("result"),
            "error": task_info.get("error")
        }
        
        if response_format.lower() == "xml":
            if task_info["status"] == ValidationStatus.COMPLETED and task_info.get("result"):
                xml_response = xml_generator.generate_validation_xml(task_info["result"])
            else:
                xml_response = xml_generator.generate_validation_xml(response_data)
            return Response(content=xml_response, media_type="application/xml")
        else:
            return response_data
            
    except HTTPException:
        raise
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "STATUS_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


@router.get("/validate/result/{validation_id}")
async def get_validation_result(validation_id: str, response_format: str = "json"):
    """Get validation result by ID."""
    try:
        # First check in-memory tasks
        if validation_id in validation_tasks:
            task_info = validation_tasks[validation_id]
            if task_info["status"] == ValidationStatus.COMPLETED and task_info.get("result"):
                result = task_info["result"]
            else:
                raise HTTPException(status_code=404, detail="Validation not completed or failed")
        else:
            # Check saved results
            result = await validation_service.get_validation_results(validation_id)
            if not result:
                raise HTTPException(status_code=404, detail="Validation result not found")
        
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_validation_xml(result)
            return Response(content=xml_response, media_type="application/xml")
        else:
            return result
            
    except HTTPException:
        raise
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "RESULT_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


@router.get("/validate/summary/{validation_id}")
async def get_validation_summary(validation_id: str, response_format: str = "json"):
    """Get validation summary by ID."""
    try:
        result = await validation_service.get_validation_results(validation_id)
        if not result:
            raise HTTPException(status_code=404, detail="Validation result not found")
        
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_summary_xml(result)
            return Response(content=xml_response, media_type="application/xml")
        else:
            # Create JSON summary
            results = result.get("results", [])
            confidence_scores = [r.get("confidence_score", 0) for r in results]
            
            summary = {
                "validation_id": validation_id,
                "total_questions": len(results),
                "answered_questions": len([r for r in results if r.get("status") == "answered"]),
                "error_questions": len([r for r in results if r.get("status") == "error"]),
                "average_confidence": sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
                "min_confidence": min(confidence_scores) if confidence_scores else 0,
                "max_confidence": max(confidence_scores) if confidence_scores else 0,
                "high_confidence_count": len([s for s in confidence_scores if s >= 0.8]),
                "processing_time": result.get("processing_time", 0),
                "validation_timestamp": result.get("validation_timestamp")
            }
            
            return summary
            
    except HTTPException:
        raise
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "SUMMARY_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


@router.get("/validate/history")
async def get_validation_history(
    page: int = 1,
    page_size: int = 10,
    status: Optional[str] = None
):
    """Get validation history with pagination."""
    try:
        # Get validation results from file system
        from pathlib import Path
        processed_path = Path(settings.PROCESSED_PATH)
        
        validation_files = list(processed_path.glob("*_validation.json"))
        
        # Filter by status if provided
        if status:
            filtered_files = []
            for file_path in validation_files:
                try:
                    import json
                    with open(file_path, "r") as f:
                        data = json.load(f)
                        if data.get("status") == status:
                            filtered_files.append(file_path)
                except:
                    continue
            validation_files = filtered_files
        
        # Pagination
        total_count = len(validation_files)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        page_files = validation_files[start_idx:end_idx]
        
        # Load validation data
        validations = []
        for file_path in page_files:
            try:
                import json
                with open(file_path, "r") as f:
                    validation_data = json.load(f)
                    validations.append(validation_data)
            except:
                continue
        
        return {
            "validations": validations,
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/validate/{validation_id}")
async def delete_validation(validation_id: str):
    """Delete validation result."""
    try:
        # Remove from in-memory tasks
        if validation_id in validation_tasks:
            del validation_tasks[validation_id]
        
        # Remove saved result file
        from pathlib import Path
        processed_path = Path(settings.PROCESSED_PATH)
        result_file = processed_path / f"{validation_id}_validation.json"
        
        if result_file.exists():
            result_file.unlink()
            return {"message": "Validation deleted successfully", "validation_id": validation_id}
        else:
            raise HTTPException(status_code=404, detail="Validation not found")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate/batch")
async def batch_validate(
    validation_requests: list[ValidationRequest],
    background_tasks: BackgroundTasks,
    response_format: str = "json"
):
    """Validate multiple reports in batch."""
    try:
        batch_id = str(uuid.uuid4())
        validation_ids = []
        
        for request in validation_requests:
            validation_id = await start_background_validation(request, background_tasks)
            validation_ids.append(validation_id)
        
        response_data = {
            "message": f"Batch validation started for {len(validation_requests)} reports",
            "validation_ids": validation_ids,
            "check_status_urls": [f"/api/v1/validate/status/{vid}" for vid in validation_ids]
        }
        
        if response_format.lower() == "xml":
            # Create simple XML response for batch
            xml_response = f"""
<BatchValidationResponse>
    <BatchId>{batch_id}</BatchId>
    <TotalValidations>{len(validation_ids)}</TotalValidations>
    <Status>processing</Status>
    <ValidationIds>
        {"".join([f"<ValidationId>{vid}</ValidationId>" for vid in validation_ids])}
    </ValidationIds>
</BatchValidationResponse>
"""
            return Response(content=xml_response.strip(), media_type="application/xml")
        else:
            return response_data
            
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "BATCH_VALIDATION_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


# Permanent Question Bank Management Endpoints
@router.post("/validate/permanent-questions/upload")
async def upload_permanent_question_bank(
    file: UploadFile = File(...),
    response_format: str = "json"
):
    """Upload and set a new permanent question bank."""
    try:
        from app.api.upload import validate_file_upload
        from app.services.file_processor import FileProcessor
        from app.models.schemas import PermanentQuestionBankResponse
        
        # Validate file
        validate_file_upload(file, settings.ALLOWED_EXCEL_EXTENSIONS, settings.MAX_FILE_SIZE)
        
        # Read file content
        file_content = await file.read()
        
        # Update permanent question bank
        file_processor = FileProcessor()
        result = await file_processor.update_permanent_question_bank(file_content, file.filename)
        
        response_data = PermanentQuestionBankResponse(
            success=result["success"],
            message=result["message"],
            file_path=result["file_path"],
            total_questions=result["total_questions"],
            timestamp=datetime.now(),
            permanent_question_bank_enabled=settings.USE_PERMANENT_QUESTION_BANK
        )
        
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_file_upload_xml(response_data.model_dump())
            return Response(content=xml_response, media_type="application/xml")
        else:
            return response_data
            
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "PERMANENT_QUESTIONS_UPLOAD_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=f"Error uploading permanent question bank: {str(e)}")


@router.get("/validate/permanent-questions/status")
async def get_permanent_question_bank_status(response_format: str = "json"):
    """Get status of the permanent question bank."""
    try:
        from app.models.schemas import PermanentQuestionBankStatus
        from pathlib import Path
        import os
        
        permanent_file_path = Path(settings.PERMANENT_QUESTION_BANK_PATH)
        file_exists = permanent_file_path.exists()
        
        # Try to get question count if file exists
        total_questions = None
        last_modified = None
        if file_exists:
            try:
                # Get file modification time
                last_modified = datetime.fromtimestamp(os.path.getmtime(permanent_file_path))
                
                # Load questions to get count
                from app.services.file_processor import FileProcessor
                file_processor = FileProcessor()
                questions = await file_processor.load_permanent_questions(force_reload=True)
                total_questions = len(questions)
            except Exception as e:
                print(f"Error loading permanent questions for status: {e}")
        
        response_data = PermanentQuestionBankStatus(
            enabled=settings.USE_PERMANENT_QUESTION_BANK,
            file_path=str(permanent_file_path),
            file_exists=file_exists,
            total_questions=total_questions,
            last_loaded=None,  # Could be enhanced to track this
            last_modified=last_modified
        )
        
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_file_upload_xml(response_data.model_dump())
            return Response(content=xml_response, media_type="application/xml")
        else:
            return response_data
            
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "PERMANENT_QUESTIONS_STATUS_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=f"Error getting permanent question bank status: {str(e)}")


@router.post("/validate/permanent-questions/reload")
async def reload_permanent_question_bank(response_format: str = "json"):
    """Reload the permanent question bank (useful if file was updated externally)."""
    try:
        from app.services.file_processor import FileProcessor
        from app.models.schemas import PermanentQuestionBankResponse
        
        if not settings.USE_PERMANENT_QUESTION_BANK:
            raise HTTPException(status_code=400, detail="Permanent question bank is not enabled")
        
        # Test loading the permanent questions
        file_processor = FileProcessor()
        questions = await file_processor.load_permanent_questions(force_reload=True)
        
        response_data = PermanentQuestionBankResponse(
            success=True,
            message=f"Permanent question bank reloaded successfully",
            file_path=settings.PERMANENT_QUESTION_BANK_PATH,
            total_questions=len(questions),
            timestamp=datetime.now(),
            permanent_question_bank_enabled=True
        )
        
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_file_upload_xml(response_data.model_dump())
            return Response(content=xml_response, media_type="application/xml")
        else:
            return response_data
            
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "PERMANENT_QUESTIONS_RELOAD_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=f"Error reloading permanent question bank: {str(e)}")


class TokenAnalysisRequest(BaseModel):
    questions: List[str]
    xml_content: str
    rag_data: Optional[Dict[str, Any]] = None
    focus_prompt: Optional[str] = None

@router.post("/validate/token-analysis")
async def analyze_tokens(request: TokenAnalysisRequest):
    """
    Analyze token usage for a validation request without actually running validation.
    Useful for cost estimation and optimization.
    """
    try:
        # Convert questions to the expected format
        questions_data = [{"question": q} for q in request.questions]

        # Analyze input tokens
        input_analysis = token_counter.analyze_validation_request_tokens(
            questions_data,
            request.xml_content,
            request.rag_data or {},
            request.focus_prompt
        )

        # Check token limits
        limit_check = token_counter.check_token_limit(input_analysis['total_input_tokens'])

        # Combine analyses
        response = {
            "model": input_analysis["model"],
            "timestamp": input_analysis["timestamp"],
            "input_analysis": input_analysis,
            "limit_check": limit_check,
            "recommendations": []
        }

        # Add recommendations based on analysis
        if limit_check["usage_percentage"] > 80:
            response["recommendations"].append("Consider reducing XML content or number of questions to stay within token limits")

        if input_analysis["estimated_cost"] > 0.10:
            response["recommendations"].append("High estimated cost - consider using gpt-4o-mini for cost optimization")

        if input_analysis["token_breakdown"]["xml_content"] > 10000:
            response["recommendations"].append("Large XML content detected - consider content optimization")

        if len(request.questions) > 20:
            response["recommendations"].append("Many questions detected - consider batching for better performance")

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Token analysis failed: {str(e)}")


@router.get("/validate/token-info")
async def get_token_info():
    """Get information about the current model's token limits and pricing."""
    try:
        model_limit = token_counter.get_model_token_limit()

        # Sample cost calculation
        sample_cost = token_counter._calculate_estimated_cost(1000, 500)

        return {
            "model": token_counter.model_name,
            "token_limit": model_limit,
            "encoding": token_counter.encoding.name,
            "sample_cost": {
                "input_tokens": 1000,
                "output_tokens": 500,
                "estimated_cost": sample_cost
            },
            "cost_per_1k_tokens": {
                "input": token_counter._calculate_estimated_cost(1000, 0),
                "output": token_counter._calculate_estimated_cost(0, 1000)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get token info: {str(e)}")