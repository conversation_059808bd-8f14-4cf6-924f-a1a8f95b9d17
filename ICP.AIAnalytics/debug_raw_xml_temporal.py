#!/usr/bin/env python3
"""
Debug raw XML content used for temporal questions vs processed XML.
"""

import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor

async def debug_raw_xml_temporal():
    """Debug raw XML vs processed XML for temporal questions."""
    print("Debugging Raw XML vs Processed XML for Report 1981692")
    print("=" * 60)
    
    file_processor = FileProcessor()
    validation_service = ValidationService()
    
    try:
        # Get the report data
        report_data = await file_processor.get_processed_report_by_report_id("1981692")
        
        if not report_data:
            print("❌ Report 1981692 not found")
            return
        
        print("✅ Report 1981692 found")
        print()
        
        # Get processed XML content
        xml_data = report_data.get('xml_data', {})
        if not xml_data:
            xml_data = report_data.get('xml_structure', {})
        
        processed_xml = validation_service._dict_to_xml_string(xml_data)
        print(f"📄 Processed XML length: {len(processed_xml)} characters")
        
        # Get raw XML content
        raw_xml = validation_service._get_raw_xml_with_registration_numbers(report_data)
        if raw_xml:
            print(f"📄 Raw XML length: {len(raw_xml)} characters")
        else:
            print("❌ Raw XML not available")
            return
        
        print()
        
        # Compare DateExpired in both XMLs
        print("🔍 Searching for DateExpired in both XMLs:")
        print("-" * 50)
        
        # Processed XML
        processed_date_start = processed_xml.find('<DateExpired>')
        if processed_date_start != -1:
            processed_date_end = processed_xml.find('</DateExpired>', processed_date_start)
            if processed_date_end != -1:
                processed_date_section = processed_xml[processed_date_start:processed_date_end + 14]
                print(f"✅ Processed XML DateExpired: {processed_date_section}")
            else:
                print("❌ Processed XML DateExpired end tag not found")
        else:
            print("❌ Processed XML DateExpired not found")
        
        # Raw XML
        raw_date_start = raw_xml.find('<DateExpired>')
        if raw_date_start != -1:
            raw_date_end = raw_xml.find('</DateExpired>', raw_date_start)
            if raw_date_end != -1:
                raw_date_section = raw_xml[raw_date_start:raw_date_end + 14]
                print(f"✅ Raw XML DateExpired: {raw_date_section}")
            else:
                print("❌ Raw XML DateExpired end tag not found")
        else:
            print("❌ Raw XML DateExpired not found")
        
        print()
        
        # Compare RegistrationNumbers sections
        print("🔍 Comparing RegistrationNumbers sections:")
        print("-" * 50)
        
        # Processed XML RegistrationNumbers
        processed_reg_start = processed_xml.find('<RegistrationNumbers>')
        if processed_reg_start != -1:
            processed_reg_end = processed_xml.find('</RegistrationNumbers>', processed_reg_start)
            if processed_reg_end != -1:
                processed_reg_section = processed_xml[processed_reg_start:processed_reg_end + 22]
                print("✅ Processed XML RegistrationNumbers section:")
                print(processed_reg_section[:1000] + "..." if len(processed_reg_section) > 1000 else processed_reg_section)
                print()
        
        # Raw XML RegistrationNumbers
        raw_reg_start = raw_xml.find('<RegistrationNumbers>')
        if raw_reg_start != -1:
            raw_reg_end = raw_xml.find('</RegistrationNumbers>', raw_reg_start)
            if raw_reg_end != -1:
                raw_reg_section = raw_xml[raw_reg_start:raw_reg_end + 22]
                print("✅ Raw XML RegistrationNumbers section:")
                print(raw_reg_section[:1000] + "..." if len(raw_reg_section) > 1000 else raw_reg_section)
                print()
        
        # Check if there are differences
        if processed_reg_start != -1 and raw_reg_start != -1:
            processed_reg_section = processed_xml[processed_reg_start:processed_reg_end + 22]
            raw_reg_section = raw_xml[raw_reg_start:raw_reg_end + 22]
            
            if processed_reg_section == raw_reg_section:
                print("✅ RegistrationNumbers sections are IDENTICAL")
            else:
                print("❌ RegistrationNumbers sections are DIFFERENT")
                print(f"Processed length: {len(processed_reg_section)}")
                print(f"Raw length: {len(raw_reg_section)}")
        
        print()
        
        # Look for any date-related differences
        print("🔍 Searching for all dates in both XMLs:")
        print("-" * 50)
        
        import re
        date_pattern = r'\d{1,2}-[A-Za-z]{3}-\d{4}'  # DD-MMM-YYYY format
        
        processed_dates = re.findall(date_pattern, processed_xml)
        raw_dates = re.findall(date_pattern, raw_xml)
        
        print(f"Processed XML dates found: {processed_dates}")
        print(f"Raw XML dates found: {raw_dates}")
        
        if processed_dates == raw_dates:
            print("✅ Date formats are IDENTICAL in both XMLs")
        else:
            print("❌ Date formats are DIFFERENT between XMLs")
        
        print()
        
        # Check for Comments sections
        print("🔍 Checking Comments sections:")
        print("-" * 50)
        
        processed_comments = processed_xml.count('<Comments>')
        raw_comments = raw_xml.count('<Comments>')
        
        print(f"Processed XML Comments tags: {processed_comments}")
        print(f"Raw XML Comments tags: {raw_comments}")
        
        if processed_comments > 0:
            comment_start = processed_xml.find('<Comments>')
            comment_end = processed_xml.find('</Comments>', comment_start)
            if comment_end != -1:
                comment_content = processed_xml[comment_start:comment_end + 11]
                print(f"Processed XML Comment: {comment_content}")
        
        if raw_comments > 0:
            comment_start = raw_xml.find('<Comments>')
            comment_end = raw_xml.find('</Comments>', comment_start)
            if comment_end != -1:
                comment_content = raw_xml[comment_start:comment_end + 11]
                print(f"Raw XML Comment: {comment_content}")
        
    except Exception as e:
        print(f"❌ Error debugging raw XML: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_raw_xml_temporal())
