#!/usr/bin/env python3
"""
Test script to debug registration number detection with the specific report.
"""

import sys
import os
import asyncio
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor

async def test_registration_debug():
    """Debug registration number detection for the specific report."""
    print("Debugging Registration Number Detection")
    print("=" * 50)
    
    report_id = "1984940"
    print(f"Report ID: {report_id}")
    print()
    
    # Initialize services
    validation_service = ValidationService()
    file_processor = FileProcessor()
    
    try:
        print("Step 1: Loading report data...")
        report_data = await file_processor.get_processed_report_by_report_id(report_id)
        
        if not report_data:
            print(f"❌ No report found for report_id: {report_id}")
            return
        
        print(f"✅ Report data loaded")
        print(f"Available keys: {list(report_data.keys())}")
        print()
        
        print("Step 2: Extracting XML data...")
        xml_data = report_data.get('xml_data', {})
        if not xml_data:
            xml_data = report_data.get('xml_structure', {})
            print("Using xml_structure as xml_data")
        
        if not xml_data:
            print("❌ No XML data found")
            return
        
        print(f"XML root keys: {list(xml_data.keys())}")
        print()
        
        print("Step 3: Examining LegalStatusSection in detail...")
        if 'Report' in xml_data and 'LegalStatusSection' in xml_data['Report']:
            legal_section = xml_data['Report']['LegalStatusSection']
            print(f"LegalStatusSection keys: {list(legal_section.keys())}")
            print()
            print("LegalStatusSection content:")
            print(json.dumps(legal_section, indent=2))
            print()
            
            # Check for any field that might contain registration numbers
            print("Searching for registration-related fields:")
            for key, value in legal_section.items():
                key_lower = key.lower()
                if any(word in key_lower for word in ['registration', 'reg', 'number', 'license', 'permit', 'certificate']):
                    print(f"  Found potential registration field: {key} = {value}")
            
            # Check if there are any fields with 'number' in the name
            print("\nFields containing 'number':")
            for key, value in legal_section.items():
                if 'number' in key.lower():
                    print(f"  {key}: {value}")
            
            # Check if there are any nested objects that might contain registration numbers
            print("\nNested objects in LegalStatusSection:")
            for key, value in legal_section.items():
                if isinstance(value, dict):
                    print(f"  {key}: {list(value.keys()) if isinstance(value, dict) else type(value)}")
                    # Check nested dict for registration fields
                    if isinstance(value, dict):
                        for nested_key, nested_value in value.items():
                            nested_key_lower = nested_key.lower()
                            if any(word in nested_key_lower for word in ['registration', 'reg', 'number', 'license']):
                                print(f"    Found nested registration field: {nested_key} = {nested_value}")
        else:
            print("❌ No LegalStatusSection found in Report")
        
        print()
        
        print("Step 4: Testing direct registration validation with debug...")
        result = validation_service.validate_registration_number_expiry(xml_data)
        print(f"Validation result:")
        print(f"  Status: {result['status']}")
        print(f"  Summary: {result['summary']}")
        print(f"  Details: {result['details']}")
        print()
        
        print("Step 5: Checking entire XML structure for any registration numbers...")
        def search_for_registration_fields(data, path=""):
            """Recursively search for registration-related fields."""
            found_fields = []
            
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}/{key}" if path else key
                    key_lower = key.lower()
                    
                    # Check if this key might be registration-related
                    if any(word in key_lower for word in ['registration', 'reg', 'number', 'license', 'permit', 'certificate']):
                        found_fields.append((current_path, key, value))
                    
                    # Recursively search nested structures
                    if isinstance(value, (dict, list)):
                        found_fields.extend(search_for_registration_fields(value, current_path))
            
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]"
                    if isinstance(item, (dict, list)):
                        found_fields.extend(search_for_registration_fields(item, current_path))
            
            return found_fields
        
        registration_fields = search_for_registration_fields(xml_data)
        
        if registration_fields:
            print(f"Found {len(registration_fields)} potential registration-related fields:")
            for path, key, value in registration_fields:
                print(f"  Path: {path}")
                print(f"  Key: {key}")
                print(f"  Value: {value}")
                print(f"  Type: {type(value)}")
                print()
        else:
            print("❌ No registration-related fields found anywhere in the XML")
        
        print("=" * 50)
        print("Debug completed!")
        print()
        print("CONCLUSION:")
        if not registration_fields:
            print("✅ The XML report genuinely does NOT contain any registration numbers.")
            print("✅ The validation is correctly returning 'No registration numbers found'.")
            print("✅ This is the expected behavior for this report.")
        else:
            print("⚠️  Registration-related fields were found but not in expected locations.")
            print("⚠️  The validation logic may need to be updated to handle these fields.")
    
    except Exception as e:
        print(f"❌ Error in debug: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_registration_debug())
