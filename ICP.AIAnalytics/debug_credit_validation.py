#!/usr/bin/env python3
"""
Debug script to test credit amount validation with a sample report.
This script creates a test report with credit information and validates it.
"""

import sys
import os
import asyncio
import json
import uuid
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor
from app.models.schemas import Question

async def debug_credit_validation():
    """Debug credit amount validation with a test report."""
    print("Debugging Credit Amount Validation")
    print("=" * 50)
    
    # Create a test report with credit information
    test_report_data = {
        "file_id": "test-credit-report",
        "report_id": "1981352",
        "xml_data": {
            "Report": {
                "HeaderSection": {
                    "Date": "22-Jul-2025",
                    "CompanyName": "L'Atelier de l'Ameublement"
                },
                "PersonnelSection": {
                    "Employees": {
                        "EmployeeDetails": {
                            "MinEmployees": "1",
                            "ExactOrEstimate": "Exact"
                        }
                    }
                },
                "PaymentsSection": {
                    "MaxCredit": "1000000",
                    "MaxCreditCurrency": "XPF",
                    "CreditFigure": "0",
                    "CreditOpinion": "Small"
                }
            }
        },
        "xml_structure": {
            "Report": {
                "HeaderSection": {
                    "Date": "22-Jul-2025",
                    "CompanyName": "L'Atelier de l'Ameublement"
                },
                "PersonnelSection": {
                    "Employees": {
                        "EmployeeDetails": {
                            "MinEmployees": "1",
                            "ExactOrEstimate": "Exact"
                        }
                    }
                },
                "PaymentsSection": {
                    "MaxCredit": "1000000",
                    "MaxCreditCurrency": "XPF",
                    "CreditFigure": "0",
                    "CreditOpinion": "Small"
                }
            }
        },
        "processing_timestamp": datetime.now().isoformat()
    }
    
    print("Test Report Data Created (Real Report Scenario):")
    print(f"Company: {test_report_data['xml_data']['Report']['HeaderSection']['CompanyName']}")
    print(f"Company Size (CreditOpinion): {test_report_data['xml_data']['Report']['PaymentsSection']['CreditOpinion']}")
    print(f"Employees: {test_report_data['xml_data']['Report']['PersonnelSection']['Employees']['EmployeeDetails']['MinEmployees']} (IRRELEVANT for company size)")
    print(f"Max Credit: {test_report_data['xml_data']['Report']['PaymentsSection']['MaxCredit']} {test_report_data['xml_data']['Report']['PaymentsSection']['MaxCreditCurrency']}")
    print(f"Credit Figure: {test_report_data['xml_data']['Report']['PaymentsSection']['CreditFigure']} (current credit)")
    print(f"Exchange Rate (XPF to GBP): 0.007257 (fallback rate)")
    print(f"Expected GBP Credit: £{int(test_report_data['xml_data']['Report']['PaymentsSection']['MaxCredit']) * 0.007257}")
    print()
    
    # Create test questions for credit validation
    test_questions = [
        Question(
            id="test-credit-small-large",
            question="Mark as an issue if a small company is associated with a large credit amount (convert to GBP and compare with thresholds).",
            category="company_credit_validation",
            client_code=None,
            darwin_reference_sections="(Company) Company Size, Number of Employees, Annual Turnover, (Credit) Max Credit, Credit Limit, (Financial) Credit Information",
            expected_outcome="rejected",
            client_specific_type="All"
        ),
        Question(
            id="test-credit-large-small",
            question="Mark as an issue if a large company is associated with a small credit amount (convert to GBP and compare with thresholds).",
            category="company_credit_validation",
            client_code=None,
            darwin_reference_sections="(Company) Company Size, Number of Employees, Annual Turnover, (Credit) Max Credit, Credit Limit, (Financial) Credit Information",
            expected_outcome="rejected",
            client_specific_type="All"
        )
    ]
    
    print("Test Questions Created:")
    for i, q in enumerate(test_questions, 1):
        print(f"{i}. {q.question}")
    print()
    
    # Initialize validation service
    validation_service = ValidationService()
    
    try:
        print("Step 1: Testing credit validation detection...")
        for q in test_questions:
            question_type = validation_service._detect_question_type(q.question)
            print(f"  Question: {q.question}")
            print(f"  Detected Type: {question_type}")
            print()

        print("Step 2: Testing holistic validation...")

        # Test holistic validation directly
        validation_results, token_analysis = await validation_service._validate_holistically(
            test_questions,
            test_report_data,
            enable_client_filtering=False,
            order_client_code=None,
            focus_prompt=None,
            include_low_confidence=True,
            min_confidence_threshold=0.3
        )
        
        print(f"✅ Holistic validation completed")
        print(f"Number of results: {len(validation_results)}")
        print()

        print("Step 3: Analyzing validation results...")
        for i, result in enumerate(validation_results, 1):
            print(f"Result {i}:")
            print(f"  Question: {result.question}")
            print(f"  Summary: {result.summary}")
            print(f"  Status: {result.status}")
            print(f"  Confidence: {result.confidence_score}")
            print(f"  Reasoning: {result.reasoning}")
            print(f"  Relevant Sections: {result.relevant_sections}")
            print()
        
        # Check if credit amounts are being recognized
        credit_mentioned = any("credit" in result.summary.lower() or "credit" in result.reasoning.lower() 
                              for result in validation_results)
        currency_mentioned = any("xpf" in result.summary.lower() or "gbp" in result.summary.lower() or 
                                "xpf" in result.reasoning.lower() or "gbp" in result.reasoning.lower()
                                for result in validation_results)
        conversion_mentioned = any("convert" in result.summary.lower() or "convert" in result.reasoning.lower()
                                  for result in validation_results)
        
        print("Step 4: Credit Recognition Analysis:")
        print(f"  Credit amounts mentioned: {'✅' if credit_mentioned else '❌'}")
        print(f"  Currency mentioned: {'✅' if currency_mentioned else '❌'}")
        print(f"  Currency conversion mentioned: {'✅' if conversion_mentioned else '❌'}")
        print()

        if not credit_mentioned:
            print("❌ ISSUE: Credit amounts are not being recognized in the validation")
            print("This suggests the prompt template or XML parsing is not working correctly")

        if not currency_mentioned:
            print("❌ ISSUE: Currency information is not being processed")
            print("The system should detect XPF and convert to GBP")

        if not conversion_mentioned:
            print("❌ ISSUE: Currency conversion is not being performed")
            print("The system should convert 1,000,000 XPF to £7,500 GBP")

        print("Step 5: Expected Analysis (Real Report Scenario):")
        print("  Company Size: Small (CreditOpinion = 'Small', employee count irrelevant)")
        print("  Credit Amount: MaxCredit 1,000,000 XPF = £7,257 GBP (Small Credit)")
        print("  Credit Figure: 0 (current credit, but use MaxCredit for validation)")
        print("  Expected Result: APPROVED (Small company with Small credit is acceptable)")
        print()

        # Test individual question validation for comparison
        print("Step 6: Testing individual question validation...")
        for i, question in enumerate(test_questions):
            try:
                individual_result = await validation_service._validate_single_question(
                    question, test_report_data, i + 1, False, None, None
                )
                print(f"Individual Result {i + 1}:")
                print(f"  Summary: {individual_result.summary}")
                print(f"  Status: {individual_result.status}")
                print(f"  Reasoning: {individual_result.reasoning}")
                print()
            except Exception as e:
                print(f"Error in individual validation {i + 1}: {e}")
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_credit_validation())
