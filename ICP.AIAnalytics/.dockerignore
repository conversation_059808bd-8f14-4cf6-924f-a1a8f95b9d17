# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
data/uploads/*
!data/uploads/.gitkeep
data/processed/*
!data/processed/.gitkeep
data/vector_db/*
!data/vector_db/.gitkeep
chroma/*
*.log
logs/

# Documentation and test files
docs/
tests/
postman/
*.md
README.md

# Development files
debug_excel.py
start.py
.env
.env.local
.env.*.local

# Docker
Dockerfile
.dockerignore
docker-compose*.yml 