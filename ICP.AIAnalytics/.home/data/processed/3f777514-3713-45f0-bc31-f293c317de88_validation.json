{"validation_id": "3f777514-3713-45f0-bc31-f293c317de88", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "57c587cc-1014-4329-8da1-327757b3a8fd", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "3a998da5-feac-48fe-bb90-0403b8ce26ac", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f6a92559-983e-47b6-9885-634989bfa3e7", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "VIOLATION: Client Specific Comments and Payment Section are missing despite Client Name being present.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "47e1cc60-ba7e-4787-a90e-4c5f2a534124", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "VIOLATION: Client Name is missing; comments in Payment and Special Notes sections are present.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b0470f09-9f6b-457f-ae8a-b7578f85b05f", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "CANNOT-VERIFY: Gross Profit and Total Income data not provided.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8b0b11b9-6708-48d7-941d-8c5eb6029798", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "CANNOT-VERIFY: No financial info section found to check expenses classification.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8ab8df8f-d961-4fe4-9276-7b19e47bef2a", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "COMPLIANT: Requested name matches Company Name, no Client Specific Comment needed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4a2bb9be-82c5-41f6-a5e2-599c3f39f12c", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "VIOLATION: Client address does not match active address; no specific comment provided.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ef9b90c4-ae65-463c-bbd6-f941c2be7988", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "VIOLATION: Max credit not found in special notes despite EUR, USD, GBP requested.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "2c741445-0617-40ca-92de-039581101fa8", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "VIOLATION: License numbers not recorded in legal or special notes sections.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f996a7a1-b979-485b-ab85-8a01c2bcaf09", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "VIOLATION: Colloquial language found in waiver section, lacks formal tone.", "confidence_score": 0.9, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7e65fd40-b92a-479e-a1f0-4fdb06f3a210", "question_number": 12, "question": "Spelling Check", "summary": "VIOLATION: Spelling errors found in 'LegalStatusAdditional' and 'Subsidiaries'.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/LegalStatusAdditional", "Report/LegalStatusSection/Subsidiaries"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8c215f84-d816-49dd-8f63-abc4d474d6fa", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "CANNOT-VERIFY: No significant changes or payments sections present to validate.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6ae5a385-4ca4-4e25-b8ba-af476a3ec46c", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "VIOLATION: Missing adverse announcements in significant changes and payments sections.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges", "Report/Payments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6cf19ab8-d478-40f9-be74-de34dd12b3d9", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "CANNOT-VERIFY: No adverse announcements found to check compliance.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "340d4da6-dc0f-4a91-b8b6-fbefa23e45e4", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "VIOLATION: Payments section lacks relevant sector information from Subject Activities.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2ef8600c-1d6c-423d-b595-617fb082f2fb", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "VIOLATION: Incorrect texts added regarding export activities in LegalStatusAdditional.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f772ac21-a3a2-4098-91d7-031aa1d11289", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "VIOLATION: Neither Income nor Total Income is present in the report.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitAndLoss/Income", "Report/ProfitAndLoss/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "25bc207b-f2c9-4adf-8094-250c502ff8b7", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "VIOLATION: Credit opinion currency requested, no credit given, TRA high not met.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "41448099-7cfc-47ff-b4f2-6bd836914034", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "VIOLATION: Country risk classification not provided despite inapplicability.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b858cab4-554b-4afc-a6ab-20337576f444", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "VIOLATION: CompanyStatus is 'Active' while ActiveAddress is 'false'.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5f3fb4e6-a3cc-4b0e-bf4b-4557e28e904e", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "CANNOT-VERIFY: License number and related entities section missing.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "45139218-a10e-4b03-9fbb-8589023da0a6", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "VIOLATION: Registration number expired with no Comment provided", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "78815911-02d0-41da-ac92-bf78f105490d", "question_number": 24, "question": "Small with large amount", "summary": "VIOLATION: PaidUp capital (123.00) is small compared to expected large amount.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fda1229a-9c8e-4e52-98ef-79b310bc4021", "question_number": 25, "question": "Large with small amount", "summary": "VIOLATION: PaidUp capital of 123.00 is small for a large entity.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/ICPLegalGroup"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2f45caaa-b930-4e70-8603-8cd88e1eed25", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "CANNOT-VERIFY: No position classifications found for comparison.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "92ac04f9-d2be-4b72-9a1a-6dc19c7f8c1c", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "VIOLATION: Research Type not specified as 'Negative'; other sections populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "3928cf1d-34fe-43fd-8572-e5b434c14e92", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "VIOLATION: Research Type is not 'No Contact'; <PERSON><PERSON> shows 'Decline'.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6163871d-7fc5-4f22-965e-3d0b1f405f76", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "VIOLATION: Research Type not found, payments comment missing.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/PaymentsSection/CreditOpinionNotes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6d2d8d44-1783-4507-9334-79ac148a933b", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "VIOLATION: Research Type is missing; no comment on contact or Person Interviewed present.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "13443a4e-981f-4e07-91a1-5c19d5bb56c2", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "VIOLATION: Sanction search not performed; missing required tag.", "confidence_score": 0.98, "relevant_sections": ["Report/SanctionSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "010237e0-d2d3-4ccc-9f8e-8847f93743ce", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "VIOLATION: No sanctions found; trade risk assessment not high.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed", "Report/SanctionSection/SanctionFound"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e7f81ae9-8286-4cdc-bb69-da3e5c831d1d", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "6d81ba6d-9707-4850-9669-08075f6abf1f", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "VIOLATION: Provenance icon missing in Head<PERSON>, Addresses, Legal sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a026b083-5e53-460e-9357-c5b807b0e6a0", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "VIOLATION: Research type is 'Select' but no contact details provided.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/AddressesSection/Addresses/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "cd580574-c3b7-4b98-9b2c-739093e78348", "question_number": 36, "question": "Capital missing currency", "summary": "VIOLATION: Capital currency field is missing; no currency specified for PaidUp capital.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "65adc697-7dec-4dfe-973c-5123d6faa668", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "VIOLATION: Shareholder not populated despite related entities present.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "36ee0689-fe12-40d8-bc64-353aa1212281", "question_number": 38, "question": "Calculate credit opinion", "summary": "CANNOT-VERIFY: Missing credit opinion calculation details in Payments section.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a3ae3368-f30c-4148-ab3e-7837d6d18e0e", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "COMPLIANT: CompanyName matches Requested in HeaderSection.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "af6d5255-c89d-47be-827f-b3b8217f1a94", "question_number": 40, "question": "Town missing in town box", "summary": "VIOLATION: Town box is empty in AddressesSection.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9191b504-3d78-49cd-9db0-711017d618ff", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "COMPLIANT: Company status is 'Active', credit can be granted.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 12:14:42.956891", "processing_time": 14.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}