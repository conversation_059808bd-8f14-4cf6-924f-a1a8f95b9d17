{"validation_id": "8e76fc79-e0dd-473e-88ee-8b91243e0037", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181489", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "55b97202-ed07-426a-aefe-7063538e9277", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "920186da-d678-4d3f-91af-14940b8f7484", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "7a1b97d3-a16a-4d4f-a17a-1ad27207bda3", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing, cannot verify comments and payment compliance", "confidence_score": 0.9, "relevant_sections": ["Order Details/Client Name", "Special Notes/Text for Specific Clients", "Payments/Credit Opinion Notes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "19743d75-6e98-4e64-83f2-59cfd83f8c22", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name missing, but Payment Section has notes, violating rule", "confidence_score": 0.95, "relevant_sections": ["Order Details/Client Name", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "723bd922-c00c-4d25-995f-5816b5e3a8c2", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit 80000.00 is less than Total Income 100000.00, compliant", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3703d0bd-339c-4e27-92e5-41d9e86fea73", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials in 'Operating Costs', violates Direct Costs rule", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f51dbbb5-bf73-4e14-8b13-bf270602ab77", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name missing; cannot verify compliance with 'AishwaryaTest'.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "dc484964-56f8-4d94-991c-a410010c358a", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address '789 Tertiary St' does not match active address '123 Main St'. Comment populated.", "confidence_score": 0.95, "relevant_sections": ["Report/AddressSection/Address", "Report/ClientSection/ClientAddress", "Report/ClientSection/ClientSpecificComment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ba821426-bb97-47fa-b947-8a781f0e92b6", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max Credit Currency 'AMD' not EUR/USD/GBP, no special note needed", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2c5ea8e8-b0dc-485e-8f61-fcb78009cb50", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/License numbers missing in LegalSection/SpecialNote", "confidence_score": 0.95, "relevant_sections": ["Report/LegalSection", "Report/SpecialNote"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ca26c563-1219-4f3c-866e-33040260c600", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased/colloquial language found in Payments activities special note", "confidence_score": 0.95, "relevant_sections": ["Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4aca3dfe-579d-418c-bc9e-9e2b80d8a67f", "question_number": 12, "question": "Spelling Check", "summary": "Spelling error in 'SourceDescription': 'testt', 'tes'", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6c20da6b-14ea-446c-b6e3-7926f135a641", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No reference to significant changes in Payments section", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f78f1b90-4cd2-4522-8c1d-51fea4bc7e35", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "TradeRiskAssessment 'Average' violates 'High' requirement", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c87a4cb5-5f3e-45b8-842c-d8e239463806", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found in Change Description section.", "confidence_score": 0.9, "relevant_sections": ["SignificantChanges/ChangeClassifications", "SignificantChanges/ChangeDescription"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5dc393e5-1720-4dbd-8dae-f9e5202ac227", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-specific info; notify editor", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6e2cdf0d-9608-40d0-aca4-d74e0007a91b", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No 'Services activities' text found in export sections, compliant", "confidence_score": 0.95, "relevant_sections": ["Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "27a79bad-2e7a-41fb-ae02-1e9f51d80a16", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Both Income and Total Income are populated with 100000.00", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2ecb844c-5016-4b97-a2df-0a19e8a086d1", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "TRA 'Average' not 'High', no editor notification needed", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b6a0b654-35e3-4943-867b-08648d6ae6d1", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not applicable, no comment link found", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c29061ea-a45c-4a7d-a277-058345e5471c", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 939. Please try again in 1.878s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "aa88fa0d-14ac-4ba5-8fea-7296de46b013", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1121. Please try again in 2.242s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4b3f7031-0412-4e20-be86-e1da41cb16c2", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1020. Please try again in 2.04s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "cb2f7552-82fb-4832-82a8-ed695a750e8b", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1134. Please try again in 2.268s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "dba713a4-6ebb-4802-88a6-903f1ab91ef5", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1126. Please try again in 2.252s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5c37161f-0eb8-47a9-a820-efecac5f47e9", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not found, cannot verify compliance", "confidence_score": 0.85, "relevant_sections": ["Personnel/Position", "PositionClassifications"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "da2971f5-e1b8-44e6-b56d-7bac0a1a1ec5", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1059. Please try again in 2.118s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4e781eef-5371-43b3-a692-16001f19aab1", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 969. Please try again in 1.938s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "3d8c4b61-bec1-461a-8c1b-cd6b01c8ee24", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28301, Requested 2990. Please try again in 2.582s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "9ca9bbf6-9473-4cb1-97ba-4458b956f01a", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is missing, no comment on contact, Person Interviewed absent", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection", "Report/SpecialNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "86e95b19-7ee2-431d-9364-88cf057a55ff", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanction Search Performed tag missing, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/Sanction"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fc55087d-5b2c-421a-a824-6abb2f87e17a", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "TradeRiskAssessment 'High', CreditOpinion and MaxCredit blank, compliant", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment", "Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/MaxCredit"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "810e372c-38af-4d55-a16f-775e885198a5", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "d98384ce-e591-4381-b7cd-f07aa2212fa6", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in FinancialSection/BalanceSheets", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "dd857800-6c17-42dc-937c-d1114ad7a744", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type 'Select' but no contact details found", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/AddressSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5ff88c83-900e-4bb1-b220-7701c9d5b7af", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency missing in XML, expected notification for blank field", "confidence_score": 0.95, "relevant_sections": ["LegalStatus/Capital", "LegalStatus/Currency"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "724a5dce-ca1b-445e-8ecb-9a61ac6b3ef7", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder info missing for group member 'AishwaryaTest'", "confidence_score": 0.95, "relevant_sections": ["Report/FinancialSection/BalanceSheetFigures/AccountType", "Report/FinancialSection/BalanceSheetFigures/CompanyName"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7a0eaa20-4381-41da-9ef0-015dba18df33", "question_number": 38, "question": "Calculate credit opinion", "summary": "Credit opinion 'Large' aligns with 'TermsStipulated' and 'Average' risk.", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/OpinionOnMaxCredit", "Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f5adc389-09b9-4ea4-b2d8-27ff75e94dc0", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'AishwaryaTest' matches in header and financials", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/FinancialSection/BalanceSheetFigures/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9e191988-f978-4402-9524-63ab12d342c3", "question_number": 40, "question": "Town missing in town box", "summary": "Town box missing in XML, cannot verify compliance", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "744008ec-df84-46e9-a560-da7c0cb73481", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status unknown, credit granted; notify editor", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/LegalSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 16:18:43.981599", "processing_time": 216.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}