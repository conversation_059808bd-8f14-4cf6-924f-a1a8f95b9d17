"""
Token counting utility using tiktoken for measuring token usage in validation requests.
"""

import tiktoken
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

from app.core.config import settings


class TokenCounter:
    def __init__(self):
        """Initialize token counter with the appropriate encoding for the model."""
        # Get encoding based on the model
        model_name = settings.OPENAI_MODEL
        
        # Map model names to encodings
        if "gpt-4" in model_name.lower():
            self.encoding = tiktoken.encoding_for_model("gpt-4")
        elif "gpt-3.5" in model_name.lower():
            self.encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
        else:
            # Default to cl100k_base encoding (used by gpt-4 and gpt-3.5-turbo)
            self.encoding = tiktoken.get_encoding("cl100k_base")
        
        self.model_name = model_name
        
    def count_tokens(self, text: str) -> int:
        """Count tokens in a text string."""
        if not text:
            return 0
        return len(self.encoding.encode(text))
    
    def count_tokens_in_messages(self, messages: List[Dict[str, str]]) -> int:
        """Count tokens in a list of messages (for chat completions)."""
        total_tokens = 0
        
        for message in messages:
            # Count tokens in message content
            content = message.get("content", "")
            role = message.get("role", "")
            
            # Add tokens for content
            total_tokens += self.count_tokens(content)
            
            # Add tokens for role and formatting
            total_tokens += self.count_tokens(role)
            
            # Add overhead tokens for message formatting (approximate)
            total_tokens += 4  # Overhead per message
        
        # Add overhead for the conversation
        total_tokens += 2  # Conversation overhead
        
        return total_tokens
    
    def analyze_validation_request_tokens(
        self, 
        questions: List[Dict[str, Any]], 
        xml_content: str,
        rag_data: Optional[Dict[str, Any]] = None,
        focus_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze token usage for a validation request."""
        
        analysis = {
            "model": self.model_name,
            "timestamp": datetime.now().isoformat(),
            "token_breakdown": {},
            "total_input_tokens": 0,
            "estimated_cost": 0.0
        }
        
        # Count tokens in XML content
        xml_tokens = self.count_tokens(xml_content)
        analysis["token_breakdown"]["xml_content"] = xml_tokens
        
        # Count tokens in questions
        questions_text = "\n".join([q.get("question", "") for q in questions])
        questions_tokens = self.count_tokens(questions_text)
        analysis["token_breakdown"]["questions"] = questions_tokens
        
        # Count tokens in RAG data if present
        rag_tokens = 0
        if rag_data:
            rag_text = json.dumps(rag_data, ensure_ascii=False)
            rag_tokens = self.count_tokens(rag_text)
        analysis["token_breakdown"]["rag_data"] = rag_tokens
        
        # Count tokens in focus prompt if present
        focus_prompt_tokens = 0
        if focus_prompt:
            focus_prompt_tokens = self.count_tokens(focus_prompt)
        analysis["token_breakdown"]["focus_prompt"] = focus_prompt_tokens
        
        # Count tokens in system prompt (estimated)
        system_prompt_tokens = self.count_tokens(
            "You are an expert XML report compliance validator performing comprehensive holistic validation."
        )
        analysis["token_breakdown"]["system_prompt"] = system_prompt_tokens
        
        # Count tokens in instruction prompt (estimated based on typical holistic prompt)
        instruction_tokens = 2000  # Estimated based on typical prompt structure
        analysis["token_breakdown"]["instruction_prompt"] = instruction_tokens
        
        # Calculate total input tokens
        total_input_tokens = (
            xml_tokens + 
            questions_tokens + 
            rag_tokens + 
            focus_prompt_tokens + 
            system_prompt_tokens + 
            instruction_tokens
        )
        analysis["total_input_tokens"] = total_input_tokens
        
        # Estimate output tokens (based on number of questions)
        estimated_output_tokens = len(questions) * 150  # Rough estimate per question
        analysis["estimated_output_tokens"] = estimated_output_tokens
        
        # Calculate estimated cost (approximate pricing)
        analysis["estimated_cost"] = self._calculate_estimated_cost(
            total_input_tokens, 
            estimated_output_tokens
        )
        
        return analysis
    
    def analyze_validation_response_tokens(
        self, 
        response_text: str,
        validation_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze token usage in the validation response."""
        
        analysis = {
            "model": self.model_name,
            "timestamp": datetime.now().isoformat(),
            "response_tokens": 0,
            "results_breakdown": {}
        }
        
        # Count tokens in the raw LLM response
        response_tokens = self.count_tokens(response_text)
        analysis["response_tokens"] = response_tokens
        
        # Analyze tokens per validation result
        total_result_tokens = 0
        for i, result in enumerate(validation_results):
            result_text = json.dumps(result, ensure_ascii=False)
            result_tokens = self.count_tokens(result_text)
            total_result_tokens += result_tokens
            
            analysis["results_breakdown"][f"result_{i+1}"] = {
                "question": result.get("question", "")[:50] + "...",
                "tokens": result_tokens,
                "summary_tokens": self.count_tokens(result.get("summary", "")),
                "reasoning_tokens": self.count_tokens(result.get("reasoning", ""))
            }
        
        analysis["total_results_tokens"] = total_result_tokens
        analysis["average_tokens_per_result"] = (
            total_result_tokens / len(validation_results) if validation_results else 0
        )
        
        return analysis
    
    def _calculate_estimated_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Calculate estimated cost based on model pricing."""
        # Approximate pricing (as of 2024) - these should be updated with current rates
        pricing = {
            "gpt-4o": {"input": 0.005 / 1000, "output": 0.015 / 1000},
            "gpt-4o-mini": {"input": 0.00015 / 1000, "output": 0.0006 / 1000},
            "gpt-4": {"input": 0.03 / 1000, "output": 0.06 / 1000},
            "gpt-3.5-turbo": {"input": 0.001 / 1000, "output": 0.002 / 1000}
        }
        
        # Find matching pricing
        model_pricing = None
        for model_key, prices in pricing.items():
            if model_key in self.model_name.lower():
                model_pricing = prices
                break
        
        if not model_pricing:
            # Default to gpt-4o-mini pricing
            model_pricing = pricing["gpt-4o-mini"]
        
        input_cost = input_tokens * model_pricing["input"]
        output_cost = output_tokens * model_pricing["output"]
        
        return round(input_cost + output_cost, 6)
    
    def get_model_token_limit(self) -> int:
        """Get the token limit for the current model."""
        limits = {
            "gpt-4o": 128000,
            "gpt-4o-mini": 128000,
            "gpt-4": 8192,
            "gpt-3.5-turbo": 4096
        }
        
        for model_key, limit in limits.items():
            if model_key in self.model_name.lower():
                return limit
        
        # Default to conservative limit
        return 4096
    
    def check_token_limit(self, total_tokens: int) -> Dict[str, Any]:
        """Check if token count is within model limits."""
        limit = self.get_model_token_limit()
        
        return {
            "total_tokens": total_tokens,
            "token_limit": limit,
            "within_limit": total_tokens <= limit,
            "usage_percentage": round((total_tokens / limit) * 100, 2),
            "tokens_remaining": max(0, limit - total_tokens)
        }
