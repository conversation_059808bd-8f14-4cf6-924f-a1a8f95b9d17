# XML Report Validation System (RAG-Enabled Version)

An intelligent XML report validation system that uses AI and RAG (Retrieval-Augmented Generation) to validate XML reports against questions from Excel files. Features vector database integration for enhanced semantic search and context-aware responses.

## Features

- **Excel Question Upload**: Upload validation questions from Excel files
- **XML Report Processing**: Process and validate XML reports
- **External API Integration**: Fetch XML reports from external APIs with base64 content
- **AI-Powered Validation**: Use OpenAI GPT models for intelligent validation with three-status classification
- **RAG Integration**: Retrieval-Augmented Generation with vector database for enhanced context understanding
- **Vector Database**: ChromaDB integration for semantic search and document retrieval
- **Smart Status System**: Three-tier status classification (approved/rejected/manual_intervention_needed)
- **XML Output**: Return validation results in structured XML format
- **REST API**: FastAPI-based backend with comprehensive endpoints
- **Client-Specific Validation**: Filter questions by client code for targeted validation
- **High Performance**: Optimized batch processing and parallel execution

## Tech Stack

- **Backend**: FastAPI
- **AI/ML**: OpenAI API, LangChain
- **Vector Database**: ChromaDB (embedded or server mode)
- **File Processing**: pandas (Excel), xml.etree.ElementTree (XML)
- **HTTP Client**: httpx (for external API integration)
- **API Testing**: Postman collection included

## Project Structure

```
ICP.AIAnalytics/            # Main FastAPI project
├── app/
│   ├── __init__.py
│   ├── main.py             # FastAPI application entry point
│   ├── api/                # API endpoints
│   ├── core/               # Core business logic
│   ├── models/             # Data models
│   ├── services/           # Business services (including vector store)
│   └── utils/              # Utility functions
├── data/
│   ├── uploads/            # Uploaded files storage
│   ├── processed/          # Processed files
│   └── vector_db/          # Vector database storage
├── tests/                  # Test files
├── docs/                   # Documentation
├── postman/                # Postman collection
├── requirements.txt        # Python dependencies
├── .env.example            # Environment variables template
├── start.py                # Startup script
└── README.md               # This file
```

## Vector Database Setup

The system uses ChromaDB for vector storage and RAG capabilities:

### Directory Structure
```
/home/<USER>/icp-ai/
├── ICP.AIAnalytics/                 # Your main FastAPI project
│   ├── app/
│   │   ├── services/
│   │   │   └── vector_store.py     # Vector store service
│   │   └── ...
│   └── ...
└── VectorDB/                       # Parallel vector database directory
    ├── embedded/                   # For embedded ChromaDB
    └── docker/                     # For Docker ChromaDB
```

### Setup Commands
```bash
# Verify vector database setup
python verify_vectordb.py

# Test vector database connectivity
python test_vectordb_setup.py

# Run in embedded mode (default)
python start.py

# Run with Docker ChromaDB
docker-compose -f docker-compose.chromadb.yml up -d
CHROMADB_MODE=server python start.py
```

## Getting Started

### Prerequisites

- Python 3.8+
- OpenAI API key
- pip package manager
- ChromaDB (automatically installed)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your OpenAI API key
   ```

4. Set up vector database:
   ```bash
   python verify_vectordb.py
   ```

5. Run the application:
   ```bash
   # Option 1: Use the startup script
   python start.py
   
   # Option 2: Using uvicorn directly
   uvicorn app.main:app --reload
   ```

## RAG Architecture

The system implements Retrieval-Augmented Generation (RAG) for enhanced validation:

### How RAG Works:
1. **Document Ingestion**: XML reports are processed and stored in vector database
2. **Question Processing**: Validation questions are embedded and stored
3. **Semantic Search**: For each question, relevant XML sections are retrieved
4. **Context-Aware Generation**: AI generates responses using retrieved context
5. **Validation**: Results are classified into three status categories

### Vector Database Benefits:
- **Semantic Understanding**: Finds relevant content even with different wording
- **Context Preservation**: Maintains document structure and relationships
- **Scalability**: Handles large documents efficiently
- **Performance**: Fast retrieval of relevant sections

## API Endpoints

### File Upload & Processing
- `POST /upload/questions` - Upload Excel file with questions
- `POST /upload/report` - Upload XML report for validation
- `POST /upload/report-from-api` - Fetch XML report from external API
- `POST /upload/order-details` - Fetch order details to get client code

### Validation & Analysis
- `POST /validate` - Validate report against questions (with RAG enhancement)
- `GET /history` - Get validation history

### Client-Specific Validation

The system supports client-specific validation by matching client codes from order details with Excel question files.

**Feature**: Client Code Filtering
- Questions in Excel files can include a `client_code` column
- System fetches client code from order details API
- Only validates questions where client codes match
- Questions without client codes are validated for all clients

### Enhanced Excel File Format

The system now supports an enhanced Excel format with improved validation capabilities:

| Client Specific / All Report Client | Validation Details (Questions) | Darwin Reference Section(s) | Expected Outcome | Editor |
|---|---|---|---|---|
| Client | Max Credit Currency must be EUR or USD | (Payments) Max Credit Currency | Currency should be EUR or USD only | |
| All | Max Credit Currency should be the same currency as the company location EUR or USD | (Payments) Max Credit Currency | Currency should be the same local currency as the company location | |
| All | If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section? | (Order Details) Client Name, (Payments) Credit Opinion Notes, (Special Notes) Text for Specific Clients | The system should display a short details indicating whether the respective fields, Client Specific Comments, and Payment Section are filled or not on the basis of client name. | |

**Enhanced Column Rules:**

**Required:**
- **Validation Details (Questions)**: The main validation question text

**New Enhanced Columns:**
- **Client Specific / All Report Client**: Specifies scope - "All" (applies to all clients), "Client" (generic client), or specific client codes
- **Darwin Reference Section(s)**: Specific XML sections to focus analysis on (e.g., "(Payments) Max Credit Currency")
- **Expected Outcome**: What the validation should verify or achieve
- **Editor**: Editor or author information

**Legacy Columns (still supported):**
- **question**: Alternative column name for validation questions
- **category**: Question category
- **priority**: Question priority (default: medium)
- **client_code**: Direct client code specification

**Enhanced Features:**
- **Focused Analysis**: Darwin Reference Sections guide the LLM to specific XML areas
- **Outcome Alignment**: Expected Outcomes help validate against business requirements
- **Improved Context**: Additional fields provide better context for AI analysis
- **Client Filtering**: Enhanced client-specific validation capabilities

## Three-Status Validation System

The system uses a three-status classification for validation results:

- **`approved`**: Information is clear, complete, and indicates positive/satisfactory results
- **`rejected`**: Information is clear, complete, and indicates negative/unsatisfactory results  
- **`manual_intervention_needed`**: Information is insufficient, unclear, ambiguous, or the question is too vague to provide a definitive answer

### Status Assignment Guidelines:

**Approved Status:**
- No errors or issues found
- Successful processing/completion
- Compliance requirements met
- High quality data/results
- Clear positive indicators

**Rejected Status:**
- Clear errors, issues, or problems identified
- Failed validation or processing
- Non-compliance with requirements
- Poor quality data/results
- Clear negative indicators

**Manual Intervention Needed:**
- Insufficient data to make determination
- Ambiguous or unclear results
- Vague questions requiring clarification
- Mixed signals (both positive and negative indicators)
- Missing context or incomplete information

## Usage Examples

### **New: Permanent Question Bank Workflow (Recommended)**

The system now supports a permanent question bank that eliminates the need to upload questions for each validation:

1. **Setup Permanent Question Bank (One Time Only):**
   ```bash
   curl -X POST "http://localhost:8000/api/v1/validate/permanent-questions/upload" \
     -F "file=@questions.xlsx"
   ```

2. **Fetch Report and Validate (Simplified):**
   ```bash
   # Fetch XML Report
   curl -X POST "http://localhost:8000/api/v1/upload/report-from-api" \
     -H "Content-Type: application/json" \
     -d '{
       "report_id": "172390",
       "bearer_token": "your_bearer_token_here"
     }'
   
   # Validate Report (No questions_file_id needed!)
   curl -X POST "http://localhost:8000/api/v1/validate" \
     -H "Content-Type: application/json" \
     -d '{
       "report_file_id": "your-report-file-id"
     }'
   ```

3. **Check Permanent Question Bank Status:**
```bash
   curl -X GET "http://localhost:8000/api/v1/validate/permanent-questions/status"
   ```

### **Benefits of Permanent Question Bank:**
- **One-time setup**: Upload questions once, use for all reports
- **Simplified API calls**: No `questions_file_id` needed in requests
- **Consistent validation**: Same questions applied to all reports
- **Easy updates**: Update question bank without changing validation workflow

## Configuration

Key configuration options (set via environment variables):
- `OPENAI_API_KEY`: Required for AI functionality
- `CHROMADB_MODE`: "embedded" or "server" for vector database
- `CHROMADB_PATH`: Path for embedded ChromaDB storage
- `VECTOR_DB_PATH`: Path for parallel vector database directory
- `MAX_FILE_SIZE`: Maximum upload file size
- `MAX_TOKENS`: Maximum tokens for AI responses
- `DEFAULT_BATCH_SIZE`: Number of questions processed in parallel

## Performance Benefits

### RAG-Enhanced Architecture Advantages:
- **Semantic Search**: Finds relevant content even with different wording
- **Context Awareness**: AI responses are grounded in retrieved document sections
- **Fast Retrieval**: Vector database provides quick semantic search
- **Precision**: RAG improves answer accuracy and relevance
- **Scalability**: Handles large documents efficiently

### Expected Performance:
- **Enhanced accuracy** through RAG-powered context retrieval
- **Faster processing** with optimized vector search
- **Better semantic understanding** of questions and content
- **Improved scalability** for large document collections

## Testing

Use the included Postman collection in the `postman/` directory for API testing.

### Run Basic Tests:
```bash
python tests/test_basic.py
```

### Test Vector Database:
```bash
python test_vectordb_setup.py
```

## License

This project is licensed under the MIT License.