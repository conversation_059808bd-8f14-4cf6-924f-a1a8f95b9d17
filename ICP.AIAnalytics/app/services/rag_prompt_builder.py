"""
RAG-enhanced prompt builder for validation responses.
Incorporates retrieved examples and patterns into LLM prompts.
"""

from typing import List, Dict, Any, Optional
from app.models.schemas import Question
from datetime import datetime


class RAGPromptBuilder:
    def __init__(self):
        pass

    def get_current_date(self) -> str:
        """
        Get the current date for temporal validation purposes.
        Returns the current date in YYYY-MM-DD format.
        """
        return datetime.now().strftime("%Y-%m-%d")

    def _is_temporal_validation(self, question: str) -> bool:
        """Check if this is a temporal validation question involving dates or expiry."""
        question_lower = question.lower()
        return any(word in question_lower for word in ["date", "time", "expiry", "period", "expired", "expire"])
    
    def build_rag_enhanced_prompt(
        self,
        question: Question,
        xml_content: str,
        question_number: int,
        retrieved_data: Dict[str, Any],
        retry: bool = False,
        focus_prompt: str = None
    ) -> str:
        """Build a RAG-enhanced validation prompt with retrieved examples and patterns."""
        
        base_prompt = f"""You are an expert XML report compliance validator with access to a knowledge base of correct validation examples. Your job is to check if the XML data follows the specific validation rule and provide accurate, actionable findings.

VALIDATION RULE: {question.question}"""
        
        # Add Darwin reference sections if available
        if question.darwin_reference_sections:
            base_prompt += f"""

DARWIN REFERENCE SECTIONS (PRIMARY FOCUS): {question.darwin_reference_sections}

DARWIN TARGETING INSTRUCTIONS:
- These Darwin Reference Sections indicate the MOST IMPORTANT XML areas for this validation
- Focus your analysis primarily on XML content related to these sections
- Look for XML paths and elements that correspond to these Darwin references
- Examples: "(Payments) Max Credit Currency" → focus on payment/credit/currency XML elements
- Examples: "(Header) Company Name, Requested" → focus on header section company and requested name fields
- Use these sections to guide your compliance checking and ensure accuracy"""
        
        # Add expected outcome if available
        if question.expected_outcome:
            base_prompt += f"""

EXPECTED OUTCOME: {question.expected_outcome}
Verify if the XML data meets this expected validation outcome and align your compliance findings accordingly."""
        
        # Add client-specific information if available
        if question.client_specific_type:
            base_prompt += f"""

CLIENT SCOPE: {question.client_specific_type}
Apply validation rules considering the client-specific requirements (e.g., "All" = applies to all clients, "Client" = client-specific rule)."""

        # Add temporal validation logic for registration expiry questions
        if self._is_temporal_validation(question.question):
            current_date = self.get_current_date()
            base_prompt += f"""

TEMPORAL VALIDATION - REGISTRATION NUMBER EXPIRY LOGIC:
- CURRENT DATE: Today is {current_date} (use this as the reference date for all expiry comparisons)
- XML STRUCTURE WARNING: RegistrationNumbers contains nested RegistrationNumber elements!
  Look for: <RegistrationNumbers><RegistrationNumber>...</RegistrationNumber></RegistrationNumbers>
- EXPIRY LOGIC: A registration number is expired ONLY if:
  1. It has an expiry date present in the data, AND
  2. The expiry date is before or equal to the current date ({current_date})
- NOT EXPIRED: If a registration number has no expiry date, it is NOT considered expired
- NOT EXPIRED: If a registration number has an expiry date after {current_date}, it is NOT expired
- EXPIRED: If a registration number has an expiry date on or before {current_date}, it IS expired
- DATE FORMATS: Handle various date formats (DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD, DD Month YYYY, etc.)
- COMMENT REQUIREMENT: If any registration number is expired, check if there's an appropriate comment explaining the expiry
- VALIDATION STEPS:
  1. Find all registration numbers in the XML data
  2. For each registration number, check if it has an expiry date
  3. If expiry date exists, compare it to {current_date}
  4. If any registration number is expired, verify if there's a comment explaining the expiry
  5. Report compliance based on whether expired registrations have explanatory comments

REGISTRATION NUMBER EXPIRY VALIDATION OUTCOMES:
- APPROVED: If no registration numbers are expired, OR if all expired registration numbers have explanatory comments
- REJECTED: If any registration number is expired AND there is no explanatory comment for the expiry
- MANUAL_INTERVENTION_NEEDED: If registration numbers are blank/not available, or if expiry dates cannot be determined

DETAILED VALIDATION LOGIC FOR REGISTRATION NUMBER EXPIRY:
1. FIND ALL REGISTRATION NUMBERS: Search for all RegistrationNumber elements in LegalStatusSection and RelatedEntitiesLegalStatusSection
2. FOR EACH REGISTRATION NUMBER:
   a) Check if DateExpired field exists and has a value
   b) If DateExpired is blank/empty/missing: Registration number is NOT expired (no expiry date means no expiry)
   c) If DateExpired has a value: Parse the date and compare to current date ({current_date})
   d) If DateExpired <= {current_date}: Registration number IS expired
   e) If DateExpired > {current_date}: Registration number is NOT expired
3. FOR EXPIRED REGISTRATION NUMBERS:
   a) Check if there is a Comments field with explanatory text about the expiry
   b) Look for comments in the same RegistrationNumber element or in general Comments sections
4. FINAL DETERMINATION:
   - If NO registration numbers found: Status = "manual_intervention_needed", Summary = "No registration numbers found in the report"
   - If registration numbers found but ALL have blank/missing expiry dates: Status = "manual_intervention_needed", Summary = "Registration numbers present but no expiry dates available for validation"
   - If ALL registration numbers are not expired: Status = "approved", Summary = "All registration numbers are current (not expired)"
   - If ANY registration number is expired AND has explanatory comment: Status = "approved", Summary = "Expired registration numbers found but all have explanatory comments"
   - If ANY registration number is expired AND lacks explanatory comment: Status = "rejected", Summary = "Expired registration numbers found without explanatory comments"

RESPONSE FORMAT REQUIREMENTS:
- Always state the specific expiry date found (e.g., "27-Nov-2024")
- Always clearly state "EXPIRED" or "NOT EXPIRED" for each registration number
- Keep responses concise and factual - one line summaries only
- Example: "Registration number 123 expires 27-Nov-2024 - EXPIRED (before 2025-07-30), no explanatory comment found"
- Example: "Registration number 456 expires 15-Dec-2025 - NOT EXPIRED (after 2025-07-30)"
- Do not make assumptions about missing data - state exactly what is found """
        
        # Add retry instruction if this is a retry
        if retry:
            base_prompt += """

RETRY INSTRUCTION: Provide a different validation finding to avoid duplicating previous responses. Focus on different aspects or provide alternative analysis."""
        
        # Add focus prompt if provided by frontend
        if focus_prompt:
            base_prompt += f"""

FOCUS PROMPT (Frontend Input): {focus_prompt}

FOCUS INSTRUCTIONS:
- This is a specific request from the user to regenerate the report with particular focus
- The focus prompt may include:
  * A new question or angle to explore
  * A specific section to emphasize in the analysis
  * A particular aspect of the validation rule to examine more closely
  * Additional context or requirements for the validation
- Incorporate this focus into your validation analysis while maintaining compliance checking accuracy
- Ensure your response addresses the specific focus area mentioned in the prompt
- If the focus prompt asks about a specific section, prioritize that section in your analysis"""
        
        # Add RAG-retrieved examples and patterns
        rag_section = self._build_rag_section(retrieved_data)
        if rag_section:
            base_prompt += f"""

{rag_section}"""
        
        base_prompt += f"""

XML DATA TO CHECK:
{xml_content}

ENHANCED VALIDATION METHODOLOGY:
1. PARSE THE RULE: Break down the specific requirement being checked
2. IDENTIFY DARWIN SECTIONS: If Darwin Reference Sections are provided, locate corresponding XML elements first
3. LOCATE RELEVANT DATA: Find exact XML elements and values needed (prioritizing Darwin-referenced areas)
4. APPLY BUSINESS LOGIC: Check if the data meets the rule requirements
5. APPLY CREDIT SIZE CLASSIFICATION: For credit-related validations, classify based on GBP thresholds
6. PROVIDE SPECIFIC VERDICT: State exactly what was found and whether it complies
7. LEARN FROM EXAMPLES: Use the provided knowledge base examples to guide your response style and accuracy

CREDIT SIZE CLASSIFICATION RULES:
When validating credit amounts or financial thresholds, apply the following classification:
- Small Credit: Less than GBP 50,000
- Medium Credit: GBP 50,000 - GBP 250,000
- Large Credit: Above GBP 250,000

CURRENCY CONVERSION REQUIREMENTS:
- Always convert non-GBP amounts to GBP equivalent for credit size classification
- Use current exchange rates when available in the XML data
- If exchange rates are not available, state "Cannot classify credit size - exchange rate not available"
- After conversion, compare against GBP thresholds to determine credit size category
- Include both original amount and GBP equivalent in your analysis

CRITICAL ACCURACY REQUIREMENTS:
1. SPECIFIC VALUES: Always include actual values found in XML (names, numbers, dates, amounts)
2. EXACT COMPARISON: Compare actual vs expected values when checking rules
3. CLEAR VERDICT: State whether rule is followed, violated, or cannot be verified
4. ACTIONABLE INSIGHT: Make it clear what action is needed (if any)
5. PROFESSIONAL LANGUAGE: Use business-appropriate language without technical jargon
6. DARWIN FOCUS: If Darwin Reference Sections are provided, ensure your analysis primarily targets those areas
7. EXAMPLE-GUIDED: Follow the patterns and quality standards demonstrated in the knowledge base examples
8. CREDIT CLASSIFICATION: For financial amounts, always classify as Small/Medium/Large credit based on GBP thresholds
9. CURRENCY HANDLING: Convert all amounts to GBP before applying credit size classification
10. Never present generated, inferred, speculated, or deduced content as fact
11. If you cannot verify something directly, say: "I cannot verify this.", "I do not have access to that information."
12. Ask for clarification if information is missing. Do not guess or fill gaps.
13. CASE-INSENSITIVE COMPARISON: When comparing string values, ignore case differences

CONDITIONAL RULE HANDLING:
- If the 'if' condition of a rule is not met, the rule is automatically 'approved'
- Example: For "If 'Income' is populated, 'Total Income' must be populated", if 'Income' is empty, condition not met → rule approved
- Example: For "If research type is 'Negative', specific sections must be present", if research type is 'Standard', condition not met → rule approved

RESPONSE FORMAT - Valid JSON only:
{{
    "summary": "Specific validation finding with actual values from Darwin-targeted sections (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Brief explanation of validation logic applied with Darwin section focus and knowledge base guidance"
}}

STATUS DETERMINATION:
- "approved": Rule is clearly followed/no violations found in Darwin-targeted areas
- "rejected": Rule is clearly violated/non-compliant condition exists in examined sections  
- "manual_intervention_needed": Missing data, ambiguity, or low confidence prevents clear determination

ACCURACY FOCUS: Provide exact values, specific comparisons, and clear compliance verdicts based on Darwin Reference Section guidance and knowledge base examples when available."""
        
        return base_prompt
    
    def _build_rag_section(self, retrieved_data: Dict[str, Any]) -> str:
        """Build the RAG section with retrieved examples and patterns."""
        if not retrieved_data:
            return ""
        
        examples = retrieved_data.get("examples", [])
        patterns = retrieved_data.get("patterns", [])
        
        if not examples and not patterns:
            return ""
        
        rag_section = "KNOWLEDGE BASE GUIDANCE:"
        
        # Add relevant examples
        if examples:
            rag_section += "\n\nRELEVANT EXAMPLES FROM KNOWLEDGE BASE:"
            
            for i, example in enumerate(examples[:3], 1):  # Limit to top 3 examples
                metadata = example.get("metadata", {})
                relevance = example.get("relevance_score", 0)
                
                if relevance > 0.5:  # Only include highly relevant examples
                    rag_section += f"\n\nExample {i} (Relevance: {relevance:.2f}):"
                    
                    # Extract key information from the example document
                    doc_text = example.get("document", "")
                    example_info = self._parse_example_document(doc_text)
                    
                    if example_info.get("question"):
                        rag_section += f"\n- Question: {example_info['question']}"
                    if example_info.get("response"):
                        rag_section += f"\n- Response: {example_info['response']}"
                    if example_info.get("status"):
                        rag_section += f"\n- Status: {example_info['status']}"
                    if example_info.get("reasoning"):
                        rag_section += f"\n- Reasoning: {example_info['reasoning']}"
        
        # Add relevant patterns
        if patterns:
            rag_section += "\n\nRELEVANT RESPONSE PATTERNS:"
            
            for i, pattern in enumerate(patterns[:2], 1):  # Limit to top 2 patterns
                metadata = pattern.get("metadata", {})
                pattern_type = metadata.get("pattern_type", "Unknown")
                relevance = pattern.get("relevance_score", 0)
                
                if relevance > 0.4:  # Include moderately relevant patterns
                    rag_section += f"\n\nPattern {i}: {pattern_type.replace('_', ' ').title()}"
                    rag_section += f" (Relevance: {relevance:.2f})"
                    
                    # Extract pattern information
                    doc_text = pattern.get("document", "")
                    if "Keywords:" in doc_text:
                        keywords_line = [line for line in doc_text.split('\n') if line.startswith("Keywords:")][0]
                        rag_section += f"\n- {keywords_line}"
                    
                    if "Example Responses:" in doc_text:
                        examples_line = [line for line in doc_text.split('\n') if line.startswith("Example Responses:")][0]
                        rag_section += f"\n- {examples_line}"
        
        # Add guidance on using the knowledge base
        rag_section += """

KNOWLEDGE BASE USAGE INSTRUCTIONS:
- Use the above examples as reference for response quality and style
- Follow similar reasoning patterns when applicable
- Maintain consistency with established response formats
- Learn from the confidence levels and status determinations shown
- Adapt the examples to your specific validation scenario
- Ensure your response quality matches or exceeds the knowledge base examples"""
        
        return rag_section
    
    def _parse_example_document(self, doc_text: str) -> Dict[str, str]:
        """Parse an example document to extract structured information."""
        info = {}
        
        lines = doc_text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith("Question:"):
                info["question"] = line[9:].strip()
            elif line.startswith("Response:"):
                info["response"] = line[9:].strip()
            elif line.startswith("Status:"):
                info["status"] = line[7:].strip()
            elif line.startswith("Reasoning:"):
                info["reasoning"] = line[10:].strip()
            elif line.startswith("Relevant Sections:"):
                info["relevant_sections"] = line[18:].strip()
        
        return info
