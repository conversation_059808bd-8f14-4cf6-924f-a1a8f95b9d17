#!/usr/bin/env python3
"""
Performance Diagnostic Script
Helps identify where the real bottlenecks are in the validation API
"""

import asyncio
import time
import aiohttp
import json

# Test configuration
API_BASE_URL = "http://localhost:8000"
BEARER_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTczNzgwNzE5Nn0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"

# Test payload
TEST_PAYLOAD = {
    "report_id": "test_report_001",
    "enable_client_filtering": True,
    "order_details_params": {
        "bearer_token": BEARER_TOKEN,
        "order_id": "12345"
    },
    "validation_options": {
        "include_low_confidence": True,
        "min_confidence_threshold": 0.3
    }
}

async def run_diagnostic_test():
    """Run diagnostic test to identify bottlenecks."""
    
    print("🔍 Performance Diagnostic Test")
    print("=" * 50)
    print("This test will help identify where the time is being spent.")
    print()
    
    async with aiohttp.ClientSession() as session:
        print("📡 Sending validation request...")
        print("Watch the server logs for timing information:")
        print("  - 🔧 SKIP_RAG_FOR_SPEED setting")
        print("  - ⚡ RAG timing messages")
        print("  - 🤖 LLM timing messages")
        print("  - 📋/📄/🔍 Cache hit messages")
        print()
        
        start_time = time.time()
        
        try:
            async with session.post(
                f"{API_BASE_URL}/validate",
                json=TEST_PAYLOAD,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    print("✅ Request completed!")
                    print(f"⏱️  Total time: {duration:.2f} seconds")
                    print(f"📊 Questions processed: {result.get('processed_questions', 0)}")
                    print(f"📊 Questions skipped: {result.get('skipped_questions', 0)}")
                    
                    print("\n🔍 DIAGNOSTIC ANALYSIS:")
                    print("-" * 30)
                    
                    if duration > 60:
                        print("🔴 VERY SLOW (>60s) - Major bottleneck detected")
                        print("   Likely causes:")
                        print("   - LLM API calls taking too long")
                        print("   - Network connectivity issues")
                        print("   - Large XML content processing")
                    elif duration > 30:
                        print("🟡 SLOW (30-60s) - Optimization needed")
                        print("   Likely causes:")
                        print("   - Cache misses (first run)")
                        print("   - RAG retrieval if enabled")
                        print("   - LLM processing time")
                    elif duration > 10:
                        print("🟢 MODERATE (10-30s) - Some optimizations working")
                        print("   Possible improvements:")
                        print("   - Enable more aggressive caching")
                        print("   - Skip RAG for speed")
                    else:
                        print("🟢 FAST (<10s) - Optimizations working well!")
                    
                    print("\n📋 NEXT STEPS:")
                    print("1. Check server logs for detailed timing breakdown")
                    print("2. Look for cache hit/miss messages")
                    print("3. Note RAG and LLM processing times")
                    print("4. Run test again to see cache effectiveness")
                    
                    return duration
                    
                else:
                    print(f"❌ API Error: {response.status}")
                    error_text = await response.text()
                    print(f"Error details: {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ Exception: {e}")
            return None

async def run_cache_test():
    """Test cache effectiveness by running the same request twice."""
    
    print("\n🔄 Cache Effectiveness Test")
    print("=" * 40)
    
    async with aiohttp.ClientSession() as session:
        # First run
        print("🔄 First run (cold cache)...")
        start_time = time.time()
        
        try:
            async with session.post(
                f"{API_BASE_URL}/validate",
                json=TEST_PAYLOAD,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    await response.json()
                    first_duration = time.time() - start_time
                    print(f"   ⏱️  Time: {first_duration:.2f} seconds")
                else:
                    print(f"   ❌ Error: {response.status}")
                    return
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return
        
        # Wait a moment
        await asyncio.sleep(1)
        
        # Second run
        print("🔄 Second run (warm cache)...")
        start_time = time.time()
        
        try:
            async with session.post(
                f"{API_BASE_URL}/validate",
                json=TEST_PAYLOAD,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    await response.json()
                    second_duration = time.time() - start_time
                    print(f"   ⏱️  Time: {second_duration:.2f} seconds")
                    
                    # Calculate improvement
                    if first_duration > 0:
                        improvement = ((first_duration - second_duration) / first_duration) * 100
                        print(f"\n📊 Cache Analysis:")
                        print(f"   First run:  {first_duration:.2f}s")
                        print(f"   Second run: {second_duration:.2f}s")
                        print(f"   Improvement: {improvement:.1f}% faster")
                        
                        if improvement > 50:
                            print("   🟢 Excellent cache performance!")
                        elif improvement > 20:
                            print("   🟡 Good cache performance")
                        elif improvement > 0:
                            print("   🟡 Some cache benefit")
                        else:
                            print("   🔴 No cache benefit detected")
                            print("   Check if caching is enabled and working")
                    
                else:
                    print(f"   ❌ Error: {response.status}")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    print("🧪 Validation API Performance Diagnostics")
    print("Make sure the API server is running and you've restarted it after config changes!")
    print()
    
    try:
        # Run diagnostic test
        asyncio.run(run_diagnostic_test())
        
        # Run cache test
        asyncio.run(run_cache_test())
        
        print("\n" + "=" * 60)
        print("🎯 SUMMARY")
        print("=" * 60)
        print("Check the server logs for detailed timing breakdown:")
        print("- RAG timing: Look for '🔍 RAG retrieval completed' or '⚡ RAG skipped'")
        print("- LLM timing: Look for '🤖 LLM processing completed'")
        print("- Cache hits: Look for '📋 Using cached' messages")
        print()
        print("If you're still seeing slow performance:")
        print("1. Verify SKIP_RAG_FOR_SPEED setting is being read correctly")
        print("2. Check if LLM API calls are the bottleneck")
        print("3. Ensure caching is working (second run should be faster)")
        print("4. Consider enabling REDUCE_LLM_CONTEXT for speed")
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
