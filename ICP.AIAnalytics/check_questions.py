#!/usr/bin/env python3
"""
Check what questions are in the permanent question bank.
"""

import sys
import os
import pandas as pd

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_questions():
    """Check what questions are in the permanent question bank."""
    print("Checking Permanent Question Bank")
    print("=" * 50)
    
    excel_path = "./data/Copy of Prompts Checking AI (2).xlsx"
    
    try:
        # Read the Excel file - skip the first row which seems to be a description
        df = pd.read_excel(excel_path, header=1)
        print(f"Excel file loaded successfully")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print()

        # Show first few rows to understand structure
        print("First 5 rows:")
        for i in range(min(5, len(df))):
            print(f"Row {i}: {df.iloc[i].tolist()}")
        print()
        
        # Look for credit-related questions
        credit_questions = []
        for idx, row in df.iterrows():
            question = str(row.get('Validation Details (Questions)', '')).lower()
            if 'credit' in question and ('company' in question or 'size' in question):
                credit_questions.append({
                    'index': idx,
                    'question': row.get('Validation Details (Questions)', ''),
                    'client': row.get('Client', ''),
                    'darwin_ref': row.get('Darwin Reference Section(s)', ''),
                    'expected_outcome': row.get('Expected Outcome', '')
                })
        
        print(f"Found {len(credit_questions)} credit validation questions:")
        for i, q in enumerate(credit_questions, 1):
            print(f"\n{i}. Question: {q['question']}")
            print(f"   Client: {q['client']}")
            print(f"   Darwin Ref: {q['darwin_ref']}")
            print(f"   Expected: {q['expected_outcome']}")
        
        if not credit_questions:
            print("❌ No credit validation questions found!")
            print("\nLet's check all questions that mention 'credit':")
            
            for idx, row in df.iterrows():
                question = str(row.get('Validation Details (Questions)', '')).lower()
                if 'credit' in question:
                    print(f"\n- Question: {row.get('Validation Details (Questions)', '')}")
                    print(f"  Client: {row.get('Client', '')}")
        
        # Also check for questions that mention "large" or "small" with "company"
        print(f"\n" + "=" * 50)
        print("Questions mentioning 'large' or 'small' with 'company':")
        
        size_questions = []
        for idx, row in df.iterrows():
            question = str(row.get('Validation Details (Questions)', '')).lower()
            if ('large' in question or 'small' in question) and 'company' in question:
                size_questions.append({
                    'question': row.get('Validation Details (Questions)', ''),
                    'client': row.get('Client', ''),
                })
        
        for i, q in enumerate(size_questions, 1):
            print(f"\n{i}. Question: {q['question']}")
            print(f"   Client: {q['client']}")
        
        if not size_questions:
            print("❌ No company size validation questions found!")
        
        # Show first 10 questions for context
        print(f"\n" + "=" * 50)
        print("First 10 questions in the bank:")
        for idx in range(min(10, len(df))):
            row = df.iloc[idx]
            question = row.get('Validation Details (Questions)', '')
            client = row.get('Client', '')
            if pd.notna(question) and question.strip():
                print(f"\n{idx + 1}. {question}")
                print(f"   Client: {client}")
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    check_questions()
