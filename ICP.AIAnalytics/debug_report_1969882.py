#!/usr/bin/env python3
"""
Debug report 1969882 to see the actual credit data.
"""

import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor

async def debug_report_1969882():
    """Debug the credit data for report 1969882."""
    print("Debugging Report 1969882 Credit Data")
    print("=" * 50)
    
    file_processor = FileProcessor()
    validation_service = ValidationService()
    
    try:
        # Get the report data
        report_data = await file_processor.get_processed_report_by_report_id("1969882")
        
        if not report_data:
            print("❌ Report 1969882 not found")
            return
        
        print("✅ Report 1969882 found")
        print(f"Available keys: {list(report_data.keys())}")
        print()
        
        # Get the XML content
        xml_data = report_data.get('xml_data', {})
        if not xml_data:
            xml_data = report_data.get('xml_structure', {})
        
        if xml_data:
            full_xml_content = validation_service._dict_to_xml_string(xml_data)
            print(f"XML content length: {len(full_xml_content)} characters")
            
            # Look for PaymentsSection
            payments_start = full_xml_content.find('<PaymentsSection>')
            if payments_start != -1:
                payments_end = full_xml_content.find('</PaymentsSection>', payments_start)
                if payments_end != -1:
                    payments_section = full_xml_content[payments_start:payments_end + 18]
                    print("\n✅ PaymentsSection found:")
                    print("-" * 40)
                    print(payments_section)
                    print("-" * 40)
                    
                    # Extract key credit information
                    import re
                    
                    credit_opinion_match = re.search(r'<CreditOpinion>(.*?)</CreditOpinion>', payments_section)
                    max_credit_match = re.search(r'<MaxCredit>(.*?)</MaxCredit>', payments_section)
                    max_credit_currency_match = re.search(r'<MaxCreditCurrency>(.*?)</MaxCreditCurrency>', payments_section)
                    credit_figure_match = re.search(r'<CreditFigure>(.*?)</CreditFigure>', payments_section)
                    
                    print("\n📊 Credit Information Analysis:")
                    print(f"  CreditOpinion: {credit_opinion_match.group(1) if credit_opinion_match else 'NOT FOUND'}")
                    print(f"  MaxCredit: {max_credit_match.group(1) if max_credit_match else 'NOT FOUND'}")
                    print(f"  MaxCreditCurrency: {max_credit_currency_match.group(1) if max_credit_currency_match else 'NOT FOUND'}")
                    print(f"  CreditFigure: {credit_figure_match.group(1) if credit_figure_match else 'NOT FOUND'}")
                    
                    # Calculate expected GBP conversion
                    if max_credit_match and max_credit_currency_match:
                        max_credit = max_credit_match.group(1)
                        currency = max_credit_currency_match.group(1)
                        
                        print(f"\n💱 Currency Conversion Analysis:")
                        print(f"  Original: {max_credit} {currency}")
                        
                        try:
                            amount = float(max_credit)
                            
                            # Apply fallback conversion rates
                            if currency == "EUR":
                                gbp_amount = amount * 0.85  # EUR to GBP
                                print(f"  Converted: £{gbp_amount:,.2f} GBP (using EUR rate 0.85)")
                            elif currency == "USD":
                                gbp_amount = amount * 0.79  # USD to GBP
                                print(f"  Converted: £{gbp_amount:,.2f} GBP (using USD rate 0.79)")
                            elif currency == "XPF":
                                gbp_amount = amount * 0.007257  # XPF to GBP
                                print(f"  Converted: £{gbp_amount:,.2f} GBP (using XPF rate 0.007257)")
                            elif currency == "BHD":
                                gbp_amount = amount * 2.10  # BHD to GBP
                                print(f"  Converted: £{gbp_amount:,.2f} GBP (using BHD rate 2.10)")
                            else:
                                print(f"  Unknown currency: {currency}")
                                gbp_amount = None
                            
                            if gbp_amount:
                                if gbp_amount < 50000:
                                    credit_size = "SMALL"
                                elif gbp_amount <= 250000:
                                    credit_size = "MEDIUM"
                                else:
                                    credit_size = "LARGE"
                                
                                print(f"  Credit Classification: {credit_size} CREDIT")
                                print(f"  Thresholds: Small < £50,000 | Medium £50,000-£250,000 | Large > £250,000")
                        
                        except ValueError:
                            print(f"  ❌ Could not convert amount to number: {max_credit}")
                    
                    # Check company size
                    company_opinion = credit_opinion_match.group(1) if credit_opinion_match else None
                    if company_opinion:
                        print(f"\n🏢 Company Size Analysis:")
                        print(f"  CreditOpinion: {company_opinion}")
                        print(f"  Company Classification: {company_opinion.upper()} COMPANY")
                        
                        # Check for mismatch
                        if company_opinion and gbp_amount:
                            print(f"\n⚖️ Mismatch Analysis:")
                            company_size = company_opinion.upper()
                            
                            if company_size == "LARGE" and credit_size == "SMALL":
                                print(f"  ❌ MISMATCH: Large company ({company_size}) with small credit ({credit_size})")
                                print(f"  Expected Status: REJECTED")
                            elif company_size == "SMALL" and credit_size == "LARGE":
                                print(f"  ❌ MISMATCH: Small company ({company_size}) with large credit ({credit_size})")
                                print(f"  Expected Status: REJECTED")
                            else:
                                print(f"  ✅ NO MISMATCH: {company_size} company with {credit_size} credit")
                                print(f"  Expected Status: APPROVED")
                
                else:
                    print("❌ PaymentsSection start found but no end tag")
            else:
                print("❌ PaymentsSection not found in XML")
        else:
            print("❌ No XML data found")
        
    except Exception as e:
        print(f"❌ Error debugging report: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_report_1969882())
