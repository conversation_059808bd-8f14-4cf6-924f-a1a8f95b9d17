#!/usr/bin/env python3
"""
Test expiry validation specifically for report 1981692.
"""

import sys
import os
import asyncio
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService

async def test_expiry_validation():
    """Test expiry validation for report 1981692."""
    print("Testing Expiry Validation for Report 1981692")
    print("=" * 50)
    
    # User's payload
    payload = {
        "report_id": "1981692",
        "validation_options": {},
        "enable_client_filtering": True,
        "order_details_params": {
            "csr_id": "91347797",
            "copy": "2",
            "version": "1"
        },
        "direct_client_code": "",
        "bearer_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nU6vPh5iSrAWw27Fax4ks7oZS1jlXfAMSghWkweeyXE"
    }
    
    print(f"Report ID: {payload['report_id']}")
    print()
    
    try:
        validation_service = ValidationService()
        
        # Run validation
        print("Step 1: Running validation...")
        results = await validation_service.validate_report(
            payload["report_id"],
            payload.get("validation_options", {}),
            payload.get("enable_client_filtering", True),
            payload.get("order_details_params", {}),
            payload.get("direct_client_code", ""),
            payload.get("bearer_token", "")
        )
        
        print(f"✅ Validation completed successfully!")
        print(f"Results type: {type(results)}")

        # Debug: Check the type and structure of results
        print("Step 2: Analyzing result structure...")
        if isinstance(results, dict):
            print(f"Results is a dictionary with keys: {list(results.keys())}")
            # Convert dict to list of results
            if 'results' in results:
                results_list = results['results']
            elif 'validation_results' in results:
                results_list = results['validation_results']
            else:
                # Assume the values are the results
                results_list = list(results.values())
        elif isinstance(results, list):
            results_list = results
        else:
            print(f"Unexpected results type: {type(results)}")
            print(f"Results content: {str(results)[:500]}...")
            return

        print(f"Total results: {len(results_list)}")

        if results_list:
            print(f"First result type: {type(results_list[0])}")
            if hasattr(results_list[0], '__dict__'):
                print(f"First result attributes: {list(results_list[0].__dict__.keys())}")
            elif isinstance(results_list[0], dict):
                print(f"First result keys: {list(results_list[0].keys())}")
            else:
                print(f"First result content: {str(results_list[0])[:200]}...")

        # Look for expiry-related results
        print("\nStep 3: Searching for expiry validation results...")
        expiry_results = []

        for i, result in enumerate(results_list):
            try:
                # Handle different result formats
                if hasattr(result, 'question'):
                    question = result.question.lower() if result.question else ''
                    summary = result.summary.lower() if hasattr(result, 'summary') and result.summary else ''
                    reasoning = result.reasoning.lower() if hasattr(result, 'reasoning') and result.reasoning else ''
                elif isinstance(result, dict):
                    question = result.get('question', '').lower()
                    summary = result.get('summary', '').lower()
                    reasoning = result.get('reasoning', '').lower()
                else:
                    # If it's a string, search within the string
                    result_str = str(result).lower()
                    question = result_str
                    summary = result_str
                    reasoning = result_str

                # Check if this is an expiry-related question
                expiry_keywords = ['expiry', 'expired', 'expire', 'registration', 'comment']
                if any(keyword in question for keyword in expiry_keywords):
                    expiry_results.append((i, result))

            except Exception as e:
                print(f"Error processing result {i}: {e}")
                continue
        
        if expiry_results:
            print(f"Found {len(expiry_results)} expiry-related results:")
            print("-" * 60)

            for i, (result_index, result) in enumerate(expiry_results, 1):
                print(f"Expiry Result {i} (index {result_index}):")

                # Handle different result formats
                if hasattr(result, 'question'):
                    print(f"  Question: {result.question}")
                    print(f"  Summary: {getattr(result, 'summary', 'N/A')}")
                    print(f"  Status: {getattr(result, 'status', 'N/A')}")
                    print(f"  Reasoning: {getattr(result, 'reasoning', 'N/A')}")
                    print(f"  Confidence: {getattr(result, 'confidence_score', 'N/A')}")

                    summary_text = (getattr(result, 'summary', '') + ' ' + getattr(result, 'reasoning', '')).lower()
                elif isinstance(result, dict):
                    print(f"  Question: {result.get('question', 'N/A')}")
                    print(f"  Summary: {result.get('summary', 'N/A')}")
                    print(f"  Status: {result.get('status', 'N/A')}")
                    print(f"  Reasoning: {result.get('reasoning', 'N/A')}")
                    print(f"  Confidence: {result.get('confidence_score', 'N/A')}")

                    summary_text = (result.get('summary', '') + ' ' + result.get('reasoning', '')).lower()
                else:
                    print(f"  Raw result: {str(result)[:500]}...")
                    summary_text = str(result).lower()

                # Check for date mentions
                has_date_mention = any(date_word in summary_text for date_word in ['2026', '2025', 'aug', 'august', 'jan', 'january'])
                print(f"  Date mentioned: {'✅' if has_date_mention else '❌'}")

                # Check for expiry status
                has_expired_mention = 'expired' in summary_text
                has_valid_mention = any(word in summary_text for word in ['valid', 'not expired', 'future'])
                print(f"  Mentions expired: {'✅' if has_expired_mention else '❌'}")
                print(f"  Mentions valid: {'✅' if has_valid_mention else '❌'}")
                print()
        else:
            print("❌ No expiry-related results found")
            
            # Show all questions to see what we have
            print("\nAll questions processed:")
            for i, result in enumerate(results_list[:10], 1):  # Show first 10
                if hasattr(result, 'question'):
                    question = result.question
                elif isinstance(result, dict):
                    question = result.get('question', 'N/A')
                else:
                    question = str(result)[:100]
                print(f"  {i}. {question}")
        
        # Expected result analysis
        print("Step 3: Expected vs Actual Analysis:")
        print("-" * 50)
        print("Expected for report 1981692:")
        print("  Registration: Commercial Registration Number 4700020431")
        print("  DateExpired: 14-Aug-2026 (VALID - expires in 374 days)")
        print("  Comments: Hijri dates (not about expiry)")
        print("  Expected Status: APPROVED (no expired registrations)")
        print()
        
        # Check if we got the expected result
        expiry_question_result = None
        for result in expiry_results:
            if 'expired' in result.get('question', '').lower() and 'comment' in result.get('question', '').lower():
                expiry_question_result = result
                break
        
        if expiry_question_result:
            actual_status = expiry_question_result.get('status', '')
            actual_summary = expiry_question_result.get('summary', '')
            
            print("Actual Result Analysis:")
            print(f"  Status: {actual_status}")
            print(f"  Summary: {actual_summary}")
            
            if actual_status == 'approved' and 'not expired' in actual_summary.lower():
                print("  ✅ CORRECT: System correctly identified no expired registrations")
            elif actual_status == 'approved' and 'expired' not in actual_summary.lower():
                print("  ✅ CORRECT: System approved (no issues found)")
            elif 'expired' in actual_summary.lower():
                print("  ❌ INCORRECT: System incorrectly identified expired registration")
                print("  🔧 ISSUE: Registration 14-Aug-2026 is VALID, not expired!")
            else:
                print(f"  ⚠️ UNCLEAR: Need to analyze result further")
        else:
            print("❌ Could not find the specific expiry validation question")
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_expiry_validation())
