{"company_credit_validation_questions": [{"id": "company-credit-mismatch-large-small", "question": "Mark as an issue if a large company is associated with a small credit amount (convert to GBP and compare with thresholds)", "darwin_reference_sections": "(Company) Company Size, Number of Employees, Annual Turnover, (Credit) Max Credit, Credit Limit, (Financial) Credit Information", "expected_outcome": "rejected", "client_specific_type": "All", "description": "Validates that large companies (>1000 employees OR >£10M turnover) do not have small credit amounts (<£50,000 GBP)", "validation_logic": "1. Identify company size from employee count and annual turnover. 2. Extract credit amounts and convert to GBP. 3. Flag as issue if large company has credit <£50,000 GBP.", "business_rationale": "Large companies typically require higher credit limits due to their scale of operations. A small credit limit for a large company may indicate data inconsistency or inadequate credit assessment."}, {"id": "company-credit-mismatch-small-large", "question": "Mark as an issue if a small company is associated with a large credit amount (convert to GBP and compare with thresholds)", "darwin_reference_sections": "(Company) Company Size, Number of Employees, Annual Turnover, (Credit) Max Credit, Credit Limit, (Financial) Credit Information", "expected_outcome": "rejected", "client_specific_type": "All", "description": "Validates that small companies (<50 employees OR <£1M turnover) do not have large credit amounts (>£250,000 GBP)", "validation_logic": "1. Identify company size from employee count and annual turnover. 2. Extract credit amounts and convert to GBP. 3. Flag as issue if small company has credit >£250,000 GBP.", "business_rationale": "Small companies typically have lower credit requirements. A large credit limit for a small company may indicate over-exposure to credit risk or data inconsistency."}, {"id": "company-credit-classification-comprehensive", "question": "Classify the company size and credit amount, then determine if there's a mismatch between company size and credit classification", "darwin_reference_sections": "(Company) Company Size, Number of Employees, Annual Turnover, Company Type, (Credit) Max Credit, Credit Limit, (Financial) Total Income, Credit Information", "expected_outcome": "approved", "client_specific_type": "All", "description": "Comprehensive analysis that classifies both company size and credit amount, then checks for logical consistency between the two classifications", "validation_logic": "1. Classify company as Small/Medium/Large based on employees and turnover. 2. Classify credit as Small/Medium/Large based on GBP thresholds. 3. Check for extreme mismatches (Large+Small, Small+Large).", "business_rationale": "Ensures that credit limits are appropriately aligned with company size and capacity, supporting sound credit risk management practices."}, {"id": "company-credit-currency-conversion", "question": "Verify that credit amounts are properly converted to GBP for company size comparison using available exchange rates", "darwin_reference_sections": "(Credit) Max Credit, Credit Limit, Exchange Rates, (Financial) Credit Information, Currency", "expected_outcome": "approved", "client_specific_type": "All", "description": "Ensures that credit amounts in foreign currencies are correctly converted to GBP before applying company size validation rules", "validation_logic": "1. Extract credit amounts and their currencies. 2. Find applicable exchange rates in the report. 3. Convert to GBP and verify conversion accuracy. 4. Apply GBP thresholds for classification.", "business_rationale": "Accurate currency conversion is essential for consistent credit risk assessment across different currencies and markets."}], "implementation_notes": {"credit_size_thresholds_gbp": {"small_credit": "< £50,000", "medium_credit": "£50,000 - £250,000", "large_credit": "> £250,000"}, "company_size_thresholds": {"small_company": "< 50 employees OR annual turnover < £1M GBP", "medium_company": "50-1000 employees OR annual turnover £1M-£10M GBP", "large_company": "> 1000 employees OR annual turnover > £10M GBP"}, "mismatch_rules": {"large_company_small_credit": "ISSUE - Flag as rejected", "small_company_large_credit": "ISSUE - Flag as rejected", "medium_company_extreme_credit": "POTENTIAL ISSUE - Manual review recommended"}, "currency_handling": {"primary_currency": "GBP", "conversion_required": "All non-GBP amounts must be converted using available exchange rates", "fallback_behavior": "If exchange rates unavailable, state 'Cannot classify - exchange rate not available'"}}, "integration_instructions": {"question_bank_update": "Add these questions to the permanent question bank Excel file", "validation_service": "Already implemented in validation_service.py with enhanced prompt templates", "testing": "Use test_company_credit_validation.py to verify functionality", "api_endpoint": "Questions will be processed through existing /validate endpoint"}}