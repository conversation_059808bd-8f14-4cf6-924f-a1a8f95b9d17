{"validation_id": "94dc3979-011d-4ed4-9008-c08a5f69bb7a", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181305", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "114d073d-89ed-4d06-9de2-795133c84493", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "a718f06c-ad5c-441a-99dc-ce857badf5e9", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "852e6b82-908e-44af-b88d-2cf2141a705f", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is absent; cannot verify comments and payment details compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/ClientSpecificCommentsSection/Comments", "Report/PaymentSection/PaymentRequired"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bdfe9a7e-e7b8-4f1b-aca3-09ec92235993", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name field is missing; comments in both sections are present, violating the rule.", "confidence_score": 0.98, "relevant_sections": ["Order Details/Client Name", "Payments/Credit Opinion Notes", "Special Notes/Text for Specific Clients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e7b38f29-d0ed-4f5f-9ac1-233cd294f8b9", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit '50.00' is less than Total Income '100.00', compliant.", "confidence_score": 0.98, "relevant_sections": ["RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1da44477-1a91-418d-bf26-a605d269d139", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials and supplies in 'Direct Costs', compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5a6f1aed-64d2-4c29-a209-23fa05bc1e67", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'RE test 2' does not match requested name 'test', Client Specific Comments missing.", "confidence_score": 0.9, "relevant_sections": ["HeaderSection/CompanyName", "HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7ee9dbec-b238-46ee-aafe-79c8c8ad18ae", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address 'test' matches active address, no comments needed.", "confidence_score": 0.98, "relevant_sections": ["AddressesSection/Addresses/Address", "RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4d630f52-968c-4f6d-b592-bf93e4316a42", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit currencies 'EUR', 'USD', 'GBP' comply; note confirms not TURKEXIM.", "confidence_score": 0.98, "relevant_sections": ["SpecialNote/MaxCredit/Currency", "SpecialNote/MaxCredit/Note"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f502c0a0-1c34-4a57-8f64-8e3d52ee674a", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License number not recorded in legal section or special notes, action needed.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/RegistrationNumbers", "RelatedEntitiesLegalStatusSection/RegistrationNumbers"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "05512016-618e-4813-8cf1-d4235d9422d0", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in legal status descriptions.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/LegalStatus", "RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f6d93d01-9673-4f47-9553-92ec3bdf955d", "question_number": 12, "question": "Spelling Check", "summary": "Spelling error found: 'atest' should be 'test'.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder/Name"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4589362a-4ef7-4814-8994-64ee9912a9a6", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements or payment references found, compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Significant Changes", "Payments"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "74d971f6-b81b-4a89-bab4-3d824fc7d83f", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Trade risk assessment is 'High', violates no credit requirement.", "confidence_score": 0.98, "relevant_sections": ["Report/TradeRiskAssessment/RiskLevel", "Report/CreditOpinionSection/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d4ad74ff-804f-48c5-8497-f39a70fde5b1", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/LegalStatus", "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c9a4feef-f435-407e-a03b-fcb6e47aae55", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-related info; no relevant data found.", "confidence_score": 0.9, "relevant_sections": ["General Info", "Subject Activities", "Payments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9aca5e7a-6207-4eaa-a592-336e532ef584", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Registration number 'Other Registration Number1' has incorrect text added, violates export rule.", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "900c724b-642e-40ed-971e-7a02432f1ac4", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both absent, cannot verify compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/ProfitAndLoss/Income", "Report/ProfitAndLoss/TotalIncome"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1e2f302b-b4b5-48cc-a6c0-52da1dcf7a36", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Credit opinion currency is 'ANG', violates requested currency condition.", "confidence_score": 0.95, "relevant_sections": ["LegalStatusSection/Capital/NotGiven", "RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/Currency"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d68daa5b-af26-42ab-a6a6-998bf1446343", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not applicable; no comment link provided.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection", "Report/RelatedEntitiesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7494470c-f672-45ef-9cdb-ffcee79d411d", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status 'Active' violates dormant/ceased trading rule.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/CompanyStatus", "LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "10f8d7b4-7b27-4004-8f27-3e8df1d4d7c0", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number '3456789' not listed as a Branch, violates compliance rule.", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RegistrationNumbers/RegistrationNumberValue"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f180d3e7-e4e8-42a7-a8b5-c547760d0266", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number '32433333' expired but has no comment, violates compliance.", "confidence_score": 0.95, "relevant_sections": ["LegalStatusSection/RegistrationNumbers", "RelatedEntitiesLegalStatusSection/RegistrationNumbers"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9d10960d-2e07-488a-becf-dad614cfb927", "question_number": 24, "question": "Small with large amount", "summary": "Shareholder 'atest' has 0 shares, violating small with large amount rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Shareholders/Shareholder/Name", "Report/LegalStatusSection/Shareholders/Shareholder/NoOfShares"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5db1122b-909f-4bc7-b522-5ba77485bb7b", "question_number": 25, "question": "Large with small amount", "summary": "Shareholder 'atest' holds 0 shares, violating large with small amount rule.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder/NoOfShares"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d2acc652-9e46-46bd-bf92-3b23c0306633", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not present for compliance check.", "confidence_score": 0.85, "relevant_sections": ["Report/Personnel/Position", "Report/Personnel/PositionClassifications"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4a89bed5-2f19-4fb4-9353-58873a4d936d", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Research Type not specified as 'Negative', violates compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ff192713-8d37-48d9-93cb-1ef236d4d63b", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline', Person Interviewed section missing, violates No Contact rule.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotes/PersonInterviewed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a845c0c2-acdb-43e0-89e7-ab8d2d28ce7e", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type is missing; cannot verify decline compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection", "Report/RelatedEntitiesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "dea229f4-728f-47c7-a8a5-2cf993cd466f", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is blank, but comments and Person Interviewed are present.", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RegistrationNumbers/Comments", "Report/RelatedEntitiesSection/RelatedEntity/PersonInterviewed/Name"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ead8e4a1-ddbe-4011-b331-1d3a7214f240", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; missing relevant tag.", "confidence_score": 0.98, "relevant_sections": ["Report/Sanction"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ff528d19-6c0c-42d2-a8f7-496fa28d065a", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanctions present, Trade Risk 'High', Credit Opinion blank, compliant.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/Sanctions", "TradeRiskAssessmentSection/TradeRiskAssessment", "CreditOpinionSection/CreditOpinion"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "54a2257d-75ae-4e3d-93cd-38f650e65305", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "fd915852-2dd6-4832-986d-7ef1e83b50d1", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in RelatedEntitiesLegalStatusSection.", "confidence_score": 0.95, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9ddd2d2c-48d0-4ad7-a5fc-73ed5559099d", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type is 'Select' but no contact details provided in address section.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8b3e9b81-b697-4c70-bee4-b14eb9b6c33a", "question_number": 36, "question": "Capital missing currency", "summary": "Capital field is not provided; violates currency requirement.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/NotGiven"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "29f31b67-ffe6-4bc2-85e2-d90460593214", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder 'atest' has no nationality, violates related entities requirement.", "confidence_score": 0.9, "relevant_sections": ["LegalStatusSection/Shareholders/Shareholder[1]/Name", "LegalStatusSection/Shareholders/Shareholder[1]/Nationality"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b9a8c49b-89b9-4d36-a20f-878bf5816aa2", "question_number": 38, "question": "Calculate credit opinion", "summary": "Credit opinion calculation cannot be verified due to missing payment data.", "confidence_score": 0.85, "relevant_sections": ["LegalStatusSection/Shareholders", "RelatedEntitiesSection/RelatedEntity"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1786e0c8-a629-4323-96a6-3d87b87463fa", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'RE test 2' does not match shareholder name 'test 2', violates compliance.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/Shareholders/Shareholder/Name"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5ed33e8b-2565-4560-a004-98c73aebb903", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is filled with 'test' and 'Test0.3', compliant with validation rule.", "confidence_score": 0.98, "relevant_sections": ["AddressesSection/Addresses/Address/Town", "RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3dc43b1b-ab15-456e-bde5-516fb2da0cc3", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is 'Active', complies with credit granting rules.", "confidence_score": 0.98, "relevant_sections": ["LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 18:43:45.234420", "processing_time": 79.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}