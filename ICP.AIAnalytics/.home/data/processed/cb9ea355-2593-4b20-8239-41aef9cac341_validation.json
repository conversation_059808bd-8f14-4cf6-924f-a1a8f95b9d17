{"validation_id": "cb9ea355-2593-4b20-8239-41aef9cac341", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "178980", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "771774fa-f5b3-4b96-b150-7f9b2d6cac44", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "81d15705-71ae-42ee-bc54-d070e8ad5397", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "e1d71509-cdc6-4eaf-a4f7-fbcd22c2b534", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present but comments and payment details missing", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "75d7dadc-d100-40dd-9aed-e94d2bfaa208", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing; no notes found in comments or payment sections.", "confidence_score": 0.95, "relevant_sections": ["Order Details", "Client Specific Comments", "Payment Section"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8648b715-a3a4-420f-8be3-fcdd01772273", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income due to missing data", "confidence_score": 0.9, "relevant_sections": ["Financial"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4250725a-dc0f-442d-b089-27a0cd9ac819", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Cannot verify Expenses for raw materials in Direct Costs", "confidence_score": 0.7, "relevant_sections": ["Financial/Profit and loss"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "03c5ac56-7a62-4244-883a-00153483a98a", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'MOGZAURI PLUS T/A MPLUS' matches requested name 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a98b4e35-7bdd-4e41-a62b-51a9c29f5286", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address not found; Client Specific Comments missing", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "f885ca6f-f3cd-4759-b416-2f8d9f5d72b5", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not in special note, violates rule for EUR/USD/GBP", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "864c074c-41d3-4d82-a531-315caf6a4873", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/License numbers missing in LegalStatus/SpecialNotes", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "10507327-4de0-4b00-8d89-5f4f67f4ef26", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language in Payments activities special note", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4948c29f-f34a-46dd-af2a-c61956272f3b", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b6309f7d-9166-43f5-a79e-ee76f52a8e03", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No significant changes or payments sections found in XML", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d2e94432-9a01-4f5e-9878-832e3dd6fe48", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1768. Please try again in 3.536s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5b6684fc-1904-4377-baf5-97d9aa238e69", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Adverse announcements not found in Change Classifications or Descriptions", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "0218c7fa-ab60-4392-94ed-d5865c5be33b", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section missing sector info for 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/ICPLegalGroup"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2f8ade32-bd8c-42c7-9cde-7ce184c6c45d", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No export-related text found in Activities section.", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c4f5b2ed-f98c-40c5-adab-a4567c9873ca", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1752. Please try again in 3.504s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "98b9d033-fcca-4dba-8933-4d31dd78d9e0", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1723. Please try again in 3.446s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "93c9e1f1-2164-4817-bd72-36d96049ee6e", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1720. Please try again in 3.44s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c6e5ded2-c0da-4a25-9885-a0415fe3aa9d", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29664, Requested 1715. Please try again in 2.758s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "aa994ce7-8ee7-4a21-8a42-e7b31c97e9a9", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29538, Requested 1796. Please try again in 2.668s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c6ecac4a-c7ae-4321-af59-c3f1f0852abd", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29612, Requested 1745. Please try again in 2.714s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c2ef7829-a52b-494d-928c-0209a95e8f39", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29805, Requested 1649. Please try again in 2.908s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f8c41934-a57a-4f67-8936-d17ddaec2043", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29541, Requested 1649. Please try again in 2.38s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c1e98e0e-4ed1-4d65-a9b2-f8511bf3f84c", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29636, Requested 1731. Please try again in 2.734s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "79fad5e7-cbdd-46db-bf14-a6b27a18170b", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29454, Requested 1780. Please try again in 2.468s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "7ee35d55-4fc0-49c3-9996-848c8c2f1892", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29566, Requested 1748. Please try again in 2.628s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "29a7b32d-a45d-4824-8908-c4c056438282", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29515, Requested 1798. Please try again in 2.626s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "a11d08fb-01f5-4832-9707-3d59ecef9675", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29555, Requested 1791. Please try again in 2.692s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e6de942e-c88c-4780-b036-2e0237e3b99f", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1724. Please try again in 3.448s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "0d711159-2b6d-47a0-a14c-293edb5ce7b5", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1764. Please try again in 3.528s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "814dda3c-52d4-40a8-a425-7cd145b4d8b8", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "b0ea72ff-ab4d-41e3-b9ff-e815ee828863", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1735. Please try again in 3.47s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d2fbd9af-1c51-4808-8c88-ef0e00a9742a", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type 'Select' but no contact details found", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2c2b1990-21fe-472c-9dc2-a81b8ea48437", "question_number": 36, "question": "Capital missing currency", "summary": "Currency missing in LegalStatusSection/Capital, field is blank", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "faa8a8bc-e076-4dcb-bd1f-819c7667e23f", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder not populated for group member 'Government Ministry'", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "83f11638-2638-468b-abb0-1c9b11fa4eb7", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1709. Please try again in 3.418s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b9ee8a08-c140-4b7b-98ed-033acd304426", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1734. Please try again in 3.468s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "2ae9e086-6f50-400e-bc56-ab2dc132a5e5", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1729. Please try again in 3.458s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "6533e475-50c1-43f3-add0-691d5325e945", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Legal status 'Government Ministry' known, no credit issue", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/LegalStatus"], "status": "rejected", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 18:34:27.425337", "processing_time": 36.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "EHTR", "rag_enabled": true, "permanent_question_bank_used": true}