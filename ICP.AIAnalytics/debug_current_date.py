#!/usr/bin/env python3
"""
Debug current date in validation prompts.
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService

def debug_current_date():
    """Debug current date values."""
    print("Debugging Current Date in Validation")
    print("=" * 40)
    
    validation_service = ValidationService()
    
    # Test current date methods
    current_date_iso = validation_service.get_current_date()
    current_date_uk = validation_service.get_current_date_formatted("uk")
    current_date_display = validation_service.get_current_date_formatted("display")
    
    print(f"Current date (ISO): {current_date_iso}")
    print(f"Current date (UK): {current_date_uk}")
    print(f"Current date (Display): {current_date_display}")
    print()
    
    # Test date comparison
    test_dates = [
        "14-Aug-2026",
        "12-Jan-2025",
        "05-Aug-2025",
        "04-Aug-2025",
        "06-Aug-2025"
    ]
    
    print("Date Comparison Tests:")
    print("-" * 30)
    
    for test_date in test_dates:
        is_expired, result = validation_service.is_date_expired(test_date)
        print(f"{test_date:12} → {'EXPIRED' if is_expired else 'VALID':8} ({result})")
    
    print()
    
    # Show what the prompt would look like
    print("Prompt Current Date Reference:")
    print("-" * 40)
    print(f"CRITICAL: Any date on or before {current_date_iso} is EXPIRED!")
    print()
    
    # Manual date comparison for 14-Aug-2026
    print("Manual Analysis for 14-Aug-2026:")
    print("-" * 40)
    
    from datetime import datetime
    current = datetime.now().date()
    target = datetime.strptime("14-Aug-2026", "%d-%b-%Y").date()
    
    print(f"Current date: {current}")
    print(f"Target date: {target}")
    print(f"Target > Current: {target > current}")
    print(f"Days difference: {(target - current).days}")
    
    if target > current:
        print("✅ 14-Aug-2026 is VALID (future date)")
    else:
        print("❌ 14-Aug-2026 is EXPIRED (past date)")

if __name__ == "__main__":
    debug_current_date()
