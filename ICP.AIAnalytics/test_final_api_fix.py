#!/usr/bin/env python3
"""
Final test to verify the API fix with the actual payload.
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService

async def test_final_api_fix():
    """Test the final API fix with the actual payload."""
    print("Final API Fix Test")
    print("=" * 40)
    
    # The payload from the user
    payload = {
        "report_id": "1984940",
        "validation_options": {},
        "enable_client_filtering": True,
        "order_details_params": {
            "csr_id": "91350959",
            "copy": "1",
            "version": "1"
        },
        "direct_client_code": "",
        "bearer_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YY7yPjv4oH-p4sZzfK2oINuGkMa3pYxzCef86UFfCEQ"
    }
    
    print(f"Testing with Report ID: {payload['report_id']}")
    print()
    
    validation_service = ValidationService()
    
    try:
        print("Running validation API...")
        validation_result = await validation_service.validate_report(
            report_id=payload['report_id'],
            validation_options=payload['validation_options'],
            enable_client_filtering=payload['enable_client_filtering'],
            order_details_params=payload['order_details_params'],
            direct_client_code=payload['direct_client_code'],
            bearer_token=payload['bearer_token']
        )
        
        print(f"✅ Validation completed successfully")
        print(f"Total questions: {validation_result['total_questions']}")
        print(f"Processed questions: {validation_result['processed_questions']}")
        print()
        
        # Look for temporal validation results
        temporal_results = []
        for result in validation_result['results']:
            question = result.get('question', '').lower()
            if any(word in question for word in ['registration', 'expiry', 'expired', 'date']):
                temporal_results.append(result)
        
        if temporal_results:
            print(f"Found {len(temporal_results)} temporal validation results:")
            for i, result in enumerate(temporal_results, 1):
                print(f"\nTemporal Result {i}:")
                print(f"  Question: {result.get('question', 'N/A')}")
                print(f"  Status: {result.get('status', 'N/A')}")
                print(f"  Summary: {result.get('summary', 'N/A')}")
                print(f"  Confidence: {result.get('confidence_score', 'N/A')}")
                
                # Check if this is the expiry validation question
                if 'expired' in result.get('question', '').lower() and 'comment' in result.get('question', '').lower():
                    print(f"  🎯 This is the main expiry validation question!")
                    if result.get('status') == 'approved' and 'expired' in result.get('summary', '').lower():
                        print(f"  ✅ CORRECTLY detected expired registration with comment!")
                    elif result.get('status') == 'manual_intervention_needed' and 'no registration' in result.get('summary', '').lower():
                        print(f"  ❌ INCORRECTLY says no registration numbers found")
                    else:
                        print(f"  ⚠️  Unexpected result for expiry validation")
        else:
            print("❌ No temporal validation results found")
        
        print()
        print("=" * 40)
        print("SUMMARY:")
        print("The API should now correctly detect the expired registration number")
        print("(322327812993, expired 12-Jan-2025) and approve it because there's a comment.")
        
    except Exception as e:
        print(f"❌ Error running validation: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_final_api_fix())
