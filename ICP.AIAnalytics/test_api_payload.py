#!/usr/bin/env python3
"""
Test script to validate the API payload and check registration number detection.
"""

import sys
import os
import asyncio
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor

async def test_api_payload():
    """Test the API payload and check registration number detection."""
    print("Testing API Payload for Registration Number Detection")
    print("=" * 60)
    
    # The payload from the user
    payload = {
        "report_id": "1984940",
        "validation_options": {},
        "enable_client_filtering": True,
        "order_details_params": {
            "csr_id": "91350959",
            "copy": "1",
            "version": "1"
        },
        "direct_client_code": "",
        "bearer_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YY7yPjv4oH-p4sZzfK2oINuGkMa3pYxzCef86UFfCEQ"
    }
    
    print(f"Report ID: {payload['report_id']}")
    print(f"Client filtering enabled: {payload['enable_client_filtering']}")
    print()
    
    # Initialize services
    validation_service = ValidationService()
    file_processor = FileProcessor()
    
    try:
        print("Step 1: Loading report data...")
        # Load the report data using the same method as the validation service
        report_data = await file_processor.get_processed_report_by_report_id(payload['report_id'])
        
        if not report_data:
            print(f"❌ No report found for report_id: {payload['report_id']}")
            return
        
        print(f"✅ Report data loaded successfully")
        print(f"Report data keys: {list(report_data.keys())}")
        print()
        
        print("Step 2: Examining XML structure for registration numbers...")
        
        # Get the XML data
        xml_data = report_data.get('xml_data', {})
        if not xml_data:
            xml_data = report_data.get('xml_structure', {})
        
        if not xml_data:
            print("❌ No XML data found in report")
            return
        
        print(f"XML data root keys: {list(xml_data.keys())}")
        
        # Search for registration numbers in various paths
        registration_paths = [
            'Report/LegalStatusSection/RegistrationNumbers',
            'Report/LegalStatusSection',
            'Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers',
            'LegalStatusSection/RegistrationNumbers',
            'LegalStatusSection',
            'RegistrationNumbers'
        ]
        
        print("\nSearching for registration numbers in XML paths:")
        found_registrations = []
        
        for path in registration_paths:
            path_parts = path.split('/')
            current_data = xml_data
            path_exists = True
            
            for part in path_parts:
                if isinstance(current_data, dict) and part in current_data:
                    current_data = current_data[part]
                else:
                    path_exists = False
                    break
            
            if path_exists and current_data:
                print(f"✅ Found data at path: {path}")
                print(f"   Data type: {type(current_data)}")
                print(f"   Data: {current_data}")
                found_registrations.append((path, current_data))
            else:
                print(f"❌ No data at path: {path}")
        
        print()
        
        if found_registrations:
            print("Step 3: Analyzing found registration data...")
            for path, data in found_registrations:
                print(f"\nPath: {path}")
                if isinstance(data, list):
                    print(f"Found {len(data)} registration entries:")
                    for i, reg in enumerate(data):
                        print(f"  Registration {i+1}: {reg}")
                elif isinstance(data, dict):
                    print(f"Found registration data: {data}")
                else:
                    print(f"Found data (type {type(data)}): {data}")
        else:
            print("❌ No registration numbers found in any expected paths")
            print("\nLet's examine the full XML structure:")
            print(json.dumps(xml_data, indent=2)[:2000] + "..." if len(str(xml_data)) > 2000 else json.dumps(xml_data, indent=2))
        
        print()
        
        print("Step 4: Testing direct registration validation...")
        # Test the direct registration validation method
        direct_result = validation_service.validate_registration_number_expiry(xml_data)
        print(f"Direct validation result:")
        print(f"  Status: {direct_result['status']}")
        print(f"  Summary: {direct_result['summary']}")
        print(f"  Details: {direct_result['details']}")
        
        print()
        
        print("Step 5: Running full validation API...")
        # Run the full validation
        try:
            validation_result = await validation_service.validate_report(
                report_id=payload['report_id'],
                validation_options=payload['validation_options'],
                enable_client_filtering=payload['enable_client_filtering'],
                order_details_params=payload['order_details_params'],
                direct_client_code=payload['direct_client_code'],
                bearer_token=payload['bearer_token']
            )
            
            print(f"✅ Validation completed successfully")
            print(f"Total questions: {validation_result['total_questions']}")
            print(f"Processed questions: {validation_result['processed_questions']}")
            print(f"Skipped questions: {validation_result['skipped_questions']}")
            
            # Look for temporal validation results
            temporal_results = []
            for result in validation_result['results']:
                question = result.get('question', '').lower()
                if any(word in question for word in ['registration', 'expiry', 'expired', 'date']):
                    temporal_results.append(result)
            
            if temporal_results:
                print(f"\nFound {len(temporal_results)} temporal validation results:")
                for i, result in enumerate(temporal_results, 1):
                    print(f"\nTemporal Result {i}:")
                    print(f"  Question: {result.get('question', 'N/A')}")
                    print(f"  Status: {result.get('status', 'N/A')}")
                    print(f"  Summary: {result.get('summary', 'N/A')}")
                    print(f"  Confidence: {result.get('confidence_score', 'N/A')}")
            else:
                print("\n❌ No temporal validation results found")
                print("Available questions:")
                for i, result in enumerate(validation_result['results'][:5], 1):
                    print(f"  {i}. {result.get('question', 'N/A')[:100]}...")
        
        except Exception as e:
            print(f"❌ Error running validation: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
    
    except Exception as e:
        print(f"❌ Error in test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_api_payload())
