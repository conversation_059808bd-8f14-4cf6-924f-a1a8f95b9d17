#!/usr/bin/env python3
"""
Debug script to see exactly what prompt is being sent to the LLM.
"""

import sys
import os
import asyncio
import json
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.models.schemas import Question

async def debug_llm_prompt():
    """Debug the exact prompt being sent to the LLM."""
    print("Debugging LLM Prompt for Credit Validation")
    print("=" * 50)
    
    # Create test report data
    test_report_data = {
        "file_id": "test-credit-report",
        "report_id": "1981352",
        "xml_data": {
            "Report": {
                "HeaderSection": {
                    "Date": "05-Aug-2025",
                    "CompanyName": "Test Company Ltd",
                    "EmployeeCount": "150",
                    "AnnualTurnover": "5000000"
                },
                "PaymentsSection": {
                    "MaxCredit": "1000000",
                    "MaxCreditCurrency": "XPF",
                    "CreditLimit": "1000000",
                    "CreditFigure": "1000000",
                    "CreditFigureCurrency": "XPF"
                },
                "ExchangeRatesSection": {
                    "ExchangeRate": [
                        {
                            "FromCurrency": "XPF",
                            "ToCurrency": "GBP",
                            "Rate": "0.0075"
                        }
                    ]
                }
            }
        },
        "xml_structure": {
            "Report": {
                "HeaderSection": {
                    "Date": "05-Aug-2025",
                    "CompanyName": "Test Company Ltd",
                    "EmployeeCount": "150",
                    "AnnualTurnover": "5000000"
                },
                "PaymentsSection": {
                    "MaxCredit": "1000000",
                    "MaxCreditCurrency": "XPF",
                    "CreditLimit": "1000000",
                    "CreditFigure": "1000000",
                    "CreditFigureCurrency": "XPF"
                },
                "ExchangeRatesSection": {
                    "ExchangeRate": [
                        {
                            "FromCurrency": "XPF",
                            "ToCurrency": "GBP",
                            "Rate": "0.0075"
                        }
                    ]
                }
            }
        },
        "processing_timestamp": datetime.now().isoformat()
    }
    
    # Create test questions
    test_questions = [
        Question(
            id="test-credit-small-large",
            question="Mark as an issue if a small company is associated with a large credit amount (convert to GBP and compare with thresholds).",
            category="company_credit_validation",
            client_code=None,
            darwin_reference_sections="(Company) Company Size, Number of Employees, Annual Turnover, (Credit) Max Credit, Credit Limit, (Financial) Credit Information",
            expected_outcome="rejected",
            client_specific_type="All"
        )
    ]
    
    # Initialize validation service
    validation_service = ValidationService()
    
    try:
        print("Step 1: Building holistic validation prompt...")
        
        # Get the XML content that would be sent to LLM
        xml_data = test_report_data.get('xml_data', {})
        full_xml_content = validation_service._dict_to_xml_string(xml_data)
        full_xml_content = validation_service._optimize_xml_content_for_speed(full_xml_content)
        
        print("XML Content being sent to LLM:")
        print("-" * 40)
        print(full_xml_content)
        print("-" * 40)
        print()
        
        # Build the holistic prompt
        holistic_prompt = validation_service._build_holistic_validation_prompt(
            test_questions, full_xml_content, {}, None
        )
        
        print("Step 2: Holistic Validation Prompt:")
        print("=" * 60)
        print(holistic_prompt)
        print("=" * 60)
        print()
        
        # Check if currency conversion instructions are in the prompt
        prompt_lower = holistic_prompt.lower()
        has_conversion_instructions = "convert to gbp" in prompt_lower or "currency conversion" in prompt_lower
        has_step_by_step = "step 1:" in prompt_lower and "step 2:" in prompt_lower
        has_xpf_example = "xpf" in prompt_lower
        has_calculation_example = "1,000,000" in prompt_lower and "7,500" in prompt_lower
        
        print("Step 3: Prompt Analysis:")
        print(f"  Has currency conversion instructions: {'✅' if has_conversion_instructions else '❌'}")
        print(f"  Has step-by-step process: {'✅' if has_step_by_step else '❌'}")
        print(f"  Has XPF example: {'✅' if has_xpf_example else '❌'}")
        print(f"  Has calculation example: {'✅' if has_calculation_example else '❌'}")
        print()
        
        if not has_conversion_instructions:
            print("❌ ISSUE: Currency conversion instructions are missing from the prompt!")
        if not has_calculation_example:
            print("❌ ISSUE: Specific calculation example is missing from the prompt!")
        
        print("Step 4: XML Data Analysis:")
        print(f"  MaxCredit: {test_report_data['xml_data']['Report']['PaymentsSection']['MaxCredit']}")
        print(f"  MaxCreditCurrency: {test_report_data['xml_data']['Report']['PaymentsSection']['MaxCreditCurrency']}")
        print(f"  Exchange Rate: {test_report_data['xml_data']['Report']['ExchangeRatesSection']['ExchangeRate'][0]['Rate']}")
        print(f"  Expected Conversion: 1,000,000 XPF × 0.0075 = £7,500 GBP")
        print(f"  Expected Classification: £7,500 < £50,000 = SMALL CREDIT")
        print()
        
    except Exception as e:
        print(f"❌ Error during prompt debugging: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_llm_prompt())
