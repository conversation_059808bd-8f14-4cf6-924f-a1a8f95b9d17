#!/usr/bin/env python3
"""
Debug the exact prompt being sent to LLM for real report 1981352.
"""

import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor
from app.models.schemas import Question

async def debug_llm_prompt_real():
    """Debug the exact prompt being sent to LLM for real report."""
    print("Debugging LLM Prompt for Real Report 1981352")
    print("=" * 60)
    
    file_processor = FileProcessor()
    validation_service = ValidationService()
    
    try:
        # Get the real report data
        report_data = await file_processor.get_processed_report_by_report_id("1981352")
        
        if not report_data:
            print("❌ Report 1981352 not found")
            return
        
        print("✅ Report 1981352 found")
        
        # Create the credit validation questions
        test_questions = [
            Question(
                id="test-credit-small-large",
                question="Mark as an issue if a small company is associated with a large credit amount.",
                category="company_credit_validation",
                client_code=None,
                darwin_reference_sections="(Company) Company Size, Number of Employees, Annual Turnover, (Credit) Max Credit, Credit Limit, (Financial) Credit Information",
                expected_outcome="rejected",
                client_specific_type="All"
            )
        ]
        
        print("Step 1: Getting XML content for LLM...")
        
        # Get the XML content exactly as the validation service would
        xml_data = report_data.get('xml_data', {})
        if xml_data:
            full_xml_content = validation_service._dict_to_xml_string(xml_data)
        else:
            xml_structure = report_data.get('xml_structure', {})
            full_xml_content = validation_service._dict_to_xml_string(xml_structure)
        
        # Apply optimization
        optimized_xml_content = validation_service._optimize_xml_content_for_speed(full_xml_content)
        
        print(f"Full XML length: {len(full_xml_content)} characters")
        print(f"Optimized XML length: {len(optimized_xml_content)} characters")
        print()
        
        # Check if PaymentsSection is in the optimized XML
        payments_section_start = optimized_xml_content.find('<PaymentsSection>')
        payments_section_end = optimized_xml_content.find('</PaymentsSection>')
        
        if payments_section_start != -1 and payments_section_end != -1:
            print("✅ PaymentsSection found in optimized XML")
            payments_section = optimized_xml_content[payments_section_start:payments_section_end + 18]
            print("PaymentsSection content:")
            print("-" * 40)
            print(payments_section)
            print("-" * 40)
            print()
            
            # Check for specific fields
            has_credit_opinion = 'CreditOpinion' in payments_section
            has_max_credit = 'MaxCredit' in payments_section
            has_max_credit_currency = 'MaxCreditCurrency' in payments_section
            has_small_opinion = 'Small' in payments_section
            has_1000000 = '1000000' in payments_section
            has_xpf = 'XPF' in payments_section
            
            print("PaymentsSection Analysis:")
            print(f"  CreditOpinion present: {'✅' if has_credit_opinion else '❌'}")
            print(f"  MaxCredit present: {'✅' if has_max_credit else '❌'}")
            print(f"  MaxCreditCurrency present: {'✅' if has_max_credit_currency else '❌'}")
            print(f"  'Small' value present: {'✅' if has_small_opinion else '❌'}")
            print(f"  '1000000' amount present: {'✅' if has_1000000 else '❌'}")
            print(f"  'XPF' currency present: {'✅' if has_xpf else '❌'}")
            print()
            
        else:
            print("❌ PaymentsSection NOT found in optimized XML!")
            print("This explains why LLM can't find credit information")
            
            # Check if it exists in the full XML
            full_payments_start = full_xml_content.find('<PaymentsSection>')
            if full_payments_start != -1:
                print(f"✅ PaymentsSection exists in full XML at position {full_payments_start}")
                if len(full_xml_content) > 10000:
                    print("❌ ISSUE: XML is being truncated and PaymentsSection is after position 10,000")
            else:
                print("❌ PaymentsSection not found in full XML either!")
        
        # Build the holistic prompt
        print("Step 2: Building holistic validation prompt...")
        holistic_prompt = validation_service._build_holistic_validation_prompt(
            test_questions, optimized_xml_content, {}, None
        )
        
        print(f"Prompt length: {len(holistic_prompt)} characters")
        print()
        
        # Check if the prompt contains the credit validation instructions
        prompt_lower = holistic_prompt.lower()
        has_credit_instructions = "company credit validation detected" in prompt_lower
        has_creditopinion_instructions = "creditopinion" in prompt_lower
        has_fallback_rates = "xpf to gbp: 0.007257" in prompt_lower
        
        print("Prompt Analysis:")
        print(f"  Credit validation instructions: {'✅' if has_credit_instructions else '❌'}")
        print(f"  CreditOpinion instructions: {'✅' if has_creditopinion_instructions else '❌'}")
        print(f"  Fallback exchange rates: {'✅' if has_fallback_rates else '❌'}")
        print()
        
        if not has_credit_instructions:
            print("❌ ISSUE: Credit validation instructions not detected in prompt!")
            
            # Check if the question type detection is working
            question_type = validation_service._detect_question_type(test_questions[0].question)
            print(f"Question type detected: {question_type}")
            
        # Show a sample of the prompt around the XML content
        xml_start_in_prompt = holistic_prompt.find('<Report>')
        if xml_start_in_prompt != -1:
            print("Step 3: XML content in prompt (first 1000 chars):")
            print("-" * 50)
            xml_sample = holistic_prompt[xml_start_in_prompt:xml_start_in_prompt + 1000]
            print(xml_sample)
            print("-" * 50)
        else:
            print("❌ No XML content found in prompt!")

        # Test the LLM directly with this prompt
        print("Step 4: Testing LLM directly with this prompt...")
        try:
            from langchain_openai import ChatOpenAI
            from langchain.schema import SystemMessage, HumanMessage
            from app.core.config import settings

            llm = ChatOpenAI(
                model=settings.OPENAI_MODEL,
                openai_api_key=settings.OPENAI_API_KEY,
                temperature=0,
                seed=42,
                max_retries=1,
                max_tokens=settings.MAX_TOKENS
            )

            messages = [
                SystemMessage(content="You are an expert XML report compliance validator. Follow all instructions exactly."),
                HumanMessage(content=holistic_prompt)
            ]

            print("Sending prompt to LLM...")
            response = await llm.ainvoke(messages)
            response_text = response.content

            print("LLM Response:")
            print("=" * 60)
            print(response_text)
            print("=" * 60)
            print()

            # Analyze the response
            response_lower = response_text.lower()
            mentions_credit_opinion = "creditopinion" in response_lower or "credit opinion" in response_lower
            mentions_small_company = "small company" in response_lower
            mentions_xpf = "xpf" in response_lower
            mentions_gbp = "gbp" in response_lower
            mentions_7257 = "7257" in response_text or "7,257" in response_text
            mentions_conversion = "×" in response_text or "*" in response_text or "convert" in response_lower

            print("LLM Response Analysis:")
            print(f"  Mentions CreditOpinion: {'✅' if mentions_credit_opinion else '❌'}")
            print(f"  Mentions small company: {'✅' if mentions_small_company else '❌'}")
            print(f"  Mentions XPF: {'✅' if mentions_xpf else '❌'}")
            print(f"  Mentions GBP: {'✅' if mentions_gbp else '❌'}")
            print(f"  Mentions 7257 conversion: {'✅' if mentions_7257 else '❌'}")
            print(f"  Shows conversion calculation: {'✅' if mentions_conversion else '❌'}")

        except Exception as llm_error:
            print(f"❌ Error testing LLM: {llm_error}")
        
    except Exception as e:
        print(f"❌ Error debugging prompt: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_llm_prompt_real())
