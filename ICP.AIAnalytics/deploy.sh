#!/bin/bash

# XML Report Validation System - Docker Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from template..."
        
        # Create basic .env file
        cat > .env << EOF
# OpenAI Configuration (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here

# Application Configuration
DEBUG=False
HOST=0.0.0.0
PORT=8000

# Vector Database Configuration
CHROMADB_MODE=server

# Performance Configuration
DEFAULT_BATCH_SIZE=10
MAX_TOKENS=4000
EOF
        
        print_warning "Please edit the .env file and set your OPENAI_API_KEY before running the application."
        print_warning "You can edit it with: nano .env"
        exit 1
    fi
    
    # Check if OpenAI API key is set
    if grep -q "your_openai_api_key_here" .env; then
        print_error "Please set your OpenAI API key in the .env file"
        print_warning "Edit the .env file and replace 'your_openai_api_key_here' with your actual API key"
        exit 1
    fi
    
    print_success ".env file is configured"
}

# Build and start the application
deploy_application() {
    print_status "Building and starting the XML Report Validation System..."
    
    # Stop existing containers if running
    if docker-compose ps | grep -q "xml-validation-api"; then
        print_status "Stopping existing containers..."
        docker-compose down
    fi
    
    # Build and start
    print_status "Building Docker image..."
    docker-compose build
    
    print_status "Starting services..."
    docker-compose up -d
    
    print_success "Application deployed successfully!"
}

# Check application health
check_health() {
    print_status "Checking application health..."
    
    # Wait for application to start
    sleep 15
    
    # Check if containers are running
    if ! docker-compose ps | grep -q "Up"; then
        print_error "Containers are not running. Check logs with: docker-compose logs"
        exit 1
    fi
    
    # Check ChromaDB health
    print_status "Checking ChromaDB server..."
    if curl -f http://localhost:8001/api/v1/heartbeat > /dev/null 2>&1; then
        print_success "ChromaDB server is healthy and responding"
    else
        print_warning "ChromaDB health check failed. Server might still be starting up."
    fi
    
    # Check application health endpoint
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Application is healthy and responding"
    else
        print_warning "Application health check failed. It might still be starting up."
        print_warning "Check logs with: docker-compose logs -f"
    fi
}

# Show deployment information
show_info() {
    print_success "Deployment completed!"
    echo
    echo "Application URLs:"
    echo "  - API: http://localhost:8000"
    echo "  - Documentation: http://localhost:8000/docs"
    echo "  - Health Check: http://localhost:8000/health"
    echo "  - ChromaDB Server: http://localhost:8001"
    echo
    echo "Useful commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop application: docker-compose down"
    echo "  - Restart application: docker-compose restart"
    echo "  - Access container: docker exec -it xml-validation-api bash"
    echo
}

# Main deployment function
main() {
    echo "XML Report Validation System - Docker Deployment"
    echo "================================================"
    echo
    
    check_docker
    check_env_file
    deploy_application
    check_health
    show_info
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        print_status "Stopping application..."
        docker-compose down
        print_success "Application stopped"
        ;;
    "restart")
        print_status "Restarting application..."
        docker-compose restart
        print_success "Application restarted"
        ;;
    "logs")
        print_status "Showing logs..."
        docker-compose logs -f
        ;;
    "status")
        print_status "Application status:"
        docker-compose ps
        ;;
    "clean")
        print_status "Cleaning up..."
        docker-compose down -v
        docker system prune -f
        print_success "Cleanup completed"
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|clean}"
        echo
        echo "Commands:"
        echo "  deploy   - Deploy the application (default)"
        echo "  stop     - Stop the application"
        echo "  restart  - Restart the application"
        echo "  logs     - Show application logs"
        echo "  status   - Show application status"
        echo "  clean    - Stop and clean up all data"
        exit 1
        ;;
esac 