#!/usr/bin/env python3
"""
Test script to verify that temporal validation is working with explicit current date instructions.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from app.models.schemas import Question

def test_temporal_validation_fix():
    """Test temporal validation with explicit current date instructions."""
    print("Testing Temporal Validation Fix with Explicit Current Date")
    print("=" * 60)
    
    validation_service = ValidationService()
    current_date = validation_service.get_current_date()
    print(f"Current date: {current_date}")
    print()
    
    # Create a temporal validation question
    temporal_question = Question(
        id="test-temporal",
        question="Check if any registration numbers have expired and require explanatory comments",
        category="temporal_validation",
        client_code=None,
        darwin_reference_sections="LegalStatusSection/RegistrationNumbers",
        expected_outcome="rejected",
        client_specific_type="All"
    )
    
    # Test XML with expired registration number
    expired_xml = """
    <Report>
        <LegalStatusSection>
            <RegistrationNumbers>
                <RegistrationNumber>12345678</RegistrationNumber>
                <DateExpired>2024-06-01</DateExpired>
            </RegistrationNumbers>
        </LegalStatusSection>
    </Report>
    """
    
    print("Testing temporal validation question detection:")
    is_temporal = validation_service._is_temporal_validation_question(temporal_question.question)
    print(f"Is temporal question: {is_temporal}")
    print()
    
    print("Testing enhanced validation prompt:")
    enhanced_prompt = validation_service._build_enhanced_validation_prompt(
        temporal_question, 
        expired_xml, 
        1, 
        retry=False, 
        focus_prompt=None
    )
    
    # Check for key temporal validation elements in prompt
    checks = [
        (f"TODAY'S DATE IS {current_date}", "Current date declaration"),
        ("TEMPORAL VALIDATION ALERT", "Temporal alert section"),
        ("EXPIRED (before", "Expiry comparison logic"),
        ("manual_intervention_needed", "Status rules"),
        ("DateExpired", "Field name guidance")
    ]
    
    print("Prompt validation checks:")
    for check_text, description in checks:
        if check_text in enhanced_prompt:
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    print()
    
    # Test holistic validation prompt
    print("Testing holistic validation prompt:")
    holistic_prompt = validation_service._build_holistic_validation_prompt(
        [temporal_question],
        expired_xml,
        {},
        focus_prompt=None
    )
    
    holistic_checks = [
        (f"TODAY'S DATE IS {current_date}", "Current date declaration"),
        ("TEMPORAL VALIDATION DETECTED", "Temporal detection"),
        ("EXPIRED!", "Expiry emphasis"),
        ("MANDATORY PROCESS", "Process instructions")
    ]
    
    print("Holistic prompt validation checks:")
    for check_text, description in holistic_checks:
        if check_text in holistic_prompt:
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    print()
    
    # Test with a non-temporal question to ensure it doesn't get temporal instructions
    non_temporal_question = Question(
        id="test-non-temporal",
        question="Check if company name matches the requested name",
        category="identity_validation",
        client_code=None,
        darwin_reference_sections="HeaderSection/CompanyName",
        expected_outcome="approved",
        client_specific_type="All"
    )
    
    print("Testing non-temporal question:")
    is_non_temporal = validation_service._is_temporal_validation_question(non_temporal_question.question)
    print(f"Is temporal question: {is_non_temporal}")
    
    non_temporal_prompt = validation_service._build_enhanced_validation_prompt(
        non_temporal_question, 
        expired_xml, 
        1, 
        retry=False, 
        focus_prompt=None
    )
    
    has_temporal_alert = "TEMPORAL VALIDATION ALERT" in non_temporal_prompt
    print(f"Has temporal alert (should be False): {has_temporal_alert}")
    
    print()
    print("=" * 60)
    print("Temporal validation fix test completed!")
    print()
    print("Key improvements:")
    print("- RAG retrieval is now skipped for faster processing")
    print("- Explicit current date instructions added to prompts")
    print("- Temporal validation detection improved")
    print("- Strong visual alerts added for temporal questions")
    print("- Clear step-by-step instructions for LLM")

if __name__ == "__main__":
    test_temporal_validation_fix()
