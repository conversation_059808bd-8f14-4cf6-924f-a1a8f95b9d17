#!/usr/bin/env python3
"""
Debug script to test Excel file processing
"""

import asyncio
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

async def test_excel_processing():
    """Test Excel file processing directly."""
    try:
        from app.services.file_processor import FileProcessor
        
        print("Testing Excel file processing...")
        
        # Create file processor
        processor = FileProcessor()
        
        # Read the Excel file directly using configuration
        import pandas as pd
        from app.core.config import settings
        df = pd.read_excel(settings.PERMANENT_QUESTION_BANK_PATH)
        
        print(f"Excel file loaded successfully")
        print(f"Columns: {list(df.columns)}")
        print(f"Shape: {df.shape}")
        print(f"First few rows:")
        print(df.head())
        
        # Test the column mapping logic
        print("\nTesting column mapping...")
        
        # Expected columns: question, category (optional), priority (optional), expected_format (optional), client_code (optional)
        required_columns = ["question"]
        optional_columns = ["category", "priority", "expected_format", "client_code"]
        
        # Check if required columns exist
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"Missing required columns: {missing_columns}")
            
            # Try common alternative column names with priority for descriptive content
            column_mapping = {
                "question": "question",
                "questions": "question",
                "question_text": "question",
                "text": "question",
                "validation details (questions)": "question",
                "validation details": "question",
                "validation question": "question",
                "validation": "question"
            }
            
            print(f"Available columns: {list(df.columns)}")
            print(f"Looking for question column...")
            
            # First try exact matches (case insensitive)
            for idx, col in enumerate(df.columns):
                col_lower = col.lower().strip()
                print(f"Checking column: '{col}' -> '{col_lower}'")
                if col_lower in column_mapping:
                    df = df.rename(columns={col: column_mapping[col_lower]})
                    print(f"✓ Mapped column '{col}' to 'question'")
                    break
            
            # Check again after mapping
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"Still missing required columns: {missing_columns}")
                # Use fallback logic
                best_column = None
                max_avg_length = 0
                
                for col in df.columns:
                    # Skip columns that clearly aren't questions
                    if col.lower().strip() in ['unnamed:', 'index', 'id', 'no', 'number']:
                        continue
                        
                    # Calculate average length of non-null text in this column
                    text_lengths = []
                    for value in df[col].dropna():
                        if isinstance(value, str) and value.strip():
                            text_lengths.append(len(value.strip()))
                    
                    if text_lengths:
                        avg_length = sum(text_lengths) / len(text_lengths)
                        if avg_length > max_avg_length:
                            max_avg_length = avg_length
                            best_column = col
                
                # Use the most descriptive column or fall back to first column
                if best_column and max_avg_length > 10:  # At least 10 characters on average
                    df = df.rename(columns={best_column: "question"})
                    print(f"Using column '{best_column}' as question column (avg length: {max_avg_length:.1f})")
                else:
                    # Use first column as fallback
                    df = df.rename(columns={df.columns[0]: "question"})
                    print(f"Using first column '{df.columns[0]}' as question column (fallback)")
            else:
                print("✓ Question column mapping successful")
        
        # Now test processing the questions
        print("\nTesting question processing...")
        
        questions = []
        for idx, row in df.iterrows():
            if pd.notna(row["question"]) and str(row["question"]).strip():
                from app.models.schemas import Question
                import uuid
                
                question = Question(
                    id=str(uuid.uuid4()),
                    question=str(row["question"]).strip(),
                    category=str(row.get("category", "")).strip() if pd.notna(row.get("category")) else None,
                    priority=str(row.get("priority", "medium")).strip() if pd.notna(row.get("priority")) else "medium",
                    expected_format=str(row.get("expected_format", "")).strip() if pd.notna(row.get("expected_format")) else None,
                    client_code=str(row.get("client_code", "")).strip().strip('"').strip("'") if pd.notna(row.get("client_code")) and str(row.get("client_code", "")).strip() else None
                )
                questions.append(question)
                print(f"Processed question {idx+1}: {question.question[:50]}...")
        
        print(f"\n✓ Successfully processed {len(questions)} questions")
        
    except Exception as e:
        import traceback
        print(f"ERROR: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_excel_processing())
    if success:
        print("\nExcel processing test passed!")
    else:
        print("\nExcel processing test failed!")
        sys.exit(1) 