#!/usr/bin/env python3
"""
Test script to verify the current date functionality for registration number expiry validation.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.validation_service import ValidationService
from datetime import datetime, timed<PERSON><PERSON>

def test_current_date_methods():
    """Test the current date methods in ValidationService."""
    print("Testing Current Date Methods for Registration Number Expiry Validation")
    print("=" * 70)
    
    # Initialize validation service
    validation_service = ValidationService()
    
    # Test get_current_date method
    current_date = validation_service.get_current_date()
    print(f"Current date (ISO format): {current_date}")
    
    # Test get_current_date_formatted method
    formats = ["iso", "uk", "us", "display"]
    for format_type in formats:
        formatted_date = validation_service.get_current_date_formatted(format_type)
        print(f"Current date ({format_type} format): {formatted_date}")
    
    print("\n" + "=" * 70)
    print("Testing Date Expiry Validation")
    print("=" * 70)
    
    # Test date expiry validation with various date formats
    test_dates = [
        "2024-12-31",  # Past date - should be expired
        "2025-12-31",  # Future date - should not be expired
        "31/12/2024",  # Past date UK format - should be expired
        "12/31/2025",  # Future date US format - should not be expired
        "31-Dec-2024", # Past date display format - should be expired
        "15-Aug-2025", # Future date display format - should not be expired
        "",            # Empty date - should not be expired
        "invalid-date", # Invalid format - should return error
    ]
    
    for test_date in test_dates:
        is_expired, parsed_date = validation_service.is_date_expired(test_date)
        status = "EXPIRED" if is_expired else "NOT EXPIRED"
        print(f"Date: '{test_date}' -> {status} (Parsed: {parsed_date})")
    
    print("\n" + "=" * 70)
    print("Testing Registration Number Validation")
    print("=" * 70)
    
    # Test registration number validation with sample XML data
    sample_xml_data = {
        "Report": {
            "LegalStatusSection": {
                "RegistrationNumbers": [
                    {
                        "RegistrationNumber": "12345678",
                        "DateExpired": "2024-11-27",  # Expired
                        "Comments": "Registration expired but renewal in progress"
                    },
                    {
                        "RegistrationNumber": "87654321",
                        "DateExpired": "2025-12-31",  # Not expired
                        "Comments": "Active registration"
                    },
                    {
                        "RegistrationNumber": "11111111",
                        "DateExpired": "2024-06-15",  # Expired
                        # No comments - should be rejected
                    },
                    {
                        "RegistrationNumber": "22222222",
                        # No expiry date - should not be considered expired
                        "Comments": "Permanent registration"
                    }
                ]
            }
        }
    }
    
    validation_result = validation_service.validate_registration_number_expiry(sample_xml_data)
    
    print(f"Validation Status: {validation_result['status']}")
    print(f"Summary: {validation_result['summary']}")
    print(f"Details: {validation_result['details']}")
    
    print("\n" + "=" * 70)
    print("Test completed successfully!")
    print("=" * 70)

if __name__ == "__main__":
    test_current_date_methods()
