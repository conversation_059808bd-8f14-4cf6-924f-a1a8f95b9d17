# RAG (Retrieval-Augmented Generation) Implementation

## Overview

The RAG implementation enhances validation accuracy by using a knowledge base of correct response examples to guide LLM responses. This system retrieves relevant examples and patterns during validation to improve consistency and quality.

## Architecture

### Components

1. **RAGService** (`app/services/rag_service.py`)
   - Manages the knowledge base of correct examples
   - <PERSON>les vector storage and retrieval
   - Extracts response patterns

2. **RAGPromptBuilder** (`app/services/rag_prompt_builder.py`)
   - Builds enhanced prompts with retrieved examples
   - Integrates RAG data with existing prompt features
   - Maintains prompt structure and quality

3. **RAG API** (`app/api/rag.py`)
   - Provides endpoints for knowledge base management
   - Supports initialization, testing, and monitoring

### Integration Points

- **ValidationService**: Enhanced to use RAG during LLM response generation
- **Vector Store**: Leverages existing ChromaDB infrastructure
- **Prompt System**: Seamlessly integrates with Darwin targeting and focus prompts

## API Endpoints

### Initialize Knowledge Base

**POST** `/api/v1/rag/initialize`

Initialize the RAG knowledge base with correct response examples.

```json
{
  "examples": [
    {
      "id": "example_1",
      "question": "Company name should match requested name",
      "summary": "COMPLIANT: CompanyName 'GenAI25' matches Requested 'GenAI25'",
      "status": "approved",
      "confidence_score": 0.95,
      "relevant_sections": ["Report/HeaderSection/CompanyName"],
      "reasoning": "Direct comparison shows exact match",
      "xml_context": "<CompanyName>GenAI25</CompanyName>"
    }
  ]
}
```

### Initialize from File

**POST** `/api/v1/rag/initialize-from-file`

Upload a JSON file containing correct examples.

```bash
curl -X POST "http://localhost:8000/api/v1/rag/initialize-from-file" \
  -F "file=@correct_validation_examples.json"
```

### Add Examples

**POST** `/api/v1/rag/add-examples`

Add additional examples to existing knowledge base.

### Get Status

**GET** `/api/v1/rag/status`

Get current RAG system status and statistics.

### Test Retrieval

**POST** `/api/v1/rag/test-retrieval`

Test RAG retrieval with sample question and XML content.

```json
{
  "question": "Company name should match requested name",
  "xml_content": "<CompanyName>Test Corp</CompanyName>"
}
```

### Reset Knowledge Base

**DELETE** `/api/v1/rag/reset`

Reset the knowledge base (use with caution).

## How RAG Works

### 1. Knowledge Base Creation

When examples are provided:
1. **Document Creation**: Each example is converted to a searchable document
2. **Embedding Generation**: OpenAI embeddings are created for semantic search
3. **Pattern Extraction**: Common response patterns are identified and stored
4. **Vector Storage**: Documents and patterns are stored in ChromaDB collections

### 2. Retrieval Process

During validation:
1. **Query Formation**: Question and XML content are combined into search query
2. **Similarity Search**: Vector store finds most relevant examples
3. **Pattern Matching**: Relevant response patterns are identified
4. **Relevance Filtering**: Only high-relevance results are included

### 3. Response Enhancement

Retrieved data enhances LLM prompts:
1. **Example Integration**: Relevant examples are added to prompt
2. **Pattern Guidance**: Response patterns provide style guidance
3. **Quality Standards**: Examples demonstrate expected quality levels
4. **Consistency**: Promotes consistent response formats

## Example Format

### Required Fields
- `question`: The validation rule or question
- `summary`: The correct response summary (max 150 chars)
- `status`: One of "approved", "rejected", "manual_intervention_needed"

### Optional Fields
- `id`: Unique identifier for the example
- `confidence_score`: Confidence level (0.0-1.0)
- `relevant_sections`: XML sections referenced
- `reasoning`: Explanation of the validation logic
- `xml_context`: Relevant XML snippet

### Example Structure

```json
{
  "id": "example_1",
  "question": "Company name should match requested name",
  "summary": "COMPLIANT: CompanyName 'GenAI25' matches Requested 'GenAI25'",
  "status": "approved",
  "confidence_score": 0.95,
  "relevant_sections": ["Report/HeaderSection/CompanyName"],
  "reasoning": "Direct comparison shows exact match between names",
  "xml_context": "<CompanyName>GenAI25</CompanyName><Requested>GenAI25</Requested>"
}
```

## Benefits

### Improved Accuracy
- **Consistent Responses**: Examples guide LLM to produce consistent outputs
- **Quality Standards**: Demonstrates expected response quality
- **Error Reduction**: Reduces hallucination and incorrect interpretations

### Enhanced Learning
- **Pattern Recognition**: System learns from correct examples
- **Context Awareness**: Better understanding of validation scenarios
- **Domain Knowledge**: Incorporates business-specific validation logic

### Scalability
- **Continuous Improvement**: Knowledge base grows with new examples
- **Feedback Integration**: Can incorporate validated responses
- **Domain Adaptation**: Easily adaptable to different validation domains

## Configuration

### Environment Variables

```bash
# OpenAI API for embeddings (required for semantic search)
OPENAI_API_KEY=your_openai_api_key

# ChromaDB configuration
CHROMADB_MODE=embedded  # or "server"
CHROMADB_PATH=./data/vector_db
```

### Collection Names
- `validation_examples`: Stores correct response examples
- `response_patterns`: Stores extracted response patterns

## Usage Examples

### 1. Initialize with Sample Data

```bash
# Initialize from provided examples file
curl -X POST "http://localhost:8000/api/v1/rag/initialize-from-file" \
  -F "file=@examples/correct_validation_examples.json"
```

### 2. Test Retrieval

```bash
curl -X POST "http://localhost:8000/api/v1/rag/test-retrieval" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "Company name should match requested name",
    "xml_content": "<CompanyName>Test Corp</CompanyName><Requested>Test Corp</Requested>"
  }'
```

### 3. Check Status

```bash
curl -X GET "http://localhost:8000/api/v1/rag/status"
```

### 4. Validate with RAG

```bash
# Regular validation now automatically uses RAG if initialized
curl -X POST "http://localhost:8000/api/v1/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "report_id": "your-report-id",
    "validation_options": {
      "focus_prompt": "Focus on company name compliance"
    }
  }'
```

## Best Practices

### Example Quality
- **Accurate Examples**: Ensure all examples are correct and verified
- **Diverse Scenarios**: Include examples covering different validation scenarios
- **Clear Reasoning**: Provide clear explanations for each validation decision
- **Consistent Format**: Maintain consistent response format and style

### Knowledge Base Management
- **Regular Updates**: Add new correct examples as they become available
- **Quality Control**: Review examples before adding to knowledge base
- **Performance Monitoring**: Monitor retrieval relevance and response quality
- **Backup Strategy**: Regularly backup the knowledge base

### Integration
- **Gradual Rollout**: Start with a small set of high-quality examples
- **A/B Testing**: Compare RAG-enhanced vs. standard responses
- **Feedback Loop**: Use validation results to improve the knowledge base
- **Monitoring**: Track accuracy improvements and system performance

## Troubleshooting

### Common Issues

1. **No Examples Retrieved**
   - Check if knowledge base is initialized
   - Verify OpenAI API key for embeddings
   - Test with simpler queries

2. **Low Relevance Scores**
   - Add more diverse examples
   - Improve example quality and detail
   - Check embedding generation

3. **Performance Issues**
   - Monitor vector store performance
   - Consider reducing retrieval count
   - Optimize example document size

### Monitoring

- Check `/api/v1/rag/status` for system health
- Monitor validation response quality
- Track retrieval relevance scores
- Review example usage patterns
