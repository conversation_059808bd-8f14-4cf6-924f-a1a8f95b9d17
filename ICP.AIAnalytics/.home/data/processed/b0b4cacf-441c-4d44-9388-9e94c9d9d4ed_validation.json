{"validation_id": "b0b4cacf-441c-4d44-9388-9e94c9d9d4ed", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181305", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "777cb446-8a91-4eb9-9005-6957dac26e51", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "18b48434-43ff-4ecd-85e6-b5214d527d1b", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "4974e786-a662-411f-a1b4-e7065f91ae7a", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'Example Client' present, comments and payment details populated", "confidence_score": 0.95, "relevant_sections": ["OrderDetails/ClientName", "ClientSpecificComments/Comments", "PaymentSection/PaymentDetails"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4f58de32-ec6d-480a-92be-c95a1fe56c0a", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name empty, Comments and Notes also empty, compliant", "confidence_score": 0.95, "relevant_sections": ["OrderDetails/ClientName", "ClientSpecificCommentsToBeIncludedInTheReport/Comments", "Payment/Notes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5662bc6d-01e1-4d15-8b10-07e877bbdf86", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income; data missing in XML", "confidence_score": 0.9, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "15574b55-cfa9-474b-94b3-9964d71441d5", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials in 'Operating Costs', violates Direct Costs rule", "confidence_score": 0.95, "relevant_sections": ["Report/RelatedEntitiesSection/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8c36cf56-594a-42e9-b868-1b1996e9eb5f", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'RE test 2' does not match requested 'test', comments missing", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "383e9aa0-0636-4727-8dd8-9644aea410a8", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address 'test' matches active address, comments not needed", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address", "Report/SpecialNotes/TextForSpecificClients"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "13c19a0c-3383-46d1-9b8e-c9d49422e541", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special note for EUR, USD, GBP request", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/Requested", "Report/RelatedEntitiesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "658feef8-6e5d-435b-b0ee-2bc99b4cb093", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License numbers missing in Legal or Special Notes sections", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/RegistrationNumbers", "Report/SpecialNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b16add5c-9962-4a38-8193-3acda7e96306", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1015. Please try again in 2.03s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "26c92a2e-565a-4709-9725-be36f1a1d27b", "question_number": 12, "question": "Spelling Check", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29782, Requested 1219. Please try again in 2.002s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d6111aa0-530d-40f5-b0e2-5b77b13f5505", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements in significant changes; payments lack reference.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges/ChangeClassifications", "Report/Payments/CreditOpinionNotes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f983a49d-f0b3-44b8-9b55-01b02bc462f3", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1109. Please try again in 2.218s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "********-fb4a-4ea7-bab8-1fe5d83c6e8e", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 958. Please try again in 1.916s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "3814d490-afa0-4bd7-87ca-fd68efa78219", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1140. Please try again in 2.28s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "41482cd6-a581-4616-ae60-8141c71aff84", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27792, Requested 2878. Please try again in 1.34s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "26a647fa-8b60-4db0-9e28-5e3d15ae547b", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28118, Requested 2945. Please try again in 2.126s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d78a4f30-3cb6-4e11-aae1-dc05ff63330f", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1028. Please try again in 2.056s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "1a84fc4a-fb5e-4960-bf5f-4649585e5da0", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1126. Please try again in 2.252s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "2c8b329d-94fd-4067-9b41-56760652b108", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1695. Please try again in 3.39s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f5ea437f-3fc3-4181-ab78-17205d8d6c5a", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1439. Please try again in 2.878s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b303c30b-4351-4eae-981b-ba6e198036b6", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1673. Please try again in 3.346s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "1b856e52-09e7-42e9-bc25-1a60d1ee1f6e", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1045. Please try again in 2.09s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "27ba44ba-fc47-4f74-b948-f3e882d66448", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1064. Please try again in 2.128s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4070b93c-5239-462a-be31-b90dfd1aa43c", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1062. Please try again in 2.124s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "1c3cfbb5-281e-4c32-9e46-c3d21f2cbced", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2973. Please try again in 5.946s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "7c3e0bf3-2198-4c86-b85d-9e848664932f", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1039. Please try again in 2.078s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "6247ae63-aa4d-4aa6-a6d5-171236a61130", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2990. Please try again in 5.98s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "9951f398-d165-4efb-959e-cc305d932de7", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2984. Please try again in 5.968s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "9460af01-f888-4235-af50-670337e6af8e", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28120, Requested 2916. Please try again in 2.072s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "795458b6-0362-49ee-882b-80510ea22a88", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27931, Requested 2956. Please try again in 1.774s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "8aa74315-a9c4-40f7-93f8-d974e8463f68", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "8ebc0aa5-88ab-4368-8892-3a71eca35d3a", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29774, Requested 2927. Please try again in 5.402s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "82f3c857-256f-4139-9d3a-43af4243b966", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type 'Select' but no contact details found in AddressesSection", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c487d6d1-5dbc-47cf-8a69-db82ca4c47dc", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28158, Requested 2909. Please try again in 2.134s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "112dc357-eb8f-43e4-83c5-d33b20d4f757", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholders populated despite group membership, violates rule", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Shareholders", "Report/RelatedEntitiesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e9482f01-4c7d-4fb8-bd34-60fb12a17a81", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28148, Requested 2901. Please try again in 2.098s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "15b4a679-547b-483d-a7d5-dda2d693c313", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'RE test 2' does not match 'Angular UAT Testing' in financials", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheetFigures/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d6a5cdc4-04e1-4560-b5b4-0139d7a74a9e", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27819, Requested 2922. Please try again in 1.482s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "15776c32-b242-4bb4-9cc8-0621f0dc39d3", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status 'Active' complies with known status requirement", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 15:57:00.293730", "processing_time": 104.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}